import { useContext } from 'react';
import { ApigeeContext } from '../../context/ApigeeContext';
import { EntryPointResult } from '../responseEntities/AuthorizationEntities';

export const useLegacyPodsReadyService = () => {
  const axiosInstance = useContext(ApigeeContext);

  async function authorizationEntrypoint(): Promise<EntryPointResult> {
    const res = await axiosInstance.get('v1/legacy/pods-ready/entrypoint');

    return res.data;
  }

  return {
    authorizationEntrypoint
  };
};
