import { useQuery } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { LegacyQueryCacheKeys } from '../../QueryCacheKeys';

export const useLegacyAuthorizationEntrypoint = () => {
  const { authorizationEntrypoint } = useLegacyMyPodsService();

  const queryResult = useQuery({
    queryKey: [LegacyQueryCacheKeys.LEGACY_ENTRYPOINT_KEY],
    queryFn: () => authorizationEntrypoint()
  });

  return {
    ...queryResult
  };
};
