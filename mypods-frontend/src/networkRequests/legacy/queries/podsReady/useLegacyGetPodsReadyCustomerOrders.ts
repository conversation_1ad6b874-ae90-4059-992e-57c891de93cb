import { useSuspenseQuery } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../../LegacyMyPodsService';
import { LegacyQueryCacheKeys } from '../../../QueryCacheKeys';
import { orderApiToDomain } from '../../../responseEntities/OrderAPIEntities';

export const useLegacyGetPodsReadyCustomerOrders = () => {
  const { getPodsReadyOrders } = useLegacyMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [LegacyQueryCacheKeys.LEGACY_CUSTOMER_ORDERS_CACHE_KEY],
    queryFn: () => getPodsReadyOrders()
  });

  return {
    ...queryResult,
    customerOrders: queryResult.data?.map((order) => orderApiToDomain(order))
  };
};
