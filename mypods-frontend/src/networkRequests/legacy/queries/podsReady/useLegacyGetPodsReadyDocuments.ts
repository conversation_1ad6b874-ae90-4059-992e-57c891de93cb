import { useQuery } from '@tanstack/react-query';

import { documentApiToDomain } from '../../../responseEntities/DocumentApiEntities';
import { setDocumentToken } from '../../../../helpers/storageHelpers';
import { useLegacyMyPodsService } from '../../LegacyMyPodsService';
import { LegacyQueryCacheKeys } from '../../../QueryCacheKeys';

export const useLegacyGetPodsReadyDocuments = () => {
  const { getPodsReadyDocuments } = useLegacyMyPodsService();

  const queryResult = useQuery({
    queryKey: [LegacyQueryCacheKeys.LEGACY_DOCUMENTS_KEY],
    queryFn: () => getPodsReadyDocuments()
  });

  if (queryResult.data?.token) {
    setDocumentToken(queryResult.data.token);
  }

  return {
    ...queryResult,
    documents: queryResult.data?.documents?.map((document) => documentApiToDomain(document))
  };
};
