import { useQueryClient, useSuspenseQuery } from '@tanstack/react-query';
import { Customer } from '../../responseEntities/CustomerEntities';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { LegacyQueryCacheKeys } from '../../QueryCacheKeys';

export const useLegacyGetCustomer = () => {
  const { getCustomer } = useLegacyMyPodsService();
  const queryClient = useQueryClient();

  const queryResult = useSuspenseQuery({
    queryKey: [LegacyQueryCacheKeys.LEGACY_CUSTOMER_CACHE_KEY],
    queryFn: () => getCustomer()
  });

  return {
    ...queryResult,
    customer: queryResult.data,
    // because our backend doesn't return updated objects after mutations
    // we take the local state and update our cache manually.
    updateCustomer: (updatedCustomer: Partial<Customer>) => {
      queryClient.setQueryData([LegacyQueryCacheKeys.LEGACY_CUSTOMER_CACHE_KEY], {
        ...queryResult.data,
        ...updatedCustomer
      });
    }
  };
};
