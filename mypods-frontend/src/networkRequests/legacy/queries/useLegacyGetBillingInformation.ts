import { useSuspenseQuery } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { setBillingDocumentToken } from '../../../helpers/storageHelpers';
import { LegacyQueryCacheKeys } from '../../QueryCacheKeys';

export const useLegacyGetBillingInformation = () => {
  const { getBillingInformation } = useLegacyMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [LegacyQueryCacheKeys.LEGACY_BILLING_INFORMATION_KEY],
    queryFn: () => getBillingInformation()
  });
  if (queryResult.data?.token) {
    setBillingDocumentToken(queryResult.data.token);
  }

  return {
    ...queryResult,
    billingInformation: queryResult.data?.billingInformation
  };
};
