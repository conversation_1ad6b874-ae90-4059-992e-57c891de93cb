import { useSuspenseQuery } from '@tanstack/react-query';
import { orderApiToDomain } from '../../responseEntities/OrderAPIEntities';
import { LegacyQueryCacheKeys } from '../../QueryCacheKeys';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';

export const useLegacyGetCustomerOrders = () => {
  const { getCustomerOrders } = useLegacyMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [LegacyQueryCacheKeys.LEGACY_CUSTOMER_ORDERS_CACHE_KEY],
    queryFn: () => getCustomerOrders()
  });

  return {
    ...queryResult,
    customerOrders: queryResult.data?.map((order) => orderApiToDomain(order))
  };
};
