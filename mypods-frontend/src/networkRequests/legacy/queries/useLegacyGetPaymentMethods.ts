import { useSuspenseQuery } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { paymentMethodApiToDomain } from '../../responseEntities/PaymentEntities';
import { LegacyQueryCacheKeys } from '../../QueryCacheKeys';

export const useLegacyGetPaymentMethods = () => {
  const { getPaymentMethods } = useLegacyMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [LegacyQueryCacheKeys.LEGACY_PAYMENT_METHODS_KEY],
    queryFn: () => getPaymentMethods()
  });

  return {
    ...queryResult,
    paymentMethods:
      queryResult.data?.map((paymentMethod) => paymentMethodApiToDomain(paymentMethod)) ?? []
  };
};
