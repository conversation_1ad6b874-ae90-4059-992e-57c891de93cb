import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { UpdatePinRequest } from '../../responseEntities/CustomerEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyUpdatePin = () => {
  const { updatePin } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_PIN_KEY],
    mutationFn: (request: UpdatePinRequest) => updatePin(request)
  });

  return {
    ...mutationResult,
    customer: mutationResult.data
  };
};
