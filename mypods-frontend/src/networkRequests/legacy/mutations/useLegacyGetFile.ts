import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyGetFile = () => {
  const { getFile } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.GET_FILE_KEY],
    mutationFn: ({
      documentId,
      isBillingDocument
    }: {
      documentId: string;
      isBillingDocument: boolean;
    }) => getFile(documentId, isBillingDocument)
  });

  return {
    ...mutationResult
  };
};
