import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyAcceptInitialDeliveryPlacement = () => {
  const { acceptInitialDeliveryPlacement } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.ACCEPT_INITIAL_DELIVERY_PLACEMENT_KEY],
    mutationFn: async (orderId: string) => acceptInitialDeliveryPlacement(orderId)
  });

  return {
    ...mutationResult
  };
};
