import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { SetDefaultPaymentMethodRequest } from '../../responseEntities/PaymentEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacySetDefaultPaymentMethod = () => {
  const { setDefaultPaymentMethod } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.SET_DEFAULT_PAYMENT_METHOD_KEY],
    mutationFn: (request: SetDefaultPaymentMethodRequest) => setDefaultPaymentMethod(request)
  });

  return {
    ...mutationResult
  };
};
