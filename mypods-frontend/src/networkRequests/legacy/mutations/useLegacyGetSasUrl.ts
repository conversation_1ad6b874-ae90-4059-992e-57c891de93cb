import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyGetSasUrl = () => {
  const { getSasUrl } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.GET_SAS_URL_KEY],
    mutationFn: ({ docRef, isBillingDocument }: { docRef: string; isBillingDocument: boolean }) =>
      getSasUrl(docRef, isBillingDocument)
  });

  return {
    ...mutationResult
  };
};
