import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { AddPaymentMethodRequest } from '../../responseEntities/PaymentEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyAddPaymentMethod = () => {
  const { addPaymentMethod } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.ADD_PAYMENT_METHOD_KEY],
    mutationFn: (request: AddPaymentMethodRequest) => addPaymentMethod(request)
  });

  return {
    ...mutationResult
  };
};
