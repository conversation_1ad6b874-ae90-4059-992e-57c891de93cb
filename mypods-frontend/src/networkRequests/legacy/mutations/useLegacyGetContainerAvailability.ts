import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { MutationCacheKeys } from '../../MutationCacheKeys';
import { ContainerAvailabilityRequest } from '../../responseEntities/AvailabilityAPIEntities';

export const useLegacyGetContainerAvailability = () => {
  const { getContainerAvailability } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.CONTAINER_AVAILABILITY_KEY],
    mutationFn: (request: ContainerAvailabilityRequest) => getContainerAvailability(request)
  });

  return {
    ...mutationResult
  };
};
