import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { UpdateSmsOptInRequest } from '../../responseEntities/CustomerEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyUpdateSmsOptIn = () => {
  const { updateSmsOptIn } = useLegacyMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_SMS_OPT_IN_KEY],
    mutationFn: (request: UpdateSmsOptInRequest) => updateSmsOptIn(request)
  });

  return { ...mutationResult };
};
