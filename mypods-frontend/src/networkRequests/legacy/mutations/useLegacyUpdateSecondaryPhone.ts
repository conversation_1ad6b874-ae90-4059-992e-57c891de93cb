import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { UpdateSecondaryPhoneRequest } from '../../responseEntities/CustomerEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyUpdateSecondaryPhone = () => {
  const { updateSecondaryPhone } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_SECONDARY_PHONE_KEY],
    mutationFn: (request: UpdateSecondaryPhoneRequest) => updateSecondaryPhone(request)
  });

  return {
    ...mutationResult
  };
};
