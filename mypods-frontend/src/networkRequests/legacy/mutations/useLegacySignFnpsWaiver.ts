import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { MutationCacheKeys } from '../../MutationCacheKeys';
import { SignFnpsWaiverRequest } from '../../responseEntities/DocumentApiEntities';

export const useLegacySignFnpsWaiver = () => {
  const { signFnpsWaiver } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.SIGN_FNPS_WAIVER_KEY],
    mutationFn: (request: SignFnpsWaiverRequest) => signFnpsWaiver(request)
  });

  return { ...mutationResult };
};
