import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { UpdateShippingAddressRequest } from '../../responseEntities/CustomerEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyUpdateShippingAddress = () => {
  const { updateShippingAddress } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_SHIPPING_ADDRESS_KEY],
    mutationFn: (request: UpdateShippingAddressRequest) => updateShippingAddress(request)
  });

  return {
    ...mutationResult
  };
};
