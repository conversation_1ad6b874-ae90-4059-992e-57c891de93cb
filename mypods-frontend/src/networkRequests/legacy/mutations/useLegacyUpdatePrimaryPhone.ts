import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { UpdatePrimaryPhoneRequest } from '../../responseEntities/CustomerEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyUpdatePrimaryPhone = () => {
  const { updatePrimaryPhone } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_PRIMARY_PHONE_KEY],
    mutationFn: (request: UpdatePrimaryPhoneRequest) => updatePrimaryPhone(request)
  });

  return {
    ...mutationResult
  };
};
