import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyGetRentalAgreement = () => {
  const { getRentalAgreement } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.GET_RENTAL_AGREEMENT_KEY],
    mutationFn: (companyCode: string) => getRentalAgreement(companyCode)
  });

  return {
    ...mutationResult
  };
};
