import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { MutationCacheKeys } from '../../MutationCacheKeys';
import { CustomStatementRequest } from '../../responseEntities/BillingEntities';

export const useLegacyCreateCustomStatement = () => {
  const { createCustomStatement } = useLegacyMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.CREATE_CUSTOM_STATEMENT_KEY],
    mutationFn: (request: CustomStatementRequest) => createCustomStatement(request)
  });

  return { ...mutationResult };
};
