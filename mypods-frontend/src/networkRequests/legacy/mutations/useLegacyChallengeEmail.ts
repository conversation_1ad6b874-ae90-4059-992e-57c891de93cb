import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { ChallengeEmailRequest } from '../../responseEntities/CustomerEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyChallengeEmail = () => {
  const { challengeEmail } = useLegacyMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.CHALLENGE_EMAIL_KEY],
    mutationFn: (request: ChallengeEmailRequest) => challengeEmail(request)
  });

  return { ...mutationResult };
};
