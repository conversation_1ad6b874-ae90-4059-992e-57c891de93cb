import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { SameServiceAreaRequest } from '../../../domain/OrderEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyIsSameServiceArea = () => {
  const { isSameServiceArea } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.IS_SAME_SERVICE_AREA_KEY],
    mutationFn: (request: SameServiceAreaRequest) => isSameServiceArea(request)
  });

  return {
    ...mutationResult
  };
};
