import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { PayInvoicesRequest } from '../../responseEntities/PaymentEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyMakePayment = () => {
  const { payInvoices } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.MAKE_PAYMENT_KEY],
    mutationFn: (request: PayInvoicesRequest) => payInvoices(request)
  });

  return {
    ...mutationResult
  };
};
