import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { UpdatePasswordRequest } from '../../responseEntities/CustomerEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyUpdatePassword = () => {
  const { updatePassword } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_PASSWORD_KEY],
    mutationFn: (request: UpdatePasswordRequest) => updatePassword(request)
  });

  return {
    ...mutationResult
  };
};
