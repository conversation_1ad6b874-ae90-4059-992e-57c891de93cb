import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { UpdateBillingAddressRequest } from '../../responseEntities/CustomerEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyUpdateBillingAddress = () => {
  const { updateBillingAddress } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_BILLING_ADDRESS_KEY],
    mutationFn: (request: UpdateBillingAddressRequest) => updateBillingAddress(request)
  });

  return {
    ...mutationResult
  };
};
