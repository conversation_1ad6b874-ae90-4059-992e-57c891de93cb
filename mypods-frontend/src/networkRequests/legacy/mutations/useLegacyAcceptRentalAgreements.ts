import { useMutation } from '@tanstack/react-query';
import { MutationCacheKeys } from '../../MutationCacheKeys';
import { AcceptRentalAgreementRequest } from '../../responseEntities/DocumentApiEntities';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { useEntryPointContext } from '../../../context/EntryPointContext';

export const useLegacyAcceptRentalAgreements = () => {
  const { acceptRentalAgreements } = useLegacyMyPodsService();
  const { entryPointResult } = useEntryPointContext();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.ACCEPT_RA_KEY],
    mutationFn: (requests: AcceptRentalAgreementRequest[]) => {
      const promise = acceptRentalAgreements(requests);
      promise?.then(() => {
        const acceptedOrderIds = requests.map((request) => request.orderId);
        entryPointResult.outstandingRentalAgreements =
          entryPointResult.outstandingRentalAgreements.filter(
            (rentalAgreement) => !acceptedOrderIds.includes(rentalAgreement.orderId)
          );
      });
      return promise;
    }
  });

  return { ...mutationResult };
};
