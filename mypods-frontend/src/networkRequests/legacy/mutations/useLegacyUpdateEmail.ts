import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { UpdateEmailRequest } from '../../responseEntities/CustomerEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyUpdateEmail = () => {
  const { updateEmail } = useLegacyMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_EMAIL_KEY],
    mutationFn: (request: UpdateEmailRequest) => updateEmail(request)
  });

  return { ...mutationResult };
};
