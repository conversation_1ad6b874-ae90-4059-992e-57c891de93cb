import { useMutation, useQueryClient } from '@tanstack/react-query';
import { MutationCacheKeys } from '../../MutationCacheKeys';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { LegacyQueryCacheKeys } from '../../QueryCacheKeys';

export const useLegacyAcceptRentalAgreement = () => {
  const { acceptRentalAgreement } = useLegacyMyPodsService();
  const queryClient = useQueryClient();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.ACCEPT_MULTIPLE_RA_KEY],
    mutationFn: acceptRentalAgreement,
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: [LegacyQueryCacheKeys.LEGACY_ENTRYPOINT_KEY]
      });
    }
  });

  return { ...mutationResult };
};
