import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { VerifyChallengeRequest } from '../../responseEntities/CustomerEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyVerifyChallenge = () => {
  const { verifyChallenge } = useLegacyMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.VERIFY_CHALLENGE_KEY],
    mutationFn: (request: VerifyChallengeRequest) => verifyChallenge(request)
  });

  return { ...mutationResult };
};
