import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { PodsReadyCreateAccountRequest } from '../../responseEntities/PodsReadyEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyCreatePodsReadyAccount = () => {
  const { createPodsReadyAccount } = useLegacyMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.VERIFY_CHALLENGE_KEY],
    mutationFn: (request: PodsReadyCreateAccountRequest) => createPodsReadyAccount(request)
  });

  return { ...mutationResult };
};
