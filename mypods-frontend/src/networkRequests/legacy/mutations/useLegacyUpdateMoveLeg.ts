import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { UpdateMoveLegRequest } from '../../../domain/OrderEntities';
import { MutationCacheKeys } from '../../MutationCacheKeys';

export const useLegacyUpdateMoveLeg = () => {
  const { updateMoveLeg } = useLegacyMyPodsService();
  const transformMoveLegResponse = async (request: UpdateMoveLegRequest) => {
    const result = await updateMoveLeg(request);
    return { ...result, quoteId: result.quoteId?.toString() };
  };
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_MOVE_LEG_KEY],
    mutationFn: transformMoveLegResponse
  });

  return {
    ...mutationResult
  };
};
