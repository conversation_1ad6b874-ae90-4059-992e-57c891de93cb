import { useMutation } from '@tanstack/react-query';
import { useLegacyMyPodsService } from '../LegacyMyPodsService';
import { MutationCacheKeys } from '../../MutationCacheKeys';
import { SignMothAgreementRequest } from '../../responseEntities/DocumentApiEntities';

export const useLegacySignMothAgreement = () => {
  const { signMothAgreement } = useLegacyMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.SIGN_MOTH_AGREEMENT_KEY],
    mutationFn: (request: SignMothAgreementRequest) => signMothAgreement(request)
  });

  return { ...mutationResult };
};
