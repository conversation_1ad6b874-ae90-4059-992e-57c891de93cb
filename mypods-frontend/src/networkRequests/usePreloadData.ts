import { useQueryClient } from '@tanstack/react-query';
import { useMyPodsService } from './MyPodsService';
import { QueryCacheKeys } from './QueryCacheKeys';

export const usePreloadData = () => {
  const queryClient = useQueryClient();
  const { getBillingInformation, getCustomerDocuments, getCustomerOrders } = useMyPodsService();

  queryClient
    .prefetchQuery({
      queryKey: [QueryCacheKeys.BILLING_INFORMATION_CACHE_KEY],
      queryFn: () => getBillingInformation()
    })
    .then(() => {});

  queryClient
    .prefetchQuery({
      queryKey: [QueryCacheKeys.CUSTOMER_DOCUMENTS_KEY],
      queryFn: () => getCustomerDocuments()
    })
    .then(() => {});

  queryClient
    .prefetchQuery({
      queryKey: [QueryCacheKeys.CUSTOMER_ORDERS_CACHE_KEY],
      queryFn: () => getCustomerOrders()
    })
    .then(() => {});
};
