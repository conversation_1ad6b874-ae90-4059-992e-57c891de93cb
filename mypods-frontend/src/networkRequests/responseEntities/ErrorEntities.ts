export interface ErrorResponse {
  message: string;
  status: string;
}

export type AuthorizationErrorStatus =
  | 'UNAUTHORIZED'
  | 'FORBIDDEN'
  | 'MULTI_CUID'
  | 'UNEXPECTED_AUTH_FAILURE';

export type CustomerAccountsLoginStatus =
  | 'SESSION_EXPIRED'
  | 'AUTH_ERROR'
  | 'LOCKED_ACCOUNT'
  | 'MAINTENANCE'
  | 'INVALID_PODS_READY_TOKEN';

export type GenericErrorStatus =
  | 'INVALID_INPUT'
  | 'ERROR'
  | 'NOT_FOUND'
  | 'FORBIDDEN'
  | 'UNAUTHORIZED';
