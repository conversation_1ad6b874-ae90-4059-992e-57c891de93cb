export const emptyUseQueryResult = {
  data: [],
  dataUpdatedAt: 0,
  error: null,
  errorUpdatedAt: 0,
  errorUpdateCount: 0,
  failureCount: 0,
  failureReason: null,
  isError: false,
  isFetched: false,
  isFetchedAfterMount: false,
  isFetching: false,
  isPending: false,
  isLoading: false,
  isLoadingError: false,
  isInitialLoading: false,
  isPaused: false,
  isPlaceholderData: false,
  isRefetchError: false,
  isRefetching: false,
  isStale: false,
  isSuccess: true,
  refetch: () => [],
  status: 'idle',
  fetchStatus: 'idle'
};
