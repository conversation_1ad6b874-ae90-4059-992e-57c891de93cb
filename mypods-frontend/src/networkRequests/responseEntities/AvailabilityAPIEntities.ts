import { MoveLegType } from '../../domain/OrderEntities';

export interface ContainerAvailabilityRequest {
  date: string;
  postalCode: string;
  orderType: string;
  moveLegType: MoveLegType;
  siteIdentity: string;
}

export interface ContainerAvailabilityResponse {
  eightFootAvailability: ContainerAvailability[];
  twelveFootAvailability: ContainerAvailability[];
  sixteenFootAvailability: ContainerAvailability[];
}

export interface ContainerAvailability {
  date: string;
  isAvailable: boolean;
}
