import { vi } from 'vitest';
import {
  EntryPointResult,
  findIfEntryPointHasMothForm,
  OutstandingMothAgreement,
  OutstandingRentalAgreement
} from '../AuthorizationEntities';
import {
  createEntryPointResult,
  createOutstandingMothAgreement,
  createOutstandingRentalAgreement
} from '../../../testUtils/MyPodsFactories';

const mockOrderHasSignedMothAgreement = vi.hoisted(() => vi.fn());

vi.mock('../../../helpers/storageHelpers', () => ({
  orderHasSignedMothAgreement: mockOrderHasSignedMothAgreement
}));

describe('findIfEntryPointHasMothForm with', () => {
  let outstandingRentalAgreements: OutstandingRentalAgreement[] = [];
  let outstandingMothAgreements: OutstandingMothAgreement[] = [];
  const hasSignedMothAgreement = true;
  const orderId = '12343';

  const createTestEntryPoint = (): EntryPointResult =>
    createEntryPointResult({
      outstandingRentalAgreements,
      outstandingMothAgreements
    });

  beforeEach(() => {
    mockOrderHasSignedMothAgreement.mockReturnValue(hasSignedMothAgreement);
  });

  describe('no outstandingRentalAgreements', () => {
    beforeEach(() => {
      outstandingRentalAgreements = [];
    });

    describe('and no outstandingMothAgreements', () => {
      beforeEach(() => {
        outstandingMothAgreements = [];
      });

      it('should return hasSignedMothAgreement', () => {
        const actual = findIfEntryPointHasMothForm(createTestEntryPoint(), orderId);

        expect(actual).toBe(hasSignedMothAgreement);
      });
    });

    describe('and outstandingMothAgreements', () => {
      beforeEach(() => {
        outstandingMothAgreements = [createOutstandingMothAgreement()];
      });

      it('should return true', () => {
        const actual = findIfEntryPointHasMothForm(createTestEntryPoint(), orderId);

        expect(actual).toBe(true);
      });
    });
  });

  describe('outstandingRentalAgreements', () => {
    beforeEach(() => {
      outstandingRentalAgreements = [createOutstandingRentalAgreement()];
    });

    describe('and outstandingMothAgreements', () => {
      beforeEach(() => {
        outstandingMothAgreements = [createOutstandingMothAgreement()];
      });

      it('should return true', () => {
        const actual = findIfEntryPointHasMothForm(createTestEntryPoint(), orderId);

        expect(actual).toBe(true);
      });
    });

    describe('and no outstandingMothAgreements', () => {
      beforeEach(() => {
        outstandingMothAgreements = [];
      });

      it('should return false when orderHasSignedMothAgreement returns false', () => {
        mockOrderHasSignedMothAgreement.mockReturnValue(false);

        const actual = findIfEntryPointHasMothForm(createTestEntryPoint(), orderId);

        expect(actual).toBe(false);
      });

      it('should return true when orderHasSignedMothAgreement returns true', () => {
        mockOrderHasSignedMothAgreement.mockReturnValue(true);

        const actual = findIfEntryPointHasMothForm(createTestEntryPoint(), orderId);

        expect(actual).toBe(true);
      });
    });
  });
});
