import { format } from 'date-fns';

export interface MonthlyBillingStatement {
  childCustomerAccount?: string;
  commercialInvoiceStatementDate?: string;
  commercialInvoiceStatementDesc?: string;
  commercialInvoiceStatementId?: string;
  createdBy?: string;
  createdDateTime?: string;
  docuRefIdentity?: number;
  fileName?: string;
  filePathAndName?: string;
  fileType?: string;
  invoiceType?: string;
  parentCustomerAccount?: string;
  path?: string;
}

export type BillingInvoice = {
  orderId: string;
  documentId: string;
  documentType?: string;
  documentDescription?: string;
  invoiceNumber?: string;
  hasAttachment: boolean;
  documentStatus: number;
  currencyType?: string;
  totalDue: number;
  amountPaid: number;
  balanceDue: number;
  isPaid: boolean;
  dueDate?: string;
  companyCode?: string;
  isInternal: boolean;
  isPoet: boolean;
};

export interface BillingResponse {
  billingInformation: BillingInformation;
  token: string;
}
export interface BillingInformation {
  invoices: BillingInvoice[];
  monthlyStatements: MonthlyBillingStatement[];
  totalBalance: number;
}

export interface CustomStatementRequest {
  startDate: string;
  endDate: string;
}

export interface CustomStatement {
  content: string;
  dateRange: string;
}

export const formatCustomRange = (startDate: string, endDate: string): string => {
  const start = format(new Date(startDate.replace(/-/g, '/')), 'MM/yy');
  const end = format(new Date(endDate.replace(/-/g, '/')), 'MM/yy');
  return `${start} - ${end}`;
};

export const formatByCurrency = (balanceDue: number, currencyType?: string) => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currencyType ?? 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
  return formatter.format(balanceDue);
};
