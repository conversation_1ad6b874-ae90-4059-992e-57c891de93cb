import { toTitleCase } from '../../helpers/stringHelper';

export interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  customerType?: CustomerType;
  email?: Email;
  primaryPhone?: Phone;
  secondaryPhone?: Phone;
  billingAddress?: CustomerAddress;
  shippingAddress?: CustomerAddress;
  isConverted?: boolean;
  smsOptIn?: boolean;
  securityQuestionAnswer?: SecurityQuestionAnswer;
  trackingUuid?: string;
  username?: string;
  militaryStatus?: string;
  militaryBranch?: string;
}

export interface Email {
  id: number;
  address?: string;
}

export interface Phone {
  id: number;
  number?: string;
}

export interface Address {
  address1?: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
}

export interface CustomerAddress extends Address {
  id: number;
  addressType?: string;
  regionCode?: string;
}

export interface SecurityQuestionAnswer {
  question: string;
  answer: string;
}

export enum CustomerType {
  RESIDENTIAL = 'RESIDENTIAL',
  COMMERCIAL = 'COMMERCIAL'
}

export interface ChallengeEmailRequest {
  email: string;
}

export enum ChallengeEmailErrorStatus {
  EMAIL_ALREADY_IN_USE = 'EMAIL_ALREADY_IN_USE',
  INVALID_EMAIL = 'INVALID_EMAIL',
  ERROR = 'ERROR'
}

export interface VerifyChallengeRequest {
  oneTimePassword: string;
}

export enum VerifyChallengeErrorStatus {
  SUCCESS = 'SUCCESS',
  INVALID_CODE = 'INVALID_CODE',
  ERROR = 'ERROR'
}

export interface UpdateEmailRequest {
  email: Email;
}

export interface UpdatePasswordRequest {
  oldPassword: string;
  newPassword: string;
  email: Email;
}

export interface UpdatePrimaryPhoneRequest {
  phone: Phone;
}

export interface UpdateSecondaryPhoneRequest {
  phone: Phone;
}

export interface UpdateBillingAddressRequest {
  address: CustomerAddress;
}

export interface UpdateShippingAddressRequest {
  address: CustomerAddress;
}

export interface UpdatePinRequest {
  pin: string;
  securityAnswer: string;
}

export enum UpdateEmailErrorStatus {
  ACCOUNT_UNDER_MAINTENANCE = 'ACCOUNT_UNDER_MAINTENANCE',
  NO_ACCOUNT_FOUND = 'NO_ACCOUNT_FOUND',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  EMAIL_ALREADY_IN_USE = 'EMAIL_ALREADY_IN_USE',
  INVALID_EMAIL = 'INVALID_EMAIL',
  ERROR = 'ERROR'
}

export interface UpdatePasswordResponse {
  responseCode: UpdatePasswordResponseCode;
}

export enum UpdatePasswordResponseCode {
  SUCCESS = 'SUCCESS',
  NO_ACCOUNT_FOUND = 'NO_ACCOUNT_FOUND',
  ACCOUNT_NOT_CONVERTED = 'ACCOUNT_NOT_CONVERTED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  PASSWORD_FAILED_REQUIREMENTS = 'PASSWORD_FAILED_REQUIREMENTS',
  NEW_PASSWORD_FAILED_REQUIREMENTS = 'NEW_PASSWORD_FAILED_REQUIREMENTS',
  NEW_PASSWORD_IS_OLD_PASSWORD = 'NEW_PASSWORD_IS_OLD_PASSWORD',
  NEW_PASSWORD_IS_COMMON = 'NEW_PASSWORD_IS_COMMON',
  NEW_PASSWORD_IS_REUSED = 'NEW_PASSWORD_IS_REUSED',
  INVALID_ACTIVATION_TOKEN = 'INVALID_ACTIVATION_TOKEN',
  INVALID_EXPIRED_PASSWORD_TOKEN = 'INVALID_EXPIRED_PASSWORD_TOKEN',
  INVALID_RESET_PASSWORD_TOKEN = 'INVALID_RESET_PASSWORD_TOKEN',
  LOCKED_ACCOUNT = 'LOCKED_ACCOUNT',
  MULTIPLE_USERS = 'MULTIPLE_USERS',
  ERROR = 'ERROR'
}

export interface UpdateSmsOptInRequest {
  lastName: string;
  primaryPhone?: Phone;
  secondaryPhone?: Phone;
  newSmsOptIn: boolean;
}

// Getters

export const formatFullName = (customer: Customer) =>
  `${customer.firstName ?? 'Web'} ${customer.lastName ?? 'Customer'}`;

export function formatAddress(address?: Address): string {
  if (!address || !address.address1) {
    return 'N/A';
  }
  const { address1, address2, city, state, postalCode } = address;
  const street = address1 && address2 ? `${address1}, ${address2}` : address1;
  const zip = postalCode?.includes('-')
    ? postalCode?.substring(0, postalCode?.indexOf('-'))
    : postalCode;
  return `${toTitleCase(street)?.trim()}, ${toTitleCase(city)?.trim()}, ${state?.toUpperCase().trim()} ${zip}`;
}

export function formatAddressLine1(address: Address): string {
  const { address1, address2 } = address;
  const street = address1 && address2 ? `${address1}, ${address2}` : address1;
  return `${toTitleCase(street)?.trim()}`;
}

export function formatAddressLine2(address: Address): string {
  const { city, state, postalCode } = address;
  const zip = postalCode?.includes('-')
    ? postalCode?.substring(0, postalCode?.indexOf('-'))
    : postalCode;
  return `${toTitleCase(city)?.trim()}, ${state?.toUpperCase().trim()} ${zip}`;
}

// TODO: Try lodash deepEqual here.
export const areAddressEqual = (address: CustomerAddress, other: CustomerAddress) => {
  if (address.id !== other.id) return false;
  if (address.addressType !== other.addressType) return false;
  if (address.address1 !== other.address1) return false;
  if (address.address2 !== other.address2) return false;
  if (address.city !== other.city) return false;
  if (address.state !== other.state) return false;
  if (address.postalCode !== other.postalCode) return false;
  if (address.regionCode !== other.regionCode) return false;
  return true;
};

export const customerEmailOutOfSync = (customer: Customer) =>
  customer.isConverted &&
  customer.username != null &&
  customer.email?.address?.toLowerCase() !== customer.username?.toLowerCase();
