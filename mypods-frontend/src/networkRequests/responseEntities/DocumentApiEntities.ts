import { CustomerDocument, DocumentDescription, IDocument } from '../../domain/DocumentEntities';
import { MothCheckboxState } from '../../pages/MothFlyInspectionFormPage/MothInspectionFormTypes';
import { CustomerAddress } from './CustomerEntities';
import {
  CustomerDocumentType,
  OrderDocumentType,
  PoetDocumentStatus
} from './DocumentSharedSubtypes';

export type AcceptRentalAgreementRequest = {
  orderId: string;
  identity: string;
  companyCode: string;
  firstName?: string;
  lastName?: string;
  docId?: string;
  docTitle?: string;
} & DocumentSignatureTimezoneRequest;

export interface DocumentAPI {
  id: string;
  orderId: string;
  type: DocumentTypeAPI;
  description: DocumentDescriptionAPI;
  companyCode: string;
  isPoet: boolean;
}

export type OrderDocumentAPI = {
  orderId: string;
  billingCompanyCode: string;
  docName: string;
  docPath?: string;
  isCustomerFacing: boolean;
  id: string;
  docType: OrderDocumentType;
  docStatus?: PoetDocumentStatus;
  isInterFranchise: boolean;
  title: DocumentDescription;
};

export type CustomerDocumentAPI = {
  docRef: string;
  docName: string;
  isCustomerFacing: boolean;
  id: string;
  docType: CustomerDocumentType;
  tags?: string;
  docNotes: string;
  invoiceNumber: string;
  title: DocumentDescription;
};

export type DocumentTypeAPI = 'MasterOrder' | 'Invoice' | 'Statement';

export type DocumentDescriptionAPI =
  | 'Order Confirmation'
  | 'Order Update Confirmation'
  | 'Local Rental Agreement - Electronic Acceptance';

export interface Documents {
  documents: DocumentAPI[];
  token: string;
}

export interface OrderDocuments {
  documents: OrderDocumentAPI[];
  token: string;
}

export interface CustomerDocuments {
  documents: CustomerDocumentAPI[];
  token: string;
}

const title = (description: DocumentDescriptionAPI): DocumentDescription => {
  if (description === 'Local Rental Agreement - Electronic Acceptance')
    return 'Local Rental Agreement';

  return description;
};

export function documentApiToDomain(document: DocumentAPI): IDocument {
  return {
    ...document,
    title: title(document.description)
  };
}

export function customerDocumentApiToDomain(
  customerDocument: CustomerDocumentAPI
): CustomerDocument {
  return {
    ...customerDocument
  };
}

export type SignFnpsWaiverRequest = {
  orderId: string;
  moveId: string;
  firstName: string;
  lastName: string;
  address: CustomerAddress;
} & DocumentSignatureTimezoneRequest;

export type SignMothAgreementRequest = {
  orderId: string;
  firstName: string;
  lastName: string;
  email: string;
  address: string;
  phone: string;
  selectedCheckboxes: string[];
  otherCheckboxes: Partial<MothCheckboxState>[];
} & DocumentSignatureTimezoneRequest;

export type DocumentSignatureTimezoneRequest = {
  timezoneOffset: number;
};
