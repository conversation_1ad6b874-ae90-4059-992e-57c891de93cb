import { Customer, CustomerAddress } from './CustomerEntities';
import { PaymentMethodPayload } from '../../pages/ManagePaymentMethodPage/Braintree/PaymentMethodPayload';

type PaymentMethodTypes =
  | 'Visa'
  | 'MasterCard'
  | 'AmericanExpress'
  | 'Discover'
  | 'PayPal'
  | 'LineOfCredit';

export interface PaymentMethodAPI {
  paymentMethodId?: string;
  isPrimary: boolean;
  cardType: PaymentMethodTypes;
  cardNumberLastFourDigits: string;
  accountId: string;
  cardExpirationDate: string;
  locAmount?: number;
  locApplicationId: number;
  locBalance?: number;
  locIsActive: boolean;
  locLender?: string;
  locOriginationDate?: string;
  locProviderName?: string;
  locTerminationDate?: string;
}

export type PaymentMethod = {
  paymentMethodId?: string;
  isPrimary: boolean;
  cardType: PaymentMethodTypes;
  displayCardType: string;
  cardNumberLastFourDigits: string;
  accountId: string;
  cardExpirationDate: Date;
  locAmount?: number;
  locApplicationId: number;
  locBalance?: number;
  locIsActive: boolean;
  locLender?: string;
  locOriginationDate?: Date;
  locProviderName?: string;
  locTerminationDate?: Date;
};

export type PaymentMethodType = 'CREDIT_CARD' | 'BANK_ACCOUNT';

export interface SetDefaultPaymentMethodRequest {
  paymentMethodId: string;
  paymentMethodType: PaymentMethodType;
}

export interface PayInvoicesRequest {
  paymentMethodId: string;
  invoices: PaymentRequestInvoice[];
  totalPaymentAmount: number;
}

export type PaymentRequestInvoice = {
  invoiceNumber?: string;
  paymentAmount: number;
  dueDate?: string;
};

export type PaymentDeclinedErrors =
  | 'NO_BALANCE_REMAINING'
  | 'FRAUD_SUSPECTED'
  | 'CARD_CLOSED'
  | 'INACTIVE_CARD'
  | 'INVALID_ACCOUNT'
  | 'INSUFFICIENT_FUNDS'
  | 'PROCESSOR_DECLINED'
  | 'CARD_ISSUER_DECLINED'
  | 'BANK_PAYMENT_UNAUTHORISED'
  | 'PAYPAL_ACCOUNT_ISSUE'
  | 'LIMIT_EXCEEDED'
  | 'SOME_PAYMENTS_FAILED'
  | 'DECLINED';

export interface AddPaymentMethodRequest {
  cardHolderName: string;
  cardExpirationMonth: string;
  cardExpirationYear: string;
  cardType: string;
  braintreeNonceToken: string;
  cardNumberLastFourDigits?: string;
  cardNumber: string;
  firstName: string;
  lastName: string;
  billingAddress: CustomerAddress;
}

function mapCardTypeToDisplayCardType(paymentMethod: PaymentMethodTypes): string {
  if (paymentMethod === 'MasterCard') return 'Mastercard';
  if (paymentMethod === 'AmericanExpress') return 'American Express';
  return paymentMethod;
}

export function paymentMethodApiToDomain(paymentMethod: PaymentMethodAPI): PaymentMethod {
  return {
    ...paymentMethod,
    cardExpirationDate: new Date(paymentMethod.cardExpirationDate),
    locOriginationDate: paymentMethod.locOriginationDate
      ? new Date(paymentMethod.locOriginationDate)
      : undefined,
    locTerminationDate: paymentMethod.locTerminationDate
      ? new Date(paymentMethod.locTerminationDate)
      : undefined,
    displayCardType: mapCardTypeToDisplayCardType(paymentMethod.cardType)
  };
}

export function addPaymentMethodRequest(
  payload: PaymentMethodPayload,
  customer: Customer,
  billingAddress: CustomerAddress
): AddPaymentMethodRequest {
  return {
    cardHolderName: payload.cardholderName,
    cardExpirationMonth: payload.expirationMonth,
    cardExpirationYear: payload.expirationYear,
    cardType: payload.cardType,
    braintreeNonceToken: payload.nonceToken,
    cardNumberLastFourDigits: payload.cardLastFourDigits,
    cardNumber: payload.cardNumber,
    firstName: customer.firstName,
    lastName: customer.lastName,
    billingAddress
  };
}

// -- getters --

export function isCitiBank(method: PaymentMethod) {
  return method.locLender?.includes('Citi');
}
