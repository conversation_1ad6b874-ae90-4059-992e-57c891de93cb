import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useAcceptInitialDeliveryPlacement = () => {
  const { acceptInitialDeliveryPlacement } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.ACCEPT_INITIAL_DELIVERY_PLACEMENT_KEY],
    mutationFn: (orderId: string) => acceptInitialDeliveryPlacement(orderId)
  });

  return { ...mutationResult };
};
