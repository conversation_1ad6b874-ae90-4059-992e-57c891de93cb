import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { SignMothAgreementRequest } from '../responseEntities/DocumentApiEntities';

export const useSignMothAgreement = () => {
  const { signMothAgreement } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.SIGN_MOTH_AGREEMENT_KEY],
    mutationFn: (request: SignMothAgreementRequest) => signMothAgreement(request)
  });

  return { ...mutationResult };
};
