import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { AcceptRentalAgreementRequest } from '../responseEntities/DocumentApiEntities';

export const useAcceptRentalAgreement = () => {
  const { acceptRentalAgreement } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.ACCEPT_RA_KEY],
    mutationFn: (request: AcceptRentalAgreementRequest) => acceptRentalAgreement(request)
  });

  // the Mulesoft update can return a response if we want to use it in an onSuccess callback;
  // currently we're not passing it back from the POET facade.
  return { ...mutationResult };
};
