import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { SignFnpsWaiverRequest } from '../responseEntities/DocumentApiEntities';

export const useSignFnpsWaiver = () => {
  const { signFnpsWaiver } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.SIGN_FNPS_WAIVER_KEY],
    mutationFn: (request: SignFnpsWaiverRequest) => signFnpsWaiver(request)
  });

  return { ...mutationResult };
};
