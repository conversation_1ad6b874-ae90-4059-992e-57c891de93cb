import { useMutation } from '@tanstack/react-query';
import { UpdatePasswordRequest } from '../responseEntities/CustomerEntities';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useUpdatePassword = () => {
  const { updatePassword } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_PASSWORD_KEY],
    mutationFn: (request: UpdatePasswordRequest) => updatePassword(request)
  });

  return {
    ...mutationResult
  };
};
