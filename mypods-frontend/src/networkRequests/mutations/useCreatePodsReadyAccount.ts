import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { PodsReadyCreateAccountRequest } from '../responseEntities/PodsReadyEntities';

export const useCreatePodsReadyAccount = () => {
  const { createPodsReadyAccount } = useMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.CREATE_PODS_READY_ACCOUNT_KEY],
    mutationFn: (request: PodsReadyCreateAccountRequest) => createPodsReadyAccount(request)
  });

  return { ...mutationResult };
};
