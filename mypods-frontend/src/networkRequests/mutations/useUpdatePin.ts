import { useMutation } from '@tanstack/react-query';
import { UpdatePinRequest } from '../responseEntities/CustomerEntities';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useUpdatePin = () => {
  const { updatePin } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_PIN_KEY],
    mutationFn: (request: UpdatePinRequest) => updatePin(request)
  });

  return {
    ...mutationResult,
    customer: mutationResult.data
  };
};
