import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { PayInvoicesRequest } from '../responseEntities/PaymentEntities';

export const useMakePayment = () => {
  const { payInvoices } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.MAKE_PAYMENT_KEY],
    mutationFn: (request: PayInvoicesRequest) => payInvoices(request)
  });

  return { ...mutationResult };
};
