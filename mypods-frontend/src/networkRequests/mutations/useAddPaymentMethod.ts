import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { AddPaymentMethodRequest } from '../responseEntities/PaymentEntities';

export const useAddPaymentMethod = () => {
  const { addPaymentMethod } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.ADD_PAYMENT_METHOD_KEY],
    mutationFn: (request: AddPaymentMethodRequest) => addPaymentMethod(request)
  });

  return {
    ...mutationResult
  };
};
