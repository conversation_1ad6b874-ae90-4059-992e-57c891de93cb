import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useGetSasUrl = () => {
  const { getSasUrl } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.GET_SAS_URL_KEY],
    mutationFn: ({ docRef, isBillingDocument }: { docRef: string; isBillingDocument: boolean }) =>
      getSasUrl(docRef, isBillingDocument)
  });

  return { ...mutationResult };
};
