import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { SameServiceAreaRequest } from '../../domain/OrderEntities';

export const useIsSameServiceArea = () => {
  const { isSameServiceArea } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.IS_SAME_SERVICE_AREA_KEY],
    mutationFn: (request: SameServiceAreaRequest) => isSameServiceArea(request)
  });

  return { ...mutationResult };
};
