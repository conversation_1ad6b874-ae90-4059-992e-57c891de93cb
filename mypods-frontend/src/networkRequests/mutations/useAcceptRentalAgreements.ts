import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { AcceptRentalAgreementRequest } from '../responseEntities/DocumentApiEntities';

export const useAcceptRentalAgreements = () => {
  const { acceptMultipleRentalAgreements } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.ACCEPT_MULTIPLE_RA_KEY],
    mutationFn: (requests: AcceptRentalAgreementRequest[]) =>
      acceptMultipleRentalAgreements(requests)
  });

  return { ...mutationResult };
};
