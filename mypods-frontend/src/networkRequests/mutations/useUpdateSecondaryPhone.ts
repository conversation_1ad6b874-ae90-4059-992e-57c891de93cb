import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { UpdateSecondaryPhoneRequest } from '../responseEntities/CustomerEntities';

export const useUpdateSecondaryPhone = () => {
  const { updateSecondaryPhone } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_SECONDARY_PHONE_KEY],
    mutationFn: (request: UpdateSecondaryPhoneRequest) => updateSecondaryPhone(request)
  });

  return { ...mutationResult };
};
