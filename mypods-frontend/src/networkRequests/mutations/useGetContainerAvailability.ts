import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { ContainerAvailabilityRequest } from '../responseEntities/AvailabilityAPIEntities';

export const useGetContainerAvailability = () => {
  const { getContainerAvailability } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.CONTAINER_AVAILABILITY_KEY],
    mutationFn: (request: ContainerAvailabilityRequest) => getContainerAvailability(request)
  });

  return {
    ...mutationResult
  };
};
