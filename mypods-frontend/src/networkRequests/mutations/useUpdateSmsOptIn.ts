import { useMutation } from '@tanstack/react-query';
import { UpdateSmsOptInRequest } from '../responseEntities/CustomerEntities';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useUpdateSmsOptIn = () => {
  const { updateSmsOptIn } = useMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_SMS_OPT_IN_KEY],
    mutationFn: (request: UpdateSmsOptInRequest) => updateSmsOptIn(request)
  });

  return { ...mutationResult };
};
