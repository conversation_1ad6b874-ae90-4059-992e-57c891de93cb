import { useMutation } from '@tanstack/react-query';
import { UpdateShippingAddressRequest } from '../responseEntities/CustomerEntities';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useUpdateShippingAddress = () => {
  const { updateShippingAddress } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_SHIPPING_ADDRESS_KEY],
    mutationFn: (request: UpdateShippingAddressRequest) => updateShippingAddress(request)
  });

  return {
    ...mutationResult
  };
};
