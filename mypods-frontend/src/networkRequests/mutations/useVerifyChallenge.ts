import { useMutation } from '@tanstack/react-query';
import { VerifyChallengeRequest } from '../responseEntities/CustomerEntities';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useVerifyChallenge = () => {
  const { verifyChallenge } = useMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.VERIFY_CHALLENGE_KEY],
    mutationFn: (request: VerifyChallengeRequest) => verifyChallenge(request)
  });

  return { ...mutationResult };
};
