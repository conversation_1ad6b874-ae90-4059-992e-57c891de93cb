import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { AddWarehouseRequest } from '../../domain/OrderEntities';

export const useAddWarehouseVisit = () => {
  const { addWarehouseVisit } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.ADD_WAREHOUSE_KEY],
    mutationFn: (request: AddWarehouseRequest) => addWarehouseVisit(request)
  });

  return {
    ...mutationResult
  };
};
