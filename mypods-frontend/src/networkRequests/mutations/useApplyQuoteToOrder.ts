import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { ApplyQuoteToOrderRequest } from '../../domain/OrderEntities';

export const useApplyQuoteToOrder = () => {
  const { applyQuoteToOrder } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.APPLY_QUOTE_TO_ORDER],
    mutationFn: (request: ApplyQuoteToOrderRequest) => applyQuoteToOrder(request)
  });

  return {
    ...mutationResult
  };
};
