import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { UpdateBillingAddressRequest } from '../responseEntities/CustomerEntities';

export const useUpdateBillingAddress = () => {
  const { updateBillingAddress } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_BILLING_ADDRESS_KEY],
    mutationFn: (request: UpdateBillingAddressRequest) => updateBillingAddress(request)
  });

  return { ...mutationResult };
};
