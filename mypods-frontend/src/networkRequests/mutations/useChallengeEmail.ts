import { useMutation } from '@tanstack/react-query';
import { ChallengeEmailRequest } from '../responseEntities/CustomerEntities';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useChallengeEmail = () => {
  const { challengeEmail } = useMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.CHALLENGE_EMAIL_KEY],
    mutationFn: (request: ChallengeEmailRequest) => challengeEmail(request)
  });

  return { ...mutationResult };
};
