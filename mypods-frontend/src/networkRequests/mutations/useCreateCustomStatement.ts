import { useMutation } from '@tanstack/react-query';
import { CustomStatementRequest } from '../responseEntities/BillingEntities';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useCreateCustomStatement = () => {
  const { createCustomStatement } = useMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.CREATE_CUSTOM_STATEMENT_KEY],
    mutationFn: (request: CustomStatementRequest) => createCustomStatement(request)
  });

  return { ...mutationResult };
};
