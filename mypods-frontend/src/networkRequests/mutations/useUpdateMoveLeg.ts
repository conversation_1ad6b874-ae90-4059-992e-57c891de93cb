import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { UpdateMoveLegRequest } from '../../domain/OrderEntities';

export const useUpdateMoveLeg = () => {
  const { updateMoveLeg } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_MOVE_LEG_KEY],
    mutationFn: (request: UpdateMoveLegRequest) => updateMoveLeg(request)
  });

  return {
    ...mutationResult
  };
};
