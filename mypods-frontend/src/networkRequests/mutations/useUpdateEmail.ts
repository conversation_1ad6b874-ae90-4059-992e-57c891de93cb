import { useMutation } from '@tanstack/react-query';
import { UpdateEmailRequest } from '../responseEntities/CustomerEntities';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useUpdateEmail = () => {
  const { updateEmail } = useMyPodsService();

  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_EMAIL_KEY],
    mutationFn: (request: UpdateEmailRequest) => updateEmail(request)
  });

  return { ...mutationResult };
};
