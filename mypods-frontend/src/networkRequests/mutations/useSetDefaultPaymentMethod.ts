import { useMutation } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';
import { SetDefaultPaymentMethodRequest } from '../responseEntities/PaymentEntities';

export const useSetDefaultPaymentMethod = () => {
  const { setDefaultPaymentMethod } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.SET_DEFAULT_PAYMENT_METHOD_KEY],
    mutationFn: (request: SetDefaultPaymentMethodRequest) => setDefaultPaymentMethod(request)
  });

  return { ...mutationResult };
};
