import { useMutation } from '@tanstack/react-query';
import { UpdatePrimaryPhoneRequest } from '../responseEntities/CustomerEntities';
import { useMyPodsService } from '../MyPodsService';
import { MutationCacheKeys } from '../MutationCacheKeys';

export const useUpdatePrimaryPhone = () => {
  const { updatePrimaryPhone } = useMyPodsService();
  const mutationResult = useMutation({
    mutationKey: [MutationCacheKeys.UPDATE_PRIMARY_PHONE_KEY],
    mutationFn: (request: UpdatePrimaryPhoneRequest) => updatePrimaryPhone(request)
  });

  return {
    ...mutationResult
  };
};
