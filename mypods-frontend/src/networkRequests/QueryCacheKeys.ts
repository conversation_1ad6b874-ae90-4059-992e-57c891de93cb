export enum QueryCacheKeys {
  BILLING_INFORMATION_CACHE_KEY = 'billing-information',
  CUSTOMER_DOCUMENTS_KEY = 'customer-documents',
  CUSTOMER_ORDERS_CACHE_KEY = 'customer-orders',
  ORDER_DOCUMENTS_KEY = 'order-documents',
  PAYMENT_METHODS_KEY = 'get-payment-methods',
  DOCUMENTS_KEY = 'documents',
  CUSTOMER_KEY = 'customer',
  REFRESH_KEY = 'refresh',
  PODS_READY_SESSION_KEY = 'pods-ready-session',
  ENTRYPOINT_KEY = 'authorization-entrypoint'
}

export enum LegacyQueryCacheKeys {
  LEGACY_CUSTOMER_CACHE_KEY = 'legacy-customer',
  LEGACY_CUSTOMER_ORDERS_CACHE_KEY = 'legacy-customer-orders',
  LEGACY_PAYMENT_METHODS_KEY = 'legacy-payment-methods',
  LEGACY_DOCUMENTS_KEY = 'legacy-documents',
  LEGACY_BILLING_INFORMATION_KEY = 'legacy-billing-information',
  LEGACY_ENTRYPOINT_KEY = 'legacy-authorization-entrypoint'
}
