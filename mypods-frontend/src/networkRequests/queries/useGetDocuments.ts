import { useQuery } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { documentApiToDomain } from '../responseEntities/DocumentApiEntities';
import { setDocumentToken } from '../../helpers/storageHelpers';
import { QueryCacheKeys } from '../QueryCacheKeys';

export const useGetDocuments = () => {
  const { getDocuments } = useMyPodsService();

  const queryResult = useQuery({
    queryKey: [QueryCacheKeys.DOCUMENTS_KEY],
    queryFn: () => getDocuments()
  });

  if (queryResult.data?.token) {
    setDocumentToken(queryResult.data.token);
  }

  return {
    ...queryResult,
    documents: queryResult.data?.documents?.map((document) => documentApiToDomain(document))
  };
};
