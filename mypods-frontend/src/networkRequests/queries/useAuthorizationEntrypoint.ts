import { useQuery } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { QueryCacheKeys } from '../QueryCacheKeys';

export const useAuthorizationEntrypoint = () => {
  const { authorizationEntrypoint } = useMyPodsService();
  const queryResult = useQuery({
    queryKey: [QueryCacheKeys.ENTRYPOINT_KEY],
    queryFn: () => authorizationEntrypoint()
  });

  return {
    ...queryResult
  };
};
