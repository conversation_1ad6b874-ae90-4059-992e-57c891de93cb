import { Suspense } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook } from '@testing-library/react';
import { useGetOrderDocuments } from '../useGetOrderDocuments';
import { runPendingPromises, testQueryClient } from '../../../../testUtils/RenderHelpers';
import { createOrderDocument } from '../../../../testUtils/MyPodsFactories';
import { mockGetOrderDocuments } from '../../../../../setupTests';
import { OrderDocument } from '../../../../domain/DocumentEntities';
import { excludeFromDocPageTypes } from '../../../responseEntities/DocumentSharedSubtypes';

// -- consts --
const SUSPENSE_FALLBACK = 'Loading Order Documents...';

// -- tests --
describe('useGetOrderDocuments.tsx', () => {
  let orderConfirmation: OrderDocument;
  let unsignedRentalAgreement: OrderDocument;
  let signedRentalAgreement: OrderDocument;
  let unsignedMothForm: OrderDocument;
  let signedLanternFlyForm: OrderDocument;
  let emptyWeightTicket: OrderDocument;
  let filledWeightTicketWithTruck: OrderDocument;
  let excludedDocument: OrderDocument;

  let queryClient: QueryClient;

  beforeEach(() => {
    orderConfirmation = createOrderDocument({ id: '1', docType: 'ORDER_CONFIRMATION' });
    unsignedRentalAgreement = createOrderDocument({
      id: '2a',
      docType: 'CONTRACT',
      docStatus: 'SENT'
    });
    signedRentalAgreement = createOrderDocument({
      id: '2b',
      docType: 'CONTRACT',
      docStatus: 'COMPLETED'
    });
    unsignedMothForm = createOrderDocument({
      id: '3',
      docType: 'SPONGY_MOTH_FORM',
      docStatus: 'SENT'
    });
    signedLanternFlyForm = createOrderDocument({
      id: '4',
      docType: 'SPOTTED_LANTERN_FLY_FORM',
      docStatus: 'SENT'
    });
    emptyWeightTicket = createOrderDocument({
      id: '5a',
      docType: 'MILITARY_WEIGHT_TICKET_EMPTY',
      docStatus: 'SENT'
    });
    filledWeightTicketWithTruck = createOrderDocument({
      id: '5a',
      docType: 'MILITARY_WEIGHT_TICKET_FULL_WITH_TRUCK',
      docStatus: 'SENT'
    });
    excludedDocument = createOrderDocument({
      id: '6',
      docType: 'MONTHLY_STATEMENT_OR_INVOICE',
      docStatus: 'SENT'
    });
    queryClient = testQueryClient();
  });

  const renderGetOrderDocuments = () => {
    // @ts-ignore
    const wrapper = ({ children }) => (
      <QueryClientProvider client={queryClient}>
        <Suspense fallback={SUSPENSE_FALLBACK}>{children}</Suspense>
      </QueryClientProvider>
    );
    return renderHook(() => useGetOrderDocuments(), { wrapper });
  };

  it('returns the query result', async () => {
    mockGetOrderDocuments.mockResolvedValue({ documents: [orderConfirmation] });
    const { result } = renderGetOrderDocuments();

    await runPendingPromises();
    expect(result.current.data.documents).toContain(orderConfirmation);
  });

  it('filters moth forms from the exported order documents', async () => {
    mockGetOrderDocuments.mockResolvedValue({
      documents: [orderConfirmation, unsignedMothForm, signedLanternFlyForm]
    });
    const { result } = renderGetOrderDocuments();

    await runPendingPromises();
    expect(result.current.data.documents).toContain(orderConfirmation);
    expect(result.current.data.documents).toContain(unsignedMothForm);
    expect(result.current.orderDocuments).not.toContain(unsignedMothForm);
    expect(result.current.orderDocuments).not.toContain(signedLanternFlyForm);
    expect(result.current.orderDocuments[0]).toEqual<OrderDocument>(orderConfirmation);
  });

  describe('rental agreements', () => {
    beforeEach(() => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [unsignedRentalAgreement, signedRentalAgreement]
      });
    });

    it('returns a list of unsigned rental agreements', async () => {
      const { result } = renderGetOrderDocuments();

      await runPendingPromises();
      expect(result.current.outstandingRentalAgreements).toHaveLength(1);
      expect(result.current.outstandingRentalAgreements[0]).toEqual(unsignedRentalAgreement);
    });

    it('updates the cache when a rental agreement has been signed', async () => {
      const { result, rerender } = renderGetOrderDocuments();

      await runPendingPromises();
      expect(result.current.outstandingRentalAgreements).toHaveLength(1);
      expect(result.current.outstandingRentalAgreements[0]).toEqual(unsignedRentalAgreement);

      result.current.markRentalAgreementAsCompleted(unsignedRentalAgreement.id);

      rerender();
      expect(result.current.outstandingRentalAgreements).toHaveLength(0);
    });

    test.each(excludeFromDocPageTypes)(
      'verify %s is not present in the filtered order documents',
      async (documentType) => {
        excludedDocument.docType = documentType;
        mockGetOrderDocuments.mockResolvedValue({
          documents: [signedRentalAgreement, excludedDocument]
        });

        const { result } = renderGetOrderDocuments();

        await runPendingPromises();
        expect(result.current.orderDocuments[0].docType).toMatch(signedRentalAgreement.docType);
        expect(
          result.current.orderDocuments?.find((x) => x.docType == excludedDocument.docType)
        ).toBeUndefined();
      }
    );
  });
  describe('weight tickets', () => {
    it('returns true when there are weight tickets, regardless of doc status', async () => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [emptyWeightTicket, filledWeightTicketWithTruck]
      });
      const { result } = renderGetOrderDocuments();

      await runPendingPromises();
      expect(result.current.hasWeightTickets).toBe(true);
    });

    it('returns false when there are no weight tickets, regardless of doc status', async () => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [orderConfirmation]
      });
      const { result } = renderGetOrderDocuments();

      await runPendingPromises();

      expect(result.current.hasWeightTickets).toBe(false);
    });
  });
});
