import { useSuspenseQuery } from '@tanstack/react-query';
import { useMyPodsService } from '../../MyPodsService';
import {
  CustomerDocumentAPI,
  customerDocumentApiToDomain
} from '../../responseEntities/DocumentApiEntities';
import { setDocumentToken } from '../../../helpers/storageHelpers';
import { QueryCacheKeys } from '../../QueryCacheKeys';
import { excludeFromDocPageTypes } from '../../responseEntities/DocumentSharedSubtypes';

export const useGetCustomerDocuments = () => {
  const { getCustomerDocuments } = useMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.CUSTOMER_DOCUMENTS_KEY],
    queryFn: () => getCustomerDocuments()
  });

  // For poet, when generating sas-url, we don't use the token - because docs are cached, we do a lookup in the backend.
  // Remove all token code after poet launch
  // TODO POET
  setDocumentToken('');

  const customerDocuments =
    queryResult.data?.documents?.map((document: CustomerDocumentAPI) =>
      customerDocumentApiToDomain(document)
    ) ?? [];

  const documentsPageDocuments = customerDocuments?.filter(
    (document: CustomerDocumentAPI) => !excludeFromDocPageTypes.includes(document.docType)
  );

  return {
    ...queryResult,
    customerDocuments,
    documentsPageDocuments
  };
};
