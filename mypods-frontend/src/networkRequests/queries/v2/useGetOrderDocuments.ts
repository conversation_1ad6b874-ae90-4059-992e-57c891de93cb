import { useQueryClient, useSuspenseQuery } from '@tanstack/react-query';
import { useMyPodsService } from '../../MyPodsService';
import { OrderDocument } from '../../../domain/DocumentEntities';
import {
  excludeFromDocPageTypes,
  mothFormDocumentTypes,
  rentalAgreementDocumentTypes,
  weightTicketDocumentTypes
} from '../../responseEntities/DocumentSharedSubtypes';
import { setDocumentToken } from '../../../helpers/storageHelpers';
import { QueryCacheKeys } from '../../QueryCacheKeys';

export const useGetOrderDocuments = () => {
  const queryClient = useQueryClient();
  const { getOrderDocuments } = useMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.ORDER_DOCUMENTS_KEY],
    queryFn: () => getOrderDocuments()
  });

  // For poet, when generating sas-url, we don't use the token - because docs are cached, we do a lookup in the backend.
  // Remove all token code after poet launch
  // TODO POET
  setDocumentToken('');

  const orderDocuments = queryResult.data?.documents ?? [];

  const filteredOrderDocuments = orderDocuments?.filter(
    (document: OrderDocument) => !excludeFromDocPageTypes.includes(document.docType)
  );
  // -- Rental Agreements --
  const outstandingRentalAgreements = orderDocuments?.filter(
    (document: OrderDocument) =>
      rentalAgreementDocumentTypes.includes(document.docType) && document.docStatus !== 'COMPLETED'
  );
  // -- Moth Agreements --
  const outstandingMothAgreements = orderDocuments?.filter(
    (document: OrderDocument) =>
      mothFormDocumentTypes.includes(document.docType) && document.docStatus === 'SENT'
  );

  // -- Weight Tickets --
  const weightTicketDocs = orderDocuments?.filter((document: OrderDocument) =>
    weightTicketDocumentTypes.includes(document.docType)
  );
  const hasWeightTickets = weightTicketDocs.length > 0;

  // -- Fragile &  Non-Paved Surfaces --
  // This is out of scope for poet release, but will be revisited.
  const outstandingFnpsAgreements = orderDocuments?.filter(
    (document: OrderDocument) =>
      document.docType === 'FRAGILE_AND_NON_PAVED_SURFACE_WAIVER' &&
      document.docStatus !== 'COMPLETED'
  );

  // -- Actions --

  // The entrypoint version of the FNPS completion relied on the moveId.
  // Ensure we're not losing functionality as we reimplement this.
  const markAgreementAsCompleted = (id: string) => {
    queryClient.setQueryData([QueryCacheKeys.ORDER_DOCUMENTS_KEY], {
      ...queryResult.data,
      documents: orderDocuments.map((doc: OrderDocument) => {
        if (doc.id === id) {
          return { ...doc, docStatus: 'COMPLETED' };
        }
        return doc;
      })
    });
  };

  const markAllAgreementsAsCompleted = (ids: string[]) => {
    queryClient.setQueryData([QueryCacheKeys.ORDER_DOCUMENTS_KEY], {
      ...queryResult.data,
      documents: orderDocuments.map((doc: OrderDocument) => {
        if (ids.includes(doc.id)) {
          return { ...doc, docStatus: 'COMPLETED' };
        }
        return doc;
      })
    });
  };

  return {
    ...queryResult,
    orderDocuments: filteredOrderDocuments,
    outstandingMothAgreements,
    outstandingFnpsAgreements,
    outstandingRentalAgreements,
    hasWeightTickets,
    markRentalAgreementAsCompleted: (id: string) => {
      markAgreementAsCompleted(id);
    },
    markFnpsAgreementAsCompleted: (id: string) => {
      markAgreementAsCompleted(id);
    },
    markAllRentalAgreementsAsCompleted: (ids: string[]) => {
      markAllAgreementsAsCompleted(ids);
    }
  };
};
