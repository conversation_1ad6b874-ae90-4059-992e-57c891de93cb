import { useSuspenseQuery } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { paymentMethodApiToDomain } from '../responseEntities/PaymentEntities';
import { QueryCacheKeys } from '../QueryCacheKeys';

export const useGetPaymentMethods = () => {
  const { getPaymentMethods } = useMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.PAYMENT_METHODS_KEY],
    queryFn: () => getPaymentMethods()
  });

  return {
    ...queryResult,
    paymentMethods:
      queryResult.data?.map((paymentMethod) => paymentMethodApiToDomain(paymentMethod)) ?? []
  };
};
