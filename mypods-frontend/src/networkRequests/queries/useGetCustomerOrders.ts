import { useQueryClient, useSuspenseQuery } from '@tanstack/react-query';
import isEmpty from 'lodash/isEmpty';
import { useMyPodsService } from '../MyPodsService';
import { OrderAPI, orderApiToDomain } from '../responseEntities/OrderAPIEntities';
import { QueryCacheKeys } from '../QueryCacheKeys';

export const useGetCustomerOrders = () => {
  const MAX_TRIES = 10;
  const { getCustomerOrders } = useMyPodsService();
  const queryClient = useQueryClient();

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.CUSTOMER_ORDERS_CACHE_KEY],
    queryFn: () => getCustomerOrders()
  });

  const createOrdersBeforeUpdate = (ordersBeforeTheUpdate: Map<string, string>) => {
    const ordersBeforeUpdating = queryClient.getQueryData([
      QueryCacheKeys.CUSTOMER_ORDERS_CACHE_KEY
    ]) as OrderAPI[];
    convertOrders(ordersBeforeUpdating, ordersBeforeTheUpdate);
  };

  const refetchUntilTheContainersUpdate = (
    notifyContainersUpdated?: (stopScheduling: boolean) => void,
    ordersBeforeTheUpdate: Map<string, string> = new Map<string, string>(),
    tries: number = 0,
    maxTries: number = MAX_TRIES
  ) => {
    if (isEmpty(ordersBeforeTheUpdate)) {
      createOrdersBeforeUpdate(ordersBeforeTheUpdate);
    }
    getCustomerOrders().then((ordersAfterUpdating: OrderAPI[]) => {
      if (tries > maxTries || ordersChanged(ordersBeforeTheUpdate, ordersAfterUpdating)) {
        queryClient.setQueryData([QueryCacheKeys.CUSTOMER_ORDERS_CACHE_KEY], ordersAfterUpdating);
        if (notifyContainersUpdated) {
          notifyContainersUpdated(true);
        }
      } else {
        setTimeout(
          refetchUntilTheContainersUpdate.bind(
            this,
            notifyContainersUpdated,
            ordersBeforeTheUpdate,
            tries + 1
          ),
          500
        );
      }
    });
  };

  return {
    ...queryResult,
    customerOrders: queryResult.data?.map((order) => orderApiToDomain(order)),
    refetchUntilTheOrdersUpdate: refetchUntilTheContainersUpdate
  };
};

export const ordersChanged = (
  containersBeforeTheUpdate: Map<string, string>,
  updatedOrders: OrderAPI[]
): boolean => {
  let updated = false;
  updatedOrders.forEach((order: OrderAPI) => {
    const originalUpdatedTime = containersBeforeTheUpdate.get(order.orderId);
    if (originalUpdatedTime !== order.updatedDate) {
      updated = true;
    }
  });
  return updated;
};

export const convertOrders = (
  orders: OrderAPI[],
  ordersBeforeTheUpdate: Map<string, string>
): void => {
  orders.forEach((order: OrderAPI) => {
    ordersBeforeTheUpdate.set(order.orderId, order.updatedDate);
  });
};
