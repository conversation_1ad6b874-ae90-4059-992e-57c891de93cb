import { useQueryClient, useSuspenseQuery } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { Customer } from '../responseEntities/CustomerEntities';
import { QueryCacheKeys } from '../QueryCacheKeys';

export const useGetCustomer = () => {
  const { getCustomer } = useMyPodsService();
  const queryClient = useQueryClient();

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.CUSTOMER_KEY],
    queryFn: () => getCustomer()
  });

  return {
    ...queryResult,
    customer: queryResult.data,
    // because our backend doesn't return updated objects after mutations
    // we take the local state and update our cache manually.
    updateCustomer: (updatedCustomer: Partial<Customer>) => {
      queryClient.setQueryData([QueryCacheKeys.CUSTOMER_KEY], {
        ...queryResult.data,
        ...updatedCustomer
      });
    }
  };
};
