import { useSuspenseQuery } from '@tanstack/react-query';
import { useMyPodsService } from '../../MyPodsService';
import { QueryCacheKeys } from '../../QueryCacheKeys';
import { orderApiToDomain } from '../../responseEntities/OrderAPIEntities';

export const useGetPodsReadyCustomerOrders = () => {
  const { getPodsReadyOrders } = useMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.CUSTOMER_ORDERS_CACHE_KEY],
    queryFn: () => getPodsReadyOrders()
  });

  return {
    ...queryResult,
    customerOrders: queryResult.data?.map((order) => orderApiToDomain(order))
  };
};
