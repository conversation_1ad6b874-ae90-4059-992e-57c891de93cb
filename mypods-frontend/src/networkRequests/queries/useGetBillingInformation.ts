import { useSuspenseQuery } from '@tanstack/react-query';
import { useMyPodsService } from '../MyPodsService';
import { setBillingDocumentToken } from '../../helpers/storageHelpers';
import { QueryCacheKeys } from '../QueryCacheKeys';

export const useGetBillingInformation = () => {
  const { getBillingInformation } = useMyPodsService();

  const queryResult = useSuspenseQuery({
    queryKey: [QueryCacheKeys.BILLING_INFORMATION_CACHE_KEY],
    queryFn: () => getBillingInformation()
  });

  // For poet, when generating sas-url, we don't use the token - because docs are cached, we do a lookup in the backend.
  // Remove all token code after poet launch
  // TODO POET
  setBillingDocumentToken('');

  return {
    ...queryResult,
    billingInformation: queryResult.data?.billingInformation
  };
};
