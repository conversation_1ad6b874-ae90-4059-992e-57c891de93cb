// Locum Constants
import { ENV_VARS } from '../environment';

export const NEW_IDENTITY: number = 0;
export const BILLING_ADDRESS_TYPE: string = 'Mailing';
export const SHIPPING_ADDRESS_TYPE: string = 'StreetAddress';
export const US_REGION_CODE: string = 'US';

// Poet Constants
export const CORPORATE_RENTAL_AGREEMENT = `${ENV_VARS.ASSETS_BASE_URL}/rental-agreements/Corporate_Rental_Agreement.pdf`;
export const LOCAL_RENTAL_AGREEMENT_PATH = (code: string) =>
  `${ENV_VARS.ASSETS_BASE_URL}/rental-agreements/contract${code}.pdf`;
