import { act } from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClientProvider } from '@tanstack/react-query';
import { testQueryClient } from '../../testUtils/RenderHelpers';
import { MemoryRouter } from 'react-router-dom';
import { useGetDocuments } from '../queries/useGetDocuments';
import { mockGetDocuments } from '../../../setupTests';
import { createAPIDocuments, createDocument } from '../../testUtils/MyPodsFactories';
import { documentApiToDomain } from '../responseEntities/DocumentApiEntities';

describe('useGetDocuments', () => {
  async function renderUseGetDocuments() {
    return act(() => {
      return renderHook(() => useGetDocuments(), {
        wrapper: (props) => (
          <QueryClientProvider client={testQueryClient()}>
            <MemoryRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
              {props.children}
            </MemoryRouter>
          </QueryClientProvider>
        )
      });
    });
  }

  it('requests and returns a customers documents', async () => {
    const documents = createAPIDocuments();
    mockGetDocuments.mockResolvedValue(documents);
    const domainDocuments = documents.documents.map((document) => documentApiToDomain(document));

    const quote = await renderUseGetDocuments();

    await waitFor(() => expect(quote.result.current.documents).toEqual(domainDocuments));
  });
});
