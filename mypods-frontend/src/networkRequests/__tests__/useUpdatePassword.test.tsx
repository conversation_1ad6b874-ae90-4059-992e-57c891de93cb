import { vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useUpdatePassword } from '../mutations/useUpdatePassword';
import { QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'react-router-dom';
import { AxiosError } from 'axios';
import { testQueryClient } from '../../testUtils/RenderHelpers';
import { createCustomer } from '../../testUtils/MyPodsFactories';
import { act } from 'react';
import { suppressErrorConsole } from '../../helpers/TestHelpers';
import { Customer } from '../responseEntities/CustomerEntities';

// -- mocks ==
const mockUpdatePassword = vi.hoisted(() => vi.fn());
vi.mock('../../networkRequests/MyPodsService', async () => ({
  useMyPodsService: () => ({
    updatePassword: mockUpdatePassword
  })
}));

describe('useUpdatePassword', function () {
  const customerId = '111222333'; //TODO, see if we need this
  const customer: Customer = createCustomer();
  const oldPassword = 'podZilla#1';
  const newPassword = 'PodZILLA2!';

  beforeEach(() => {
    mockUpdatePassword.mockReset();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  async function renderUseUpdatePassword() {
    return act(() => {
      return renderHook(() => useUpdatePassword(), {
        wrapper: (props) => (
          <QueryClientProvider client={testQueryClient()}>
            <MemoryRouter
              initialEntries={['?customerId=' + customerId]}
              future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
              {props.children}
            </MemoryRouter>
          </QueryClientProvider>
        )
      });
    });
  }

  it('requests and receives a success status code', async () => {
    mockUpdatePassword.mockResolvedValue(customer);

    const response = await renderUseUpdatePassword();

    await waitFor(() =>
      response.result.current.mutate({
        email: customer.email!,
        oldPassword: oldPassword,
        newPassword: newPassword
      })
    );
    expect(response.result.current.isSuccess).toBe(true);
  });

  it('returns an error, when the client returns an error', async () => {
    suppressErrorConsole();
    const error = new AxiosError('Request failed with status code 400', '400');
    mockUpdatePassword.mockRejectedValue(error);

    const response = await renderUseUpdatePassword();

    await waitFor(() =>
      response.result.current.mutate({
        email: customer.email!,
        oldPassword: oldPassword,
        newPassword: newPassword
      })
    );
    expect(response.result.current.isError).toBe(true);
  });
});
