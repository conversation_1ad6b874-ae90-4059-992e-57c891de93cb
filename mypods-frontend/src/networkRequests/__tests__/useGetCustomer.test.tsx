import { vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useGetCustomer } from '../queries/useGetCustomer';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'react-router-dom';
import { AxiosError } from 'axios';
import { testQueryClient } from '../../testUtils/RenderHelpers';
import { createCustomer } from '../../testUtils/MyPodsFactories';
import { act } from 'react';
import { suppressErrorConsole } from '../../helpers/TestHelpers';
import { Customer } from '../responseEntities/CustomerEntities';

// -- mocks ==
const mockGetCustomer = vi.hoisted(() => vi.fn());
vi.mock('../../networkRequests/MyPodsService', async () => ({
  useMyPodsService: () => ({
    getCustomer: mockGetCustomer
  })
}));

describe('useGetCustomer', function () {
  let queryClient: QueryClient;
  const customer: Customer = createCustomer();

  beforeEach(() => {
    queryClient = testQueryClient();
    mockGetCustomer.mockReset();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  async function renderUseGetCustomer() {
    return act(() => {
      return renderHook(() => useGetCustomer(), {
        wrapper: (props) => (
          <QueryClientProvider client={queryClient}>
            <MemoryRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
              {props.children}
            </MemoryRouter>
          </QueryClientProvider>
        )
      });
    });
  }

  it('requests and returns a Customer', async () => {
    mockGetCustomer.mockResolvedValue(customer);

    const quote = await renderUseGetCustomer();

    await waitFor(() => expect(quote.result.current.customer).toEqual(customer));
  });

  it('returns an error, when the client returns an error', async () => {
    suppressErrorConsole();
    const error = new AxiosError(
      'Request failed with status code 500',
      'AxiosError',
      undefined,
      'ERR_BAD_RESPONSE',
      undefined
    );
    mockGetCustomer.mockRejectedValue(error);
    try {
      await renderUseGetCustomer();
    } catch (error) {
      expect(error).toEqual(error);
    }
  });
});
