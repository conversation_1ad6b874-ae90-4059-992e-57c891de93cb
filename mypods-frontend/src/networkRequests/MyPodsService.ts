import { useContext } from 'react';
import { useLocation } from 'react-router-dom';
import { ApigeeContext } from '../context/ApigeeContext';
import {
  ChallengeEmailRequest,
  Customer,
  UpdateBillingAddressRequest,
  UpdateEmailRequest,
  UpdatePasswordRequest,
  UpdatePasswordResponse,
  UpdatePinRequest,
  UpdatePrimaryPhoneRequest,
  UpdateSecondaryPhoneRequest,
  UpdateShippingAddressRequest,
  UpdateSmsOptInRequest,
  VerifyChallengeRequest
} from './responseEntities/CustomerEntities';
import {
  BillingResponse,
  CustomStatement,
  CustomStatementRequest,
  formatCustomRange
} from './responseEntities/BillingEntities';
import { EntryPointResult, RefreshSessionClaims } from './responseEntities/AuthorizationEntities';
import {
  AcceptRentalAgreementRequest,
  CustomerDocuments,
  Documents,
  OrderDocuments,
  SignFnpsWaiverRequest,
  SignMothAgreementRequest
} from './responseEntities/DocumentApiEntities';
import { AccessTokenClaims } from './responseEntities/DebugEntities';
import {
  ApplyQuoteToOrderResponse,
  OrderAPI,
  UpdateMoveLegResponse
} from './responseEntities/OrderAPIEntities';
import {
  ContainerAvailabilityRequest,
  ContainerAvailabilityResponse
} from './responseEntities/AvailabilityAPIEntities';
import {
  AddWarehouseRequest,
  SameServiceAreaRequest,
  UpdateMoveLegRequest,
  ApplyQuoteToOrderRequest
} from '../domain/OrderEntities';
import {
  AddPaymentMethodRequest,
  PayInvoicesRequest,
  PaymentMethodAPI,
  SetDefaultPaymentMethodRequest
} from './responseEntities/PaymentEntities';
import {
  DOCUMENTS_TOKEN_KEY,
  getBillingDocumentToken,
  getDocumentToken
} from '../helpers/storageHelpers';
import {
  PodsReadyAccessTokenClaims,
  PodsReadyCreateAccountRequest,
  PodsReadyCreateAccountResponse,
  StartPodsReadySession
} from './responseEntities/PodsReadyEntities';
import { isPodsReadyLocation } from '../helpers/podsReadyHelpers';

export const useMyPodsService = () => {
  const axiosInstance = useContext(ApigeeContext);
  const location = useLocation();

  const getPoetUrl = (version: string, controllerName: string): string => {
    if (isPodsReadyLocation(location)) {
      return 'v1/pods-ready';
    }
    return `${version}/${controllerName}`;
  };

  async function authorizationEntrypoint(): Promise<EntryPointResult> {
    const res = await axiosInstance.get('v1/authorization/entrypoint');

    return res.data;
  }

  async function refreshSession(): Promise<RefreshSessionClaims> {
    const res = await axiosInstance.post('v1/session/refresh');
    return res.data;
  }

  async function acceptRentalAgreement(request: AcceptRentalAgreementRequest): Promise<void> {
    const url = `${getPoetUrl('v1', 'document')}/accept-rental-agreement`;
    const res = await axiosInstance.patch(url, request);
    return res.data;
  }

  async function acceptMultipleRentalAgreements(
    requests: AcceptRentalAgreementRequest[]
  ): Promise<void> {
    const url = `${getPoetUrl('v1', 'document')}/accept-rental-agreements`;
    const res = await axiosInstance.patch(url, requests);
    return res.data;
  }

  async function getCustomer(): Promise<Customer> {
    let url = 'v1/customer';
    if (isPodsReadyLocation(location)) {
      url = 'v1/pods-ready/customer';
    }
    const res = await axiosInstance.get(url);
    return res.data;
  }

  async function getCustomerOrders(): Promise<OrderAPI[]> {
    const url = `${getPoetUrl('v1', 'customer')}/orders`;
    const res = await axiosInstance.get(url);
    return res.data;
  }

  async function challengeEmail(request: ChallengeEmailRequest): Promise<void> {
    return axiosInstance.put('v1/customer/email/challenge', request);
  }

  async function verifyChallenge(request: VerifyChallengeRequest): Promise<void> {
    return axiosInstance.post('v1/customer/email/verify-challenge', request);
  }

  async function updateEmail(request: UpdateEmailRequest): Promise<void> {
    return axiosInstance.patch('v1/customer/email', request);
  }

  async function updatePassword(request: UpdatePasswordRequest): Promise<UpdatePasswordResponse> {
    const res = await axiosInstance.patch(`v1/customer/change-password`, request);
    return res.data;
  }

  async function updatePrimaryPhone(request: UpdatePrimaryPhoneRequest): Promise<void> {
    return axiosInstance.patch('v1/customer/primary-phone', request);
  }

  async function updateSecondaryPhone(request: UpdateSecondaryPhoneRequest): Promise<void> {
    return axiosInstance.patch('v1/customer/secondary-phone', request);
  }

  async function updateBillingAddress(request: UpdateBillingAddressRequest): Promise<void> {
    return axiosInstance.patch('v1/customer/billing-address', request);
  }

  async function updateShippingAddress(request: UpdateShippingAddressRequest): Promise<void> {
    return axiosInstance.patch('v1/customer/shipping-address', request);
  }

  async function updateSmsOptIn(request: UpdateSmsOptInRequest): Promise<void> {
    return axiosInstance.patch('v1/customer/sms-opt-in', request);
  }

  async function updatePin(request: UpdatePinRequest): Promise<void> {
    return axiosInstance.patch('v1/customer/pin', request);
  }

  async function getSasUrl(docRef: string, isBillingDocument: boolean): Promise<string> {
    const response = await axiosInstance.get(`v1/document/sas-url?docRef=${docRef}`, {
      ...headersForDocuments(isBillingDocument)
    });
    return response.data;
  }

  async function getDocuments(): Promise<Documents> {
    const response = await axiosInstance.get('v1/document/documents', {
      ...headersForDocuments(false)
    });

    return response.data;
  }

  async function getOrderDocuments(): Promise<OrderDocuments> {
    const url = `${getPoetUrl('v2', 'document')}/order-documents`;
    const response = await axiosInstance.get(url, {
      ...headersForDocuments(false)
    });

    return response.data;
  }

  async function getCustomerDocuments(): Promise<CustomerDocuments> {
    const response = await axiosInstance.get('v2/document/customer-documents', {
      ...headersForDocuments(false)
    });

    return response.data;
  }

  async function getBillingInformation(): Promise<BillingResponse> {
    const res = await axiosInstance.get(`v1/billing/customer/billing-information`, {
      ...headersForDocuments(true)
    });
    return res.data;
  }

  async function addPaymentMethod(request: AddPaymentMethodRequest): Promise<void> {
    const res = await axiosInstance.post(`/v1/payment/payment-method`, request);
    return res.data;
  }

  async function createCustomStatement(request: CustomStatementRequest): Promise<CustomStatement> {
    const response = await axiosInstance.post(`v1/document/custom-statement`, request, {
      responseType: 'blob'
    });
    const blob = new Blob([response.data], { type: response.headers['content-type'] });
    return {
      content: URL.createObjectURL(blob),
      dateRange: formatCustomRange(request.startDate, request.endDate)
    };
  }

  async function signFnpsWaiver(request: SignFnpsWaiverRequest): Promise<void> {
    return axiosInstance.post('v1/document/sign-fnps', request);
  }

  async function signMothAgreement(request: SignMothAgreementRequest): Promise<string> {
    const url = `${getPoetUrl('v1', 'document')}/sign-moth-agreement`;
    const response = await axiosInstance.post(url, request, { responseType: 'blob' });
    const blob = new Blob([response.data], { type: response.headers['content-type'] });
    return URL.createObjectURL(blob);
  }

  async function createAccessToken(claims: AccessTokenClaims): Promise<string> {
    const res = await axiosInstance.post('/v1/debug/access-token', claims);
    return res.data;
  }

  async function getContainerAvailability(
    request: ContainerAvailabilityRequest
  ): Promise<ContainerAvailabilityResponse> {
    const res = await axiosInstance.post('/v1/availability/container-availability', request);
    return res.data;
  }

  async function acceptInitialDeliveryPlacement(orderId: string): Promise<void> {
    await axiosInstance.patch(`/v1/order/${orderId}/accept-initial-delivery-placement`);
  }

  async function updateMoveLeg(request: UpdateMoveLegRequest): Promise<UpdateMoveLegResponse> {
    const res = await axiosInstance.post('/v1/order/update-move-leg', request);
    return res.data;
  }

  async function addWarehouseVisit(request: AddWarehouseRequest): Promise<UpdateMoveLegResponse> {
    const res = await axiosInstance.post('/v1/order/add-warehouse-visit', request);
    return res.data;
  }

  async function isSameServiceArea(request: SameServiceAreaRequest): Promise<boolean> {
    const res = await axiosInstance.post('/v1/order/is-same-service-area', request);
    return res.data;
  }

  async function getPaymentMethods(): Promise<PaymentMethodAPI[]> {
    const res = await axiosInstance.get(`/v1/payment/methods`);
    return res.data;
  }

  async function setDefaultPaymentMethod(request: SetDefaultPaymentMethodRequest): Promise<void> {
    return axiosInstance.patch('v1/payment/set-default-payment-method', request);
  }

  async function payInvoices(request: PayInvoicesRequest): Promise<void> {
    return axiosInstance.post('v1/payment/pay-invoices', request);
  }

  const headersForDocuments = (isBillingDocument: boolean) => {
    const token = isBillingDocument ? getBillingDocumentToken() : getDocumentToken();
    return token ? { headers: { [`${DOCUMENTS_TOKEN_KEY}`]: token } } : undefined;
  };

  async function startPodsReadySession(
    tokenRequest: StartPodsReadySession
  ): Promise<PodsReadyAccessTokenClaims> {
    const res = await axiosInstance.post('/v1/pods-ready/session', tokenRequest);
    return res.data;
  }

  async function createPodsReadyAccount(
    request: PodsReadyCreateAccountRequest
  ): Promise<PodsReadyCreateAccountResponse> {
    const res = await axiosInstance.post('v1/pods-ready/create-account', request);
    return res.data;
  }

  async function getPodsReadyOrders(): Promise<OrderAPI[]> {
    const res = await axiosInstance.get('v1/pods-ready/orders');
    return res.data;
  }

  async function getPodsReadyOrderDocuments(): Promise<Documents> {
    const res = await axiosInstance.get('v1/pods-ready/order-documents', {
      ...headersForDocuments(false)
    });
    return res.data;
  }

  async function applyQuoteToOrder(
    request: ApplyQuoteToOrderRequest
  ): Promise<ApplyQuoteToOrderResponse> {
    const res = await axiosInstance.post('/v1/order/apply-quote-to-order', request);
    return res.data;
  }

  return {
    authorizationEntrypoint,
    refreshSession,
    acceptRentalAgreement,
    acceptMultipleRentalAgreements,
    createAccessToken,
    getCustomer,
    getCustomerOrders,
    updateEmail,
    challengeEmail,
    verifyChallenge,
    updatePassword,
    updatePrimaryPhone,
    updateSecondaryPhone,
    updateBillingAddress,
    updateShippingAddress,
    updateSmsOptIn,
    updatePin,
    getSasUrl,
    getBillingInformation,
    createCustomStatement,
    getContainerAvailability,
    acceptInitialDeliveryPlacement,
    updateMoveLeg,
    addWarehouseVisit,
    isSameServiceArea,
    getDocuments,
    getOrderDocuments,
    getCustomerDocuments,
    getPaymentMethods,
    setDefaultPaymentMethod,
    payInvoices,
    addPaymentMethod,
    signFnpsWaiver,
    signMothAgreement,
    startPodsReadySession,
    createPodsReadyAccount,
    getPodsReadyOrders,
    getPodsReadyOrderDocuments,
    applyQuoteToOrder
  };
};
