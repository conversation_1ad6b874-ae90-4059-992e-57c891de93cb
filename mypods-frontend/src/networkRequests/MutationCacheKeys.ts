export enum MutationCacheKeys {
  VERIFY_CHALLENGE_KEY = 'verify-challenge',
  ACCEPT_RA_KEY = 'accept-rental-agreement',
  ACCEPT_MULTIPLE_RA_KEY = 'accept-rental-agreements',
  ACCEPT_INITIAL_DELIVERY_PLACEMENT_KEY = 'accept-initial-delivery-placement',
  ADD_PAYMENT_METHOD_KEY = 'add-payment-method',
  ADD_WAREHOUSE_KEY = 'add-warehouse-key',
  CHALLENGE_EMAIL_KEY = 'challenge-email',
  CREATE_CUSTOM_STATEMENT_KEY = 'create-custom-statement',
  CREATE_PODS_READY_ACCOUNT_KEY = 'create-pods-ready-account',
  CONTAINER_AVAILABILITY_KEY = 'container-availability',
  GET_FILE_KEY = 'get-file',
  GET_RENTAL_AGREEMENT_KEY = 'get-rental-agreement',
  GET_SAS_URL_KEY = 'get-sas-url',
  IS_SAME_SERVICE_AREA_KEY = 'is-same-service-area',
  MAKE_PAYMENT_KEY = 'make-payment',
  SET_DEFAULT_PAYMENT_METHOD_KEY = 'set-default-payment-method',
  SIGN_FNPS_WAIVER_KEY = 'sign-fnps-waiver',
  SIGN_MOTH_AGREEMENT_KEY = 'sign-moth-agreement',
  UPDATE_BILLING_ADDRESS_KEY = 'update-billing-address',
  UPDATE_SHIPPING_ADDRESS_KEY = 'update-shipping-address',
  UPDATE_EMAIL_KEY = 'update-email',
  UPDATE_MOVE_LEG_KEY = 'update-move-leg',
  UPDATE_PASSWORD_KEY = 'update-password',
  UPDATE_PIN_KEY = 'update-pin',
  UPDATE_PRIMARY_PHONE_KEY = 'update-primary-phone',
  UPDATE_SECONDARY_PHONE_KEY = 'update-secondary-phone',
  UPDATE_SMS_OPT_IN_KEY = 'update-sms-opt-in',
  APPLY_QUOTE_TO_ORDER = 'apply-quote-to-order'
}
