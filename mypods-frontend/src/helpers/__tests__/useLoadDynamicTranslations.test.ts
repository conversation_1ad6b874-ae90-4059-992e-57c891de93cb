import { vi } from 'vitest';
import { useLoadDynamicTranslations } from '../useLoadDynamicTranslations';
import SplitIO from '@splitsoftware/splitio/types/splitio';

describe('useLoadDynamicTranslations', () => {
  const addResource = vi.fn();
  const flagName = 'flagName';
  const dictionary = {
    addResource: addResource
  };

  it('adds translations', () => {
    const usTitle = 'usTitle';
    const usBody = 'usBody';
    const caTitle = 'caTitle';
    const caBody = 'caBody';
    const config = {
      en_us: {
        title: usTitle,
        body: usBody
      },
      fr_ca: {
        title: caTitle,
        body: caBody
      }
    };
    const treatments = {
      flagName: {
        config: JSON.stringify(config)
      } as SplitIO.TreatmentWithConfig
    };

    useLoadDynamicTranslations(flagName, treatments, dictionary);

    expect(addResource).toBeCalledWith('en_us', 'translation', 'flagName.title', usTitle);
    expect(addResource).toBeCalledWith('en_us', 'translation', 'flagName.body', usBody);
    expect(addResource).toBeCalledWith('fr_ca', 'translation', 'flagName.title', caTitle);
    expect(addResource).toBeCalledWith('fr_ca', 'translation', 'flagName.body', caBody);
  });

  it('returns false with invalid json', () => {
    const treatments = {
      flagName: {
        config: 'invalid json for fun and profit'
      } as SplitIO.TreatmentWithConfig
    };

    expect(useLoadDynamicTranslations(flagName, treatments, dictionary)).toBeFalsy();
  });

  it('returns false with null config', () => {
    const treatments = {
      flagName: {
        config: null
      } as SplitIO.TreatmentWithConfig
    };

    expect(useLoadDynamicTranslations(flagName, treatments, dictionary)).toBeFalsy();
  });
});
