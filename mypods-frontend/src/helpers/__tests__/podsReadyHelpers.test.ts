import { getBaseUrl } from '../podsReadyHelpers';

describe('getBaseUrl', () => {
  const baseUrl = 'v1/legacy';

  describe('not pods ready', () => {
    const notPodsReady = false;

    it('normal routes should return baseUrl/legacyControllerName', () => {
      const actual = getBaseUrl(baseUrl, notPodsReady, '/document');
      expect(actual).toEqual('v1/legacy/document');
    });

    it('passing blank for index style routes (like get customer) should return the baseurl', () => {
      const actual = `${getBaseUrl(baseUrl, notPodsReady, '')}/customer`;
      expect(actual).toEqual('v1/legacy/customer');
    });
  });

  describe('pods ready', () => {
    const podsReady = true;

    it('normal routes should replace the controller with pods-ready', () => {
      const actual = getBaseUrl(baseUrl, podsReady, '/document');
      expect(actual).toEqual('v1/legacy/pods-ready');
    });

    it('passing blank for index style routes (like get customer) should return pods-ready', () => {
      const actual = `${getBaseUrl(baseUrl, podsReady, '')}/customer`;
      expect(actual).toEqual('v1/legacy/pods-ready/customer');
    });
  });
});
