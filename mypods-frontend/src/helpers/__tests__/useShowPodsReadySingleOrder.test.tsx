import { renderHook } from '@testing-library/react';
import {
  createOrderDocument,
  mockIsPodsReadySingleOrderEnabled
} from '../../testUtils/MyPodsFactories';
import {
  mockGetOrderDocuments,
  mockGetPodsReadyOrderDocuments,
  mockOrderHasSignedMothAgreement
} from '../../../setupTests';
import { useShowPodsReadySingleOrder } from '../useShowPodsReadySingleOrder';
import { runPendingPromises, testQueryClient } from '../../testUtils/RenderHelpers';
import { QueryClientProvider } from '@tanstack/react-query';
import { Suspense } from 'react';
import {
  mothFormDocumentTypes,
  weightTicketDocumentTypes
} from '../../networkRequests/responseEntities/DocumentSharedSubtypes';
import { OrderDocumentAPI } from '../../networkRequests/responseEntities/DocumentApiEntities';

describe('useShowPodsReadySingleOrder', () => {
  const singleRentalAgreement = { documents: [createOrderDocument()] };
  const multiOrderDocuments = {
    documents: [
      createOrderDocument({ orderId: '1' }),
      createOrderDocument({ docType: mothFormDocumentTypes[0], orderId: '2' })
    ]
  };
  const multiDocumentsFromSameOrder = { documents: [createOrderDocument()] };
  const justMothFormDocuments: OrderDocumentAPI[] = [];
  const completedDocumentsOrder = {
    documents: [
      createOrderDocument({ docStatus: 'COMPLETED' }),
      createOrderDocument({
        docType: 'FRAGILE_AND_NON_PAVED_SURFACE_WAIVER',
        docStatus: 'COMPLETED'
      })
    ]
  };
  mothFormDocumentTypes.forEach((docType: string) => {
    multiDocumentsFromSameOrder.documents.push(createOrderDocument({ docType: docType }));
    justMothFormDocuments.push(createOrderDocument({ docType: docType }));
    completedDocumentsOrder.documents.push(
      createOrderDocument({ docType: docType, docStatus: 'COMPLETED' })
    );
  });
  const justMothFormDocumentsOrders = { documents: justMothFormDocuments };
  const weightTicketsDocuments: OrderDocumentAPI[] = [];
  weightTicketDocumentTypes.forEach((docType: string) => {
    weightTicketsDocuments.push(createOrderDocument({ docType: docType }));
  });
  const weightTicketsDocumentOrders = { documents: weightTicketsDocuments };
  const noDocumentOrders = { documents: [] };

  const renderGetOrderDocuments = async () => {
    // @ts-ignore
    const wrapper = ({ children }) => (
      <QueryClientProvider client={testQueryClient()}>
        <Suspense fallback={'SUSPENSE_FALLBACK'}>{children}</Suspense>
      </QueryClientProvider>
    );
    const { result } = renderHook(() => useShowPodsReadySingleOrder(), { wrapper });
    await runPendingPromises();
    return result.current.showPodsReadySingleOrder;
  };

  describe('isPodsReadySingleOrderEnabled true and', () => {
    beforeEach(() => {
      mockIsPodsReadySingleOrderEnabled.mockReturnValue(true);
      mockOrderHasSignedMothAgreement.mockReturnValue(false);
    });

    it('has one outstanding rental agreement returns true', async () => {
      mockGetOrderDocuments.mockResolvedValue(singleRentalAgreement);

      const actual = await renderGetOrderDocuments();

      expect(actual).toBe(true);
    });

    it('has more than one outstanding rental agreement returns false', async () => {
      mockGetOrderDocuments.mockResolvedValue(multiOrderDocuments);

      const actual = await renderGetOrderDocuments();

      expect(actual).toBe(false);
    });

    it('has more than one order doc but all from the same doc return true', async () => {
      mockGetOrderDocuments.mockResolvedValue(multiDocumentsFromSameOrder);

      const actual = await renderGetOrderDocuments();

      expect(actual).toBe(true);
    });

    it('has only moth documents returns true', async () => {
      mockGetOrderDocuments.mockResolvedValue(justMothFormDocumentsOrders);

      const actual = await renderGetOrderDocuments();

      expect(actual).toBe(true);
    });

    it('has only previously signed moth documents returns false', async () => {
      mockOrderHasSignedMothAgreement.mockReturnValue(true);
      mockGetOrderDocuments.mockResolvedValue(justMothFormDocumentsOrders);

      const actual = await renderGetOrderDocuments();

      expect(actual).toBe(false);
    });

    it('has no documents returns false', async () => {
      mockGetOrderDocuments.mockResolvedValue(noDocumentOrders);

      const actual = await renderGetOrderDocuments();

      expect(actual).toBe(false);
    });

    it('only has weight ticket documents returns false', async () => {
      mockGetOrderDocuments.mockResolvedValue(weightTicketsDocumentOrders);

      const actual = await renderGetOrderDocuments();

      expect(actual).toBe(false);
    });

    it('has no outstanding documents returns false', async () => {
      mockGetOrderDocuments.mockResolvedValue(completedDocumentsOrder);

      const actual = await renderGetOrderDocuments();

      expect(actual).toBe(false);
    });
  });

  describe('isPodsReadySingleOrderEnabled false and', () => {
    beforeEach(() => {
      mockIsPodsReadySingleOrderEnabled.mockReturnValue(false);
    });

    it('has outstanding document returns false', async () => {
      mockGetOrderDocuments.mockResolvedValue(singleRentalAgreement);

      const actual = await renderGetOrderDocuments();

      expect(actual).toBe(false);
    });

    it('has no documents returns false', async () => {
      mockGetOrderDocuments.mockResolvedValue(noDocumentOrders);

      const actual = await renderGetOrderDocuments();

      expect(actual).toBe(false);
    });
  });
});
