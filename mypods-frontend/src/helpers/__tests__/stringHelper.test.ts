import { toCamelCase, toTitleCase } from '../stringHelper';

describe('getContextForMoveLegs', () => {
  it.each([
    ['INITIAL_DELIVERY', 'initialDelivery'],
    ['SELF_INITIAL_DELIVERY', 'selfInitialDelivery'],
    ['PICKUP', 'pickup'],
    ['MOVE', 'move'],
    ['VISIT_CONTAINER', 'visitContainer'],
    ['REDELIVERY', 'redelivery'],
    ['WAREHOUSE_TO_WAREHOUSE', 'warehouseToWarehouse'],
    ['SELF_FINAL_PICKUP', 'selfFinalPickup'],
    ['FINAL_PICKUP', 'finalPickup'],
    ['', '']
  ])("format move leg type from '%s' to '%s'", (input, expectedOutput) => {
    expect(toCamelCase(input)).toEqual(expectedOutput);
  });
});

describe('toTitleCase', () => {
  it.each([
    ['Simple', 'Simple'],
    ['simple', 'Simple'],
    ['SIMPLE', 'Simple'],
    ['HERNDON', 'Herndon'],
    ['san francisco', '<PERSON> Francisco'],
    [undefined, ''],
    [null, '']
  ])("formats '%s' to '%s'", (input, expectedOutput) => {
    expect(toTitleCase(input)).toBe(expectedOutput);
  });
});
