import { parseJwt } from '../jwtHelper';

describe('decodeJWTToken', () => {
  it('should parse a valid JWT token', () => {
    const token =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
    const decoded = parseJwt(token);
    expect(decoded).toEqual({
      sub: '1234567890',
      name: '<PERSON>',
      iat: 1516239022
    });
  });

  it('should return null for an invalid JWT token', () => {
    const token = 'invalid-token';
    const decoded = parseJwt(token);
    expect(decoded).toBeNull();
  });
});
