import { TranslationKeys } from '../../locales/TranslationKeys';

export const isNullOrEmpty = (value: string | undefined | null) =>
  value == null || value.length === 0;
export const isNotNullOrEmpty = (value: string | undefined | null) => !isNullOrEmpty(value);

const emailRegex =
  // eslint-disable-next-line
  /^(?:[^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*|".+")@(\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}]|([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})$|^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.^[a-z]{2,3})+$/;
const pinRegex = /^[0-9]{4}$/;
const lowercaseRegex = /[a-z]/;
const uppercaseRegex = /[A-Z]/;
const specialCharacterRegex = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/;

const sanitizePhone = (phone: string): string => phone.replace(/[\s{\-}]/g, '');

export const isValidPhone = (value: string) => {
  const numbers = sanitizePhone(value);
  return numbers.length === 10 && /^\d+$/.test(numbers);
};

export const isValidEmail = (email: string) => emailRegex.test(email);

export const isValidPin = (pin: string) => pinRegex.test(pin);

function isValidCode(value: string | undefined) {
  const regex = /^[A-Z]+$/;
  if (isNullOrEmpty(value)) return false;
  if (!regex.test(value!!)) return false;
  return value!!.length === 2;
}

function isPostalCodeValid(value: string | undefined) {
  const regex = /^((\d{5}-\d{4})|(\d{5})|([A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d))$/;
  if (isNullOrEmpty(value)) return false;
  if (!regex.test(value!!)) return false;
  return true;
}

export const isValidState = (value: string | undefined) => isValidCode(value);
export const isValidCountryCode = (value: string | undefined) => isValidCode(value);
export const isValidPostalCode = (value: string | undefined) => isPostalCodeValid(value);

export type Validity = 'valid' | 'invalid' | 'default';

export const byValidity = <T>(
  validity: Validity,
  options: { valid: T; invalid: T; default: T }
): T => options[validity];

// eslint-disable-next-line no-restricted-globals
export const isTextNumeric = (text: string) => !isNaN(Number(text)) && !isNaN(parseFloat(text));

export interface PasswordValidationRule {
  i18nKey: string;
  errorI18nKey: string;
  validity: Validity;
}

export interface PasswordValidationResults {
  rules: PasswordValidationRule[];
  isValid: boolean;
}

export const useValidatePassword = (newPassword?: string): PasswordValidationResults => {
  const validateRegex = (regex: RegExp) => (value: string) => regex.test(value);
  const validate = (validation: (value: string) => boolean, value?: string): Validity => {
    if (!value) {
      return 'default';
    }
    return validation(value) ? 'valid' : 'invalid';
  };

  const hasEightCharacters = {
    i18nKey: TranslationKeys.AccountPage.AccountInfo.Password.Rules.LENGTH,
    errorI18nKey: TranslationKeys.CommonComponents.Input.Error.Validation.Password.LENGTH,
    validity: validate((value) => value.length >= 8, newPassword)
  };

  const hasLowercase = {
    i18nKey: TranslationKeys.AccountPage.AccountInfo.Password.Rules.LOWERCASE,
    errorI18nKey: TranslationKeys.CommonComponents.Input.Error.Validation.Password.LOWERCASE,
    validity: validate(validateRegex(lowercaseRegex), newPassword)
  };
  const hasUppercase = {
    i18nKey: TranslationKeys.AccountPage.AccountInfo.Password.Rules.UPPERCASE,
    errorI18nKey: TranslationKeys.CommonComponents.Input.Error.Validation.Password.UPPERCASE,
    validity: validate(validateRegex(uppercaseRegex), newPassword)
  };
  const hasSpecialCharacter = {
    i18nKey: TranslationKeys.AccountPage.AccountInfo.Password.Rules.SPECIAL_CHARACTER,
    errorI18nKey:
      TranslationKeys.CommonComponents.Input.Error.Validation.Password.SPECIAL_CHARACTER,
    validity: validate(validateRegex(specialCharacterRegex), newPassword)
  };

  const rules = [hasEightCharacters, hasLowercase, hasUppercase, hasSpecialCharacter];

  return {
    rules,
    isValid: rules.every(({ validity }) => validity === 'valid')
  };
};
