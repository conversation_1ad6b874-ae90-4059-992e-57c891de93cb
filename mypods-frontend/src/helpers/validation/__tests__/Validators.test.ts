import { isValidPostalCode } from '../Validators';

describe('postal code validation ', () => {
  it('US valid postal code', () => {
    expect(isValidPostalCode('32615')).toEqual(true);
  });

  it('US valid postal code + 4', () => {
    expect(isValidPostalCode('32615-1205')).toEqual(true);
  });

  it('US invalid postal code', () => {
    expect(isValidPostalCode('AA615')).toEqual(false);
  });

  it('Canada valid postal code', () => {
    expect(isValidPostalCode('M5V 3L9')).toEqual(true);
  });

  it('Canada valid postal code without space', () => {
    expect(isValidPostalCode('M5V3L9')).toEqual(true);
  });

  it('Canada invalid postal code', () => {
    expect(isValidPostalCode('M5V3L9AS')).toEqual(false);
  });
});
