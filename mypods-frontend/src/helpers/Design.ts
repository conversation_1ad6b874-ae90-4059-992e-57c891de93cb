// generated by `npm run design:build`; do not edit directly
// add desired design changes to design/inputs/overrides.json
export const Design = {
  Primitives: {
    Color: {
      Bkgd: {
        bkgd: '#FFFFFF'
      },
      Red: {
        darkRed: '#B11426',
        classicRed: '#CE202F',
        maraschino: '#E81C2D'
      },
      Blue: {
        darkOasis: '#00649F',
        oasis: '#007DB8',
        oasis100: '#007DB81A',
        lightOasis: '#03A2E2'
      },
      NeutralLight: {
        cloud: '#EBEBEB',
        breeze: '#D4E9E6',
        salt: '#EBF4F3',
        white: '#FFFFFF'
      },
      NeutralDark: {
        charcoal: '#3D3D3D',
        smoke: '#989A9B',
        black: '#000000',
        midnight: '#083544'
      },
      Semantic: {
        success: '#1C843C',
        error: '#DC3333',
        warning: '#B84000'
      }
    },
    Font: {
      Family: {
        primary:
          '"Nunito Sans", "Open Sans",-apple-system,<PERSON>linkMacSystem<PERSON>ont,<PERSON><PERSON>,Se<PERSON>e UI,system-ui,Arial,sans-serif',
        secondary:
          '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif'
      },
      Size: {
        BodyUniversal: {
          lg: 18,
          md: 16,
          sm: 14,
          xs: 12,
          xxs: 10
        },
        Heading: {
          Desktop: {
            xxl: 40,
            xl: 32,
            lg: 28,
            md: 24,
            sm: 20,
            xs: 18
          },
          Mobile: {
            xxl: 32,
            xl: 24,
            lg: 20,
            md: 18,
            sm: 16,
            xs: 14
          }
        }
      },
      Weight: {
        black: 900,
        bold: 700,
        'semi-bold': 600,
        regular: 400
      },
      LineHeight: {
        bodyUniversal: 1.5,
        mobile: 1.2,
        Desktop: {
          standard: 1.2,
          xl: 1.1875,
          xxl: 1.2
        }
      },
      LetterSpacing: {
        default: '0'
      }
    },
    Spacing: {
      xxxs: '0.25rem',
      xxs: '0.5rem',
      xs: '0.75rem',
      sm: '1rem',
      md: '1.5rem',
      lg: '2.5rem',
      xl: '5rem',
      xxl: '7.5rem',
      lgPlus: '3rem'
    }
  },
  Alias: {
    Text: {
      BodyUniversal: {
        Lg: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 18,
          fontWeight: 400,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        LgSemi: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 18,
          fontWeight: 600,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        LgBold: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 18,
          fontWeight: 700,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        Md: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 16,
          fontWeight: 400,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        MdSemi: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 16,
          fontWeight: 600,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        MdBold: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 16,
          fontWeight: 700,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        Sm: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 14,
          fontWeight: 400,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        SmSemi: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 14,
          fontWeight: 600,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        SmBold: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 14,
          fontWeight: 700,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        Xs: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 12,
          fontWeight: 400,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        XsSemi: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 12,
          fontWeight: 600,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        XsBold: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 12,
          fontWeight: 700,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        Xxs: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 10,
          fontWeight: 400,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        XxsSemi: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 10,
          fontWeight: 600,
          letterSpacing: '0',
          lineHeight: 1.5
        },
        XxsBold: {
          fontFamily:
            '"Open Sans", "Nunito Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
          fontSize: 10,
          fontWeight: 700,
          letterSpacing: '0',
          lineHeight: 1.5
        }
      },
      Heading: {
        Desktop: {
          Xxl: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 40,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          },
          Xl: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 32,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.1875
          },
          Lg: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 28,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          },
          Md: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 24,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          },
          Sm: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 20,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          },
          Xs: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 18,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          }
        },
        Mobile: {
          Xxl: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 32,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          },
          Xl: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 24,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          },
          Lg: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 20,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          },
          Md: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 18,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          },
          Sm: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 16,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          },
          Xs: {
            fontFamily:
              '"Nunito Sans", "Open Sans",-apple-system,BlinkMacSystemFont,Roboto,Segoe UI,system-ui,Arial,sans-serif',
            fontSize: 14,
            fontWeight: 900,
            letterSpacing: '0',
            lineHeight: 1.2
          }
        }
      },
      Signature: {
        Regular: {
          fontFamily: '"Playball", serif;',
          fontSize: 18,
          fontWeight: 400,
          letterSpacing: '0',
          lineHeight: 1.5
        }
      }
    },
    Color: {
      primary100: '#FDE8EA',
      primary200: '#F8BBC0',
      primary300: '#F17781',
      primary400: '#EF606C',
      primary500: '#CE202F',
      primary600: '#B11426',
      primary700: '#9B0000',
      primary800: '#790F17',
      primary900: '#5D0B12',
      secondary100: '#E5F2F8',
      secondary200: '#BFDEED',
      secondary300: '#7FBDDB',
      secondary400: '#409ECA',
      secondary500: '#007DB8',
      secondary600: '#006493',
      secondary700: '#005178',
      secondary800: '#003F5C',
      secondary900: '#00324A',
      neutral100: '#FFFFFF',
      neutral200: '#EBEBEB',
      neutral300: '#CBCBCB',
      neutral400: '#B1B1B1',
      neutral500: '#979797',
      neutral600: '#7D7D7D',
      neutral700: '#636363',
      neutral800: '#494949',
      neutral900: '#2B2B2B',
      accent100: '#F2FAFE',
      accent200: '#EBF3F6',
      accent300: '#EBF4F3',
      accent400: '#D4E9E6',
      accent500: 'notdefined',
      accent600: 'notdefined',
      accent700: 'notdefined',
      accent800: '#224F5E',
      accent900: '#083544',
      infoLight: '#E8F5FC',
      infoMain: '#0288D1',
      infoDark: '#014D89',
      successLight: '#EDF7EE',
      successMain: '#1C843C',
      successDark: '#1B5E20',
      errorLight: '#FAEEED',
      errorMain: '#DC3333',
      errorDark: '#731717',
      warningLight: '#FFF5E5',
      warningMain: '#B84000',
      warningDark: '#993600',
      warningDarkest: '#802D01',
      neutral150: '#FAFAFA',
      neutral175: '#F7F7F7'
    },
    Sizing: {
      leftNavDrawerWidth: '14.5rem',
      rightDrawerWidth: '30.625rem'
    }
  },
  Element: {
    _comment: 'The element level is currently not used as of Sept 2023.'
  }
};
