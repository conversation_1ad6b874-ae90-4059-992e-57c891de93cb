import { MY_PODS_INTRO_SPLASH_SCREEN_ENABLED, useFeatureFlags } from './useFeatureFlags';

export const useShowMyPodsIntroSplashScreen = () => {
  const useShouldShow = (): boolean => {
    const { isMyPodsIntroSplashScreenEnabled } = useFeatureFlags([
      MY_PODS_INTRO_SPLASH_SCREEN_ENABLED
    ]);
    const isSplashScreenEnabled = isMyPodsIntroSplashScreenEnabled();
    const neverViewed = localStorage.getItem(MY_PODS_INTRO_SPLASH_SCREEN_SEEN_KEY) === null;
    return isSplashScreenEnabled && neverViewed;
  };

  const setShown = () => {
    localStorage.setItem(MY_PODS_INTRO_SPLASH_SCREEN_SEEN_KEY, 'true');
  };

  return {
    useShouldShow,
    setShown
  };
};

export const MY_PODS_INTRO_SPLASH_SCREEN_SEEN_KEY = 'my_pods_intro_splash_screen_seen';
