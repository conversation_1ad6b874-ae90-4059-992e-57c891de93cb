import isEmpty from 'lodash/isEmpty';
import {
  EntryPointResult,
  findIfEntryPointHasMothForm
} from '../networkRequests/responseEntities/AuthorizationEntities';
import {
  getOrdersWithSignedMothAgreements,
  setOrdersWithSignedMothAgreements
} from './storageHelpers';

export const unsignedMothAgreements = (entryPointResult: EntryPointResult) => {
  const signedAgreements = getOrdersWithSignedMothAgreements();
  if (signedAgreements == null) return entryPointResult.outstandingMothAgreements;

  return entryPointResult.outstandingMothAgreements.filter(
    (it) => !signedAgreements.includes(it.orderId)
  );
};

export const addSignedMothOrderId = (orderId: string) => {
  const signedAgreements = getOrdersWithSignedMothAgreements();

  if (signedAgreements == null) {
    setOrdersWithSignedMothAgreements([orderId]);
  } else {
    setOrdersWithSignedMothAgreements(signedAgreements.concat(orderId));
  }
};

export const taskStatus = (entryPointResult: EntryPointResult) => {
  const { outstandingMothAgreements, outstandingRentalAgreements } = entryPointResult;
  const orderId = outstandingRentalAgreements[0]?.orderId ?? '';
  const hasMothForm = findIfEntryPointHasMothForm(entryPointResult, orderId);
  let completedTasks = 1; // order task is always complete
  if (isEmpty(outstandingRentalAgreements)) {
    completedTasks += 1;
  }
  let totalTasks = 2; // rental agreement + order task
  if (hasMothForm) {
    totalTasks += 1;
    if (isEmpty(outstandingMothAgreements)) {
      completedTasks += 1;
    }
  }
  return { totalTasks, completedTasks };
};
