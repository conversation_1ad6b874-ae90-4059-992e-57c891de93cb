import { useState } from 'react';
import { PODS_READY_SINGLE_ORDER_ENABLED, useFeatureFlags } from '../useFeatureFlags';
import { useEntryPointContext } from '../../context/EntryPointContext';
import {
  OutstandingMothAgreement,
  OutstandingRentalAgreement
} from '../../networkRequests/responseEntities/AuthorizationEntities';
import { orderHasSignedMothAgreement } from '../storageHelpers';

export const useLegacyShowPodsReadySingleOrder = () => {
  const [isInPodsReadyFlow, setIsInPodsReadyFlow] = useState<boolean>(true);
  const {
    entryPointResult: { outstandingRentalAgreements, outstandingMothAgreements, isFetching }
  } = useEntryPointContext();
  const { isPodsReadySingleOrderEnabled } = useFeatureFlags([PODS_READY_SINGLE_ORDER_ENABLED]);
  const uniqueOrderIds = new Set();

  outstandingRentalAgreements.forEach((doc: OutstandingRentalAgreement) => {
    uniqueOrderIds.add(doc.orderId);
  });

  outstandingMothAgreements.forEach((doc: OutstandingMothAgreement) => {
    if (!orderHasSignedMothAgreement(doc.orderId)) {
      uniqueOrderIds.add(doc.orderId);
    }
  });
  const preventSomeoneFromSwitchingToPodsReadyAfterGettingNormalHomePage = () => {
    if (isInPodsReadyFlow) {
      setIsInPodsReadyFlow(false);
    }
  };
  const hasExactlyOneOpenOrder = uniqueOrderIds.size === 1;
  const podsReadyEnabled = isPodsReadySingleOrderEnabled();
  const currentlyLooksLikePodsReady = podsReadyEnabled && hasExactlyOneOpenOrder;
  if (uniqueOrderIds.size > 1) {
    // Without this fix, a user can end up in an inconsistent state where the pb-pods-ready-single-order
    // flag is set to true, but they still have two rental agreements.
    // Initially, this works correctly - the user sees the standard MyPODS homepage (specifically,
    // the rental agreement viewer), because having multiple rental agreements means they are not
    // considered a “pods-ready single order” user. However, the problem arises after the user signs
    // one of those rental agreements. At that point, only one rental agreement remains, and because the
    // flag from Split (pb-pods-ready-single-order) is set to true, the system suddenly switches them to
    // the "pods-ready single order" experience. From the user’s perspective, this is very confusing:
    // they just signed a rental agreement, but are now shown a page telling them they need to sign
    // a rental agreement. To prevent this, when a user logs in and we detect they have documents from
    // more than one order, we should explicitly save a state variable indicating they are not a
    // pods-ready single order user. This way, signing one of the agreements won’t unexpectedly
    // flip their experience mid-flow.
    preventSomeoneFromSwitchingToPodsReadyAfterGettingNormalHomePage();
  }
  return {
    isFetching,
    showPodsReadySingleOrder: isInPodsReadyFlow && currentlyLooksLikePodsReady
  };
};
