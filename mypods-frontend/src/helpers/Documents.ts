// Known document titles
export const ORDER_CONFIRMATION = 'Order Confirmation';
export const ORDER_UPDATE_CONFIRMATION = 'Order Update Confirmation';
export const LOCAL_RENTAL_AGREEMENT = 'Local Rental Agreement';
export const LOCAL_RENTAL_AGREEMENT_ELECTRONIC = 'Local Rental Agreement - Electronic Acceptance';
export const IF_RENTAL_AGREEMENT = 'IF Rental Agreement';
export const IF_RENTAL_AGREEMENT_ELECTRONIC = 'IF Rental Agreement - Electronic Acceptance';
export const NON_PAVED_SURFACE_WAIVER = 'Non-Paved Surface Waiver';
export const PLACEMENT_WAIVER = 'Placement Waiver';
export const SPONGY_MOTH_CERTIFICATION_FORM = 'Spongy Moth Certification Form';
export const ANCILLARY_ITEM_DOCUMENTATION = 'Ancillary Item Documentation';
export const CPO_COO_ADDENDUM = 'CPO-COO Addendum';
export const CPI_COO_ADDENDUM_ELECTRONIC = 'CPO-COO Addendum - Electronic Acceptance';
export const WEIGHT_TICKET_EMPTY_CONTAINER = 'Weight Ticket - Empty Container';
export const WEIGHT_TICKET_EMPTY_CONTAINER_WITH_TRUCK =
  'Weight Ticket - Empty Container - With Truck';
export const WEIGHT_TICKET_FULL_CONTAINER = 'Weight Ticket - Full Container';
export const WEIGHT_TICKET_FULL_CONTAINER_WITH_TRUCK =
  'Weight Ticket - Full Container - With Truck';
export const BOOKING_CONFIRMATION = 'Booking Confirmation (Pasha Ocean Transport)';
export const DOCK_RECEIPT = 'Dock Receipt';

// List of known document titles
export const DOCUMENT_TITLES = [
  ORDER_CONFIRMATION,
  ORDER_UPDATE_CONFIRMATION,
  LOCAL_RENTAL_AGREEMENT,
  LOCAL_RENTAL_AGREEMENT_ELECTRONIC,
  IF_RENTAL_AGREEMENT,
  IF_RENTAL_AGREEMENT_ELECTRONIC,
  NON_PAVED_SURFACE_WAIVER,
  PLACEMENT_WAIVER,
  SPONGY_MOTH_CERTIFICATION_FORM,
  ANCILLARY_ITEM_DOCUMENTATION,
  CPO_COO_ADDENDUM,
  CPI_COO_ADDENDUM_ELECTRONIC,
  WEIGHT_TICKET_EMPTY_CONTAINER,
  WEIGHT_TICKET_EMPTY_CONTAINER_WITH_TRUCK,
  WEIGHT_TICKET_FULL_CONTAINER,
  WEIGHT_TICKET_FULL_CONTAINER_WITH_TRUCK,
  BOOKING_CONFIRMATION,
  DOCK_RECEIPT
];

export const SIGNABLE_DOCUMENTS: string[] = [
  LOCAL_RENTAL_AGREEMENT,
  LOCAL_RENTAL_AGREEMENT_ELECTRONIC,
  IF_RENTAL_AGREEMENT,
  IF_RENTAL_AGREEMENT_ELECTRONIC,
  NON_PAVED_SURFACE_WAIVER,
  PLACEMENT_WAIVER,
  SPONGY_MOTH_CERTIFICATION_FORM
];

export const DOCUMENT_TITLES_RETRIEVED_BY_COMPANY_CODE: string[] = [
  LOCAL_RENTAL_AGREEMENT,
  LOCAL_RENTAL_AGREEMENT_ELECTRONIC,
  IF_RENTAL_AGREEMENT,
  IF_RENTAL_AGREEMENT_ELECTRONIC,
  CPO_COO_ADDENDUM
];
