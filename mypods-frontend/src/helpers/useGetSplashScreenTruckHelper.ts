import useMediaQuery from '@mui/system/useMediaQuery/useMediaQuery';
import { ENV_VARS } from '../environment';
import { theme } from '../PodsTheme';

export const useGetSplashScreenTruckHelper = () => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  let imageType = '';
  if (isMobile) {
    imageType = '-mobile';
  }
  return `${ENV_VARS.ASSETS_BASE_URL}/intro-splash-screen/Pods-truck-illustration${imageType}.jpg`;
};
