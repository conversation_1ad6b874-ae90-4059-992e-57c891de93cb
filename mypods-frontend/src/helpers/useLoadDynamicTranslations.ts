import SplitIO from '@splitsoftware/splitio/types/splitio';

export const useLoadDynamicTranslations = (
  flagName: string,
  treatments: SplitIO.TreatmentsWithConfig,
  dictionary: any
) => {
  const loadLanguage = (languageCode: string, language: any) => {
    Object.keys(language).forEach((translationKey) => {
      const fullKeyName = `${flagName}.${translationKey}`;
      const value = language[translationKey];
      dictionary.addResource(languageCode, 'translation', fullKeyName, value);
    });
  };

  const treatment = treatments?.[flagName];
  if (treatment?.config) {
    try {
      const translations = JSON.parse(treatment.config);
      Object.keys(translations).forEach((languageCode) => {
        loadLanguage(languageCode, translations[languageCode]);
      });
      return true;
    } catch {
      return false;
    }
  }
  return false;
};
