import cloneDeep from 'lodash/cloneDeep';
import mergeDeep from 'lodash/merge';

export function mergeStyles<T = Object>(
  ...styleClasses: Array<T | false | null | undefined>
): T | null {
  return styleClasses.reduce<T | null>((memo, styleClass) => {
    if (styleClass == null || !styleClass) {
      return memo;
    }

    if (memo == null) {
      return cloneDeep(styleClass);
    }

    return mergeDeep(memo, styleClass);
  }, null);
}
