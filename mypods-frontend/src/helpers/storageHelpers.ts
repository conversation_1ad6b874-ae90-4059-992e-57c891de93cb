export const ORDERS_WITH_SIGNED_MOTH_AGREEMENTS = 'ordersWithSignedMothAgreements';
export const WEIGHT_TICKET_BANNER = 'dismissedWeightTicketBanner';
export const DOCUMENTS_TOKEN_KEY = 'DocumentToken';
export const BILLING_DOCUMENTS_TOKEN_KEY = 'BillingDocumentToken';

export const getOrdersWithSignedMothAgreements = (): string[] | null => {
  const signedAgreements = localStorage.getItem(ORDERS_WITH_SIGNED_MOTH_AGREEMENTS);
  if (signedAgreements != null) return JSON.parse(signedAgreements) as string[];
  return null;
};

export const orderHasSignedMothAgreement = (orderId: string): boolean =>
  getOrdersWithSignedMothAgreements()?.find((signedOrderId: string) => signedOrderId === orderId) !=
  null;

export const setOrdersWithSignedMothAgreements = (orderIds: string[] | null) => {
  if (orderIds == null) localStorage.removeItem(ORDERS_WITH_SIGNED_MOTH_AGREEMENTS);
  else localStorage.setItem(ORDERS_WITH_SIGNED_MOTH_AGREEMENTS, JSON.stringify(orderIds));
};

export const getWeightTicketBanner = (): boolean | null => {
  const showWeightedTicketBanner = localStorage.getItem(WEIGHT_TICKET_BANNER);
  if (showWeightedTicketBanner != null) return JSON.parse(showWeightedTicketBanner) as boolean;
  return null;
};

export const setWeightTicketBanner = (showWeightedTicketBanner: boolean | null) => {
  if (showWeightedTicketBanner == null) localStorage.removeItem(WEIGHT_TICKET_BANNER);
  else localStorage.setItem(WEIGHT_TICKET_BANNER, JSON.stringify(showWeightedTicketBanner));
};

export const getLocalStorage = (key: string): string | null => {
  const token = localStorage.getItem(key);
  if (token != null) return token;
  return null;
};

export const setLocalStorage = (key: string, token: string | null) => {
  if (token == null) localStorage.removeItem(key);
  else localStorage.setItem(key, token);
};

export const getDocumentToken = (): string | null => getLocalStorage(DOCUMENTS_TOKEN_KEY);

export const setDocumentToken = (token: string | null) => {
  setLocalStorage(DOCUMENTS_TOKEN_KEY, token);
};

export const getBillingDocumentToken = (): string | null =>
  getLocalStorage(BILLING_DOCUMENTS_TOKEN_KEY);

export const setBillingDocumentToken = (token: string | null) => {
  setLocalStorage(BILLING_DOCUMENTS_TOKEN_KEY, token);
};
