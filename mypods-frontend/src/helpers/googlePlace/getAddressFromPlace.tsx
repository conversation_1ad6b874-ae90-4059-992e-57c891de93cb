export interface PlaceAddress {
  streetAddress?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

export const getAddressFromPlace = (place: google.maps.places.PlaceResult) => {
  const address: PlaceAddress = {};
  let streetNumber = '';
  let route = '';
  if (!place?.address_components) return address;
  const addressComponents = place?.address_components as google.maps.GeocoderAddressComponent[];
  addressComponents.forEach((component) => {
    // @ts-ignore remove once typings fixed
    const componentType = component.types[0];
    switch (componentType) {
      case 'street_number':
        streetNumber = component.long_name;
        break;
      case 'route':
        route = component.short_name;
        break;
      case 'postal_code':
        address.postalCode = component.long_name;
        break;
      case 'locality':
        address.city = component.long_name;
        break;
      case 'administrative_area_level_1':
        address.state = component.short_name;
        break;
      case 'country':
        address.country = component.short_name;
        break;
    }
  });
  address.streetAddress = route;
  if (streetNumber) address.streetAddress = `${streetNumber} ${address.streetAddress}`;
  return address;
};
