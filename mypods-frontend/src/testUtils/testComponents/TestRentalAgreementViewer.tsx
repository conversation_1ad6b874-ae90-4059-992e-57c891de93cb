import React, { screen, within } from '@testing-library/react';
import { PdfRentalAgreementViewerProps } from '../../components/RentalAgreementOnboarding/RentalAgreementDocumentViewer/PdfRentalAgreementViewer';

export const RENTAL_AGREEMENT_VIEWER = 'PdfRentalAgreementViewer';
export const RENTAL_AGREEMENT_VIEWER_BUTTON = 'Accept';
export const TestOutstandingDocumentViewerViews = {
  button: async () =>
    within(await screen.findByTestId('rental-agreement-viewer')).getByText(
      RENTAL_AGREEMENT_VIEWER_BUTTON
    ),
  pdfLink: async () =>
    within(await screen.findByTestId('rental-agreement-viewer')).getByRole('link')
};

export const TestRentalAgreementViewer = ({ onAccept, pdfUrl }: PdfRentalAgreementViewerProps) => (
  <div data-testid="rental-agreement-viewer">
    <p>{RENTAL_AGREEMENT_VIEWER}</p>
    <a href={pdfUrl}>pdf</a>
    <button type="button" onClick={onAccept}>
      {RENTAL_AGREEMENT_VIEWER_BUTTON}
    </button>
  </div>
);
