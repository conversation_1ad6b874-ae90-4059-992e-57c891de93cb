import React, { Suspense } from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { InitialEntry } from 'history';
import { MutationCache, QueryCache, QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from '@mui/material';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { theme } from '../PodsTheme';
import { NotificationProvider } from '../components/notifications/NotificationContext';
import {
  EntryPointContextState,
  EntryPointProviderWrapper,
  initialEntryPointState
} from '../context/EntryPointContext';
import {
  BillingContext,
  BillingContextProps,
  initialBillingState
} from '../context/BillingContext';
import { PaymentMethod } from '../networkRequests/responseEntities/PaymentEntities';
import { useGetCustomer } from '../networkRequests/queries/useGetCustomer';
import { IOrdersContextState, OrdersContext } from '../context/OrdersContext';
import { createCustomer } from './MyPodsFactories';
import { Customer } from '../networkRequests/responseEntities/CustomerEntities';
import { mockLegacyGetCustomer } from '../../setupTests';
import { SessionHandler } from '../components/session/SessionHandler';
import { LegacyOrdersContext } from '../context/legacy/LegacyOrdersContext';
import { useLegacyGetCustomer } from '../networkRequests/legacy/queries/useLegacyGetCustomer';
import { LegacyQueryCacheKeys } from '../networkRequests/QueryCacheKeys';

export const runPendingPromises = async () => act(async () => {});

export function getInitialEntries(path?: string, search?: string, pageState?: any) {
  return [{ path, search, state: pageState }];
}

// QueryClient by default retries failures up to 3 times
// so in tests, we disable this retry to render failures immediately,
// otherwise there would be a delay, and the test would fail unexpectedly

export const testQueryClient = (
  options: { queryCache?: QueryCache; mutationCache?: MutationCache } = {}
) =>
  new QueryClient({
    queryCache: options.queryCache ?? new QueryCache(),
    mutationCache: options.mutationCache ?? new MutationCache(),
    defaultOptions: {
      queries: {
        gcTime: 0,
        retryDelay: 1,
        retry: 0,
        staleTime: 100
      },
      mutations: {
        gcTime: 0,
        retryDelay: 1,
        retry: 0
      }
    }
  });

export const expectCustomerContextContains = async (data: string) => {
  await waitFor(() =>
    expect(screen.getByTestId('customer-context-viewer')).toHaveTextContent(new RegExp(data))
  );
};

export const CustomerContextViewer = () => {
  const { customer } = useGetCustomer();
  return <div data-testid="customer-context-viewer">{JSON.stringify(customer)}</div>;
};

export const LegacyCustomerContextViewer = () => {
  const { customer } = useLegacyGetCustomer();
  return <div data-testid="customer-context-viewer">{JSON.stringify(customer)}</div>;
};

export function renderWithTheme(subject: React.JSX.Element) {
  return render(
    <MemoryRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      <ThemeProvider theme={theme}>{subject}</ThemeProvider>
    </MemoryRouter>
  );
}

export function renderWithQueryProvider(
  subject: React.JSX.Element,
  client?: QueryClient,
  initialEntries: InitialEntry[] | string[] | undefined = undefined
) {
  const queryClient = client ?? testQueryClient();

  return render(
    <MemoryRouter
      initialEntries={initialEntries}
      future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      <ThemeProvider theme={theme}>
        <ErrorBoundary renderError={(error) => <p>{error.message}</p>}>
          <QueryClientProvider client={queryClient}>
            <Suspense fallback={<div>Loading customer information...</div>}>
              <NotificationProvider>{subject}</NotificationProvider>
            </Suspense>
          </QueryClientProvider>
        </ErrorBoundary>
      </ThemeProvider>
    </MemoryRouter>
  );
}

export function renderWithLegacyProvidersAndState(
  subject: React.JSX.Element,
  options: {
    entryPointState?: EntryPointContextState;
    billingState?: BillingContextProps;
    ordersState?: IOrdersContextState;
    paymentMethodState?: PaymentMethod[];
    initialEntries?: InitialEntry[] | string[];
    customQueryClient?: QueryClient;
    initialCustomer?: Customer;
  } = {}
) {
  const {
    entryPointState,
    initialEntries,
    billingState,
    customQueryClient,
    ordersState,
    initialCustomer
  } = options;
  const customer = initialCustomer ?? createCustomer();
  const queryClient = customQueryClient ?? testQueryClient();

  mockLegacyGetCustomer.mockResolvedValue(customer);
  queryClient.setQueryData([LegacyQueryCacheKeys.LEGACY_CUSTOMER_CACHE_KEY], customer);

  return render(
    <MemoryRouter
      initialEntries={initialEntries}
      future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      <ThemeProvider theme={theme}>
        <QueryClientProvider client={queryClient}>
          <ErrorBoundary renderError={(error) => <p>{error.message}</p>}>
            <Suspense fallback={<div>Loading customer information...</div>}>
              <EntryPointProviderWrapper {...(entryPointState ?? initialEntryPointState)}>
                <BillingContext.Provider value={{ ...(billingState ?? initialBillingState) }}>
                  <LegacyOrdersContext.Provider
                    value={{
                      ...(ordersState ?? {
                        orders: [],
                        refetch: vi.fn,
                        refetchOnFailure: vi.fn,
                        showLoader: false
                      })
                    }}>
                    <NotificationProvider>{subject}</NotificationProvider>
                  </LegacyOrdersContext.Provider>
                </BillingContext.Provider>
              </EntryPointProviderWrapper>
            </Suspense>
          </ErrorBoundary>
        </QueryClientProvider>
      </ThemeProvider>
    </MemoryRouter>
  );
}

export function renderWithPoetProvidersAndState(
  subject: React.JSX.Element,
  options: {
    initialEntries?: InitialEntry[] | string[];
    ordersState?: IOrdersContextState;
    customQueryClient?: QueryClient;
  } = {}
) {
  const { initialEntries, customQueryClient, ordersState } = options;
  const queryClient = customQueryClient ?? testQueryClient();

  return render(
    <MemoryRouter
      initialEntries={initialEntries}
      future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      <ThemeProvider theme={theme}>
        <ErrorBoundary renderError={(error) => <p>{error.message}</p>}>
          <QueryClientProvider client={queryClient}>
            <Suspense fallback={<div>Loading (Test Render Promise Awaiting)...</div>}>
              <SessionHandler>
                <OrdersContext.Provider
                  value={{
                    ...(ordersState ?? {
                      orders: [],
                      refetch: vi.fn,
                      refetchOnFailure: vi.fn,
                      showLoader: false
                    })
                  }}>
                  <NotificationProvider>{subject}</NotificationProvider>
                </OrdersContext.Provider>
              </SessionHandler>
            </Suspense>
          </QueryClientProvider>
        </ErrorBoundary>
      </ThemeProvider>
    </MemoryRouter>
  );
}
