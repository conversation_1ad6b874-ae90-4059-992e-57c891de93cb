/* eslint-disable react/prop-types */
import { vi } from 'vitest';
import React from 'react';

const hasChildren = (node) => node && (node.children || (node.props && node.props.children));

const getChildren = (node) =>
  node && node.children ? node.children : node.props && node.props.children;

const renderNodes = (reactNodes) => {
  if (typeof reactNodes === 'string') {
    return reactNodes;
  }

  return Object.keys(reactNodes).map((key, i) => {
    const child = reactNodes[key];
    const isElement = React.isValidElement(child);
    if (typeof child === 'string') {
      return child;
    }

    if (hasChildren(child)) {
      const inner = renderNodes(getChildren(child));
      return React.cloneElement(child, { ...child.props, key: i }, inner);
    }

    if (typeof child === 'object' && !isElement) {
      return Object.keys(child).reduce((str, childKey) => `${str}${child[childKey]}`, '');
    }

    return child;
  });
};

const useMock = [(k) => k, {}];
useMock.t = (key, args) => {
  if (args == null) {
    return key;
  }

  return `${key}[${Object.values(args).join(',')}]`;
};

useMock.i18n = {
  changeLanguage: () => new Promise(() => {})
};

export const mockTransFn = ({ i18nKey, children, components, ...props }) => (
  <React.Fragment key={i18nKey}>
    <React.Fragment key={`${i18nKey}-key`}>{i18nKey}</React.Fragment>
    <React.Fragment key={`${i18nKey}-props`}>
      <span {...props} />
      <script>{JSON.stringify(props.values)}</script>
    </React.Fragment>

    <React.Fragment key={`${i18nKey}-children`}>
      {Array.isArray(children) ? renderNodes(children) : renderNodes([children])}
    </React.Fragment>

    <React.Fragment key={`${i18nKey}-components`}>
      {components && renderNodes(components)}
    </React.Fragment>
  </React.Fragment>
);

vi.mock('react-i18next', async () => ({
  ...(await vi.importActual('react-i18next')),
  // this mock makes sure any components using the translate HoC receive the t function as a prop
  withTranslation: () => (Component) => (props) => <Component t={(k) => k} {...props} />,
  Trans: mockTransFn,
  Translation: ({ children }) => children((k) => k, { i18n: {} }),
  useTranslation: () => useMock
}));
