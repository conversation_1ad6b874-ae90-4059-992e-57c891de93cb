import React from 'react';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { theme } from '../../../PodsTheme';
import { PageLayout } from '../../../components/PageLayout';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';
import { DocumentCardSkeleton } from './DocumentCardSkeleton';

export const DocumentsSkeleton = () => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = documentSkeletonStyles(isMobile);
  const { t: translate } = useTranslation();
  const numSkeletonDocs = 4;
  // Placeholder array so that we can easily render each row of the skeleton
  const documentSkeletonPlaceholders = [...Array(numSkeletonDocs).keys()];
  return (
    <PageLayout columnsLg={6}>
      <Grid container direction="column" spacing={2}>
        <Grid item>
          <Grid container direction="column">
            <Typography variant="h1">{translate(TranslationKeys.DocumentsPage.HEADER)}</Typography>
            <Typography {...styles.subtitle}>
              {translate(TranslationKeys.DocumentsPage.SUBTITLE)}
            </Typography>
          </Grid>
        </Grid>
        <Grid item container direction="column" rowGap={2}>
          {documentSkeletonPlaceholders.map((_, i) => (
            <DocumentCardSkeleton key={i} />
          ))}
        </Grid>
      </Grid>
    </PageLayout>
  );
};

// -- styles --
const documentSkeletonStyles = (isMobile: boolean) => ({
  documentPage: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.lgPlus
    }
  },
  headerSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.sm
    }
  },
  subtitle: {
    sx: {
      ...(isMobile ? Design.Alias.Text.BodyUniversal.Md : Design.Alias.Text.BodyUniversal.Lg),
      color: Design.Alias.Color.accent900
    }
  },
  documentCards: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs
    }
  }
});
