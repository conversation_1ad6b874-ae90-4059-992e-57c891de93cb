import { Grid, Skeleton } from '@mui/material';
import React from 'react';
import { Design } from '../../../helpers/Design';

export const DocumentCardSkeleton = () => {
  const styles = documentCardStyles;
  return (
    <Grid container alignItems="center" direction="row" {...styles.card}>
      <Grid container direction="row" {...styles.documentData}>
        <Grid item xs={1} style={{ alignContent: 'center' }}>
          <Skeleton
            variant="rectangular"
            height={16}
            width={16}
            sx={{ borderRadius: '4px' }}></Skeleton>
        </Grid>
        <Grid item xs={6} {...styles.titleContainer}>
          <Skeleton variant="text" />
          <Skeleton variant="text" />
        </Grid>
      </Grid>
      <Grid container item xs={1} {...styles.iconContainer} justifyContent="flex-end">
        <Skeleton
          variant="rectangular"
          height={24}
          width={24}
          sx={{ borderRadius: '4px' }}></Skeleton>
      </Grid>
    </Grid>
  );
};

const documentCardStyles = {
  button: {
    sx: {
      display: 'flex',
      width: '100%',
      textAlign: 'unset'
    }
  },
  card: {
    sx: {
      gap: Design.Primitives.Spacing.sm,
      display: 'flex',
      flexDirection: 'row',
      padding: '16px',
      flexWrap: 'nowrap',
      justifyContent: 'space-between',
      borderRadius: '8px',
      border: '1px solid var(--color-neutral-neutral300, #CBCBCB)',
      background: 'var(--color-defaults-neutral100-D, #FFF)',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)'
    }
  },
  documentData: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  titleContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column'
    }
  },
  iconContainer: {
    sx: {
      alignContent: 'center'
    }
  }
};
