import React, { useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { PageLayout } from '../../components/PageLayout';
import { Design } from '../../helpers/Design';
import { theme } from '../../PodsTheme';

import { DocumentCard } from './DocumentCard';
import { NotificationContext } from '../../components/notifications/NotificationContext';
import { DocumentsSkeleton } from './utility/DocumentsSkeleton';
import { useGetOrderDocuments } from '../../networkRequests/queries/v2/useGetOrderDocuments';
import { useGetCustomerDocuments } from '../../networkRequests/queries/v2/useGetCustomerDocuments';
import { usePreloadData } from '../../networkRequests/usePreloadData';

// -- impls --
export const DocumentsPage: React.FC = () => {
  // -- hooks --
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t: translate } = useTranslation();
  const {
    orderDocuments: orderDocs,
    error: orderDocsError,
    isPending: orderDocsIsPending
  } = useGetOrderDocuments();
  const {
    documentsPageDocuments: customerDocs,
    error: customerDocsError,
    isPending: customerDocsIsPending
  } = useGetCustomerDocuments();
  // -- state --
  const { setNotification } = useContext(NotificationContext);
  const documents = useMemo(
    () => [
      ...orderDocs.map((doc) => ({
        orderId: doc.orderId,
        id: doc.id,
        title: doc.title,
        type: doc.docType,
        docName: doc.docName
      })),
      ...customerDocs.map((doc) => ({
        orderId: '',
        id: doc.id,
        title: doc.title,
        type: doc.docType,
        docName: doc.docName
      }))
    ],
    [orderDocs, customerDocs]
  );
  // -- constants --
  const styles = documentPageStyles(isMobile);

  // -- handlers --
  // TODO: Confirm the correct behavior for errors with design/product - eh
  if (orderDocsError || customerDocsError)
    setNotification({
      message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
      isError: true
    });

  usePreloadData();
  if (orderDocsIsPending || customerDocsIsPending) return <DocumentsSkeleton />;

  if (documents.length < 1) {
    return (
      <PageLayout columnsLg={6}>
        <Grid {...styles.headerSection}>
          <Typography variant="h1">{translate(TranslationKeys.DocumentsPage.HEADER)}</Typography>
          <Typography {...styles.subtitle}>No documents found</Typography>
        </Grid>
      </PageLayout>
    );
  }

  return (
    <PageLayout columnsLg={6}>
      <Grid {...styles.documentPage}>
        <Grid {...styles.headerSection}>
          <Typography variant="h1">{translate(TranslationKeys.DocumentsPage.HEADER)}</Typography>
          <Typography {...styles.subtitle}>
            {translate(TranslationKeys.DocumentsPage.SUBTITLE)}
          </Typography>
        </Grid>
        <Grid {...styles.documentCards}>
          {documents &&
            documents.map((document) => (
              <DocumentCard
                {...document}
                displayName={translate(`documentsPage.types.${document.type}`)}
                fileName={document.docName}
                key={document.id}
              />
            ))}
        </Grid>
      </Grid>
    </PageLayout>
  );
};

// -- styles --
const documentPageStyles = (isMobile: boolean) => ({
  documentPage: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.lgPlus
    }
  },
  headerSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.sm
    }
  },
  subtitle: {
    sx: {
      ...(isMobile ? Design.Alias.Text.BodyUniversal.Md : Design.Alias.Text.BodyUniversal.Lg),
      color: Design.Alias.Color.accent900
    }
  },
  documentCards: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs
    }
  }
});
