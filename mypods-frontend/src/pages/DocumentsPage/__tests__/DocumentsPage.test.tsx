import { renderWithQueryProvider, testQueryClient } from '../../../testUtils/RenderHelpers';
import { DocumentsPage } from '../DocumentsPage';
import { screen } from '@testing-library/react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import {
  createBillingInformation,
  createCustomer,
  createOrderDocument
} from '../../../testUtils/MyPodsFactories';
import {
  mockGetBillingInformation,
  mockGetCustomer,
  mockGetCustomerDocuments,
  mockGetOrderDocuments
} from '../../../../setupTests';
import { QueryCacheKeys } from '../../../networkRequests/QueryCacheKeys';

describe('Document Page', () => {
  const testDoc = createOrderDocument();

  beforeEach(() => {
    mockGetBillingInformation.mockResolvedValue(createBillingInformation());
    mockGetCustomer.mockResolvedValue(createCustomer());
    mockGetCustomerDocuments.mockResolvedValue({ documents: [] });
  });

  const queryClient = testQueryClient();
  it('should render documents page', async () => {
    mockGetOrderDocuments.mockResolvedValue({ documents: [testDoc] });

    renderWithQueryProvider(<DocumentsPage />, queryClient);

    expect(await screen.findByText(TranslationKeys.DocumentsPage.HEADER)).toBeInTheDocument();
    expect(await screen.findByText(TranslationKeys.DocumentsPage.SUBTITLE)).toBeInTheDocument();
    expect(
      await screen.findByText(TranslationKeys.DocumentsPage.Types.RENTAL_AGREEMENT)
    ).toBeInTheDocument();
    expect(screen.getByText(`#${testDoc.orderId}`)).toBeInTheDocument();
  });

  it.skip('given customer tries access a doc they do not own, should display an error', async () => {
    mockGetOrderDocuments.mockResolvedValue({ documents: [testDoc] });

    renderWithQueryProvider(<DocumentsPage />, queryClient);

    expect(await screen.findByText(TranslationKeys.DocumentsPage.HEADER));
    expect(await screen.findByText(TranslationKeys.DocumentsPage.SUBTITLE));
    expect(screen.getByText(`#${testDoc.orderId}`)).toBeInTheDocument();
  });

  it('displays a fallback message when the user has no documents', async () => {
    mockGetOrderDocuments.mockResolvedValue({ documents: [] });
    queryClient.setQueryData([QueryCacheKeys.ORDER_DOCUMENTS_KEY], { documents: [] });
    queryClient.setQueryData([QueryCacheKeys.CUSTOMER_DOCUMENTS_KEY], { documents: [] });

    renderWithQueryProvider(<DocumentsPage />, queryClient);

    expect(screen.getByText(/No documents found/)).toBeInTheDocument();
  });
});
