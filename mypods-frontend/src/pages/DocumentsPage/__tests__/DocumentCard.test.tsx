import React, { act } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { createDocument } from '../../../testUtils/MyPodsFactories';
import { DocumentCard } from '../DocumentCard';
import { DocumentDescription, IDocument } from '../../../domain/DocumentEntities';
import { mockGetSasUrl } from '../../../../setupTests';
import { testQueryClient } from '../../../testUtils/RenderHelpers';
import { NotificationProvider } from '../../../components/notifications/NotificationContext';
import { QueryClientProvider } from '@tanstack/react-query';
import { DOCUMENT_TITLES, SIGNABLE_DOCUMENTS } from '../../../helpers/Documents';

describe('DocumentCard', () => {
  const user = userEvent.setup();
  const orderConfirmationDoc = createDocument({ isPoet: true });
  const displayName = 'Display Name';
  const documentName = 'docName';
  const renderCardForDocument = (
    document: IDocument = orderConfirmationDoc,
    docName: string = documentName,
    fileName: string = ''
  ) => {
    render(
      <QueryClientProvider client={testQueryClient()}>
        <NotificationProvider>
          <DocumentCard
            {...document}
            displayName={displayName}
            docName={docName}
            fileName={fileName}
          />
        </NotificationProvider>
      </QueryClientProvider>
    );
  };

  const actions = {
    openDocument: async () => {
      await act(async () => await user.click(screen.getByRole('button')));
    }
  };

  it('displays the title & subtitle correctly', () => {
    renderCardForDocument();

    screen.getByText(displayName);
    screen.getByText(`#${orderConfirmationDoc.orderId}`);
  });

  it.each(SIGNABLE_DOCUMENTS)(
    'displays the green checkmark for signed %s document',
    (documentName) => {
      const document = createDocument({ title: documentName as DocumentDescription });
      renderCardForDocument(document, documentName);
      screen.getByTestId('checkmark-icon');
    }
  );

  const documentsThatCannotBeSigned = DOCUMENT_TITLES.filter(
    (title) => !SIGNABLE_DOCUMENTS.includes(title)
  );
  it.each(documentsThatCannotBeSigned)(
    'displays the grey document for non-signable %s document',
    (documentName) => {
      const document = createDocument({ title: documentName as DocumentDescription });
      renderCardForDocument(document, documentName);
      screen.getByTestId('document-icon');
    }
  );

  describe.skip('signature information', () => {
    it.skip('when the document is unsigned, it includes a link to sign the document', () => {});
    it.skip('when the document signed, it does not include a signature prompt', () => {});
  });

  describe('poet download links', () => {
    const expectedUrl = 'https://file.pdf?sas=token';
    const fileName = 'https://file.pdf';
    const poetDocument = createDocument({ isPoet: true });
    const open = vi.fn();
    const originalOpen = window.open;

    beforeEach(() => {
      window.open = open;
    });

    afterEach(() => {
      window.open = originalOpen;
    });

    it('should redirect the user to returned url from the getSasUrl call', async () => {
      mockGetSasUrl.mockResolvedValue(expectedUrl);
      renderCardForDocument(poetDocument, documentName, fileName);

      await actions.openDocument();

      expect(mockGetSasUrl).toHaveBeenCalledWith(fileName, false);
      expect(open).toHaveBeenCalledWith(expectedUrl, '_blank');
    });
  });
});
