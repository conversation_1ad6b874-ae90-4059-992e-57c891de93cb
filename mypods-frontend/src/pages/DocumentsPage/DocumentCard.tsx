import React, { useContext } from 'react';
import { ButtonBase, CircularProgress, Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { AxiosError } from 'axios';
import { Design } from '../../helpers/Design';
import { DocumentIcon } from '../../components/icons/DocumentIcon';
import { CheckmarkIcon } from '../../components/icons/CheckmarkIcon';
import { NotificationContext } from '../../components/notifications/NotificationContext';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { SIGNABLE_DOCUMENTS } from '../../helpers/Documents';
import { DownloadIcon } from '../../components/icons/DownloadIcon';
import { useGtmEvents } from '../../config/google/useGtmEvents';
import { useGetSasUrl } from '../../networkRequests/mutations/useGetSasUrl'; // -- types --

interface Props {
  docName: string;
  orderId: string;
  displayName: string;
  fileName: string;
}

// -- impls --
export const DocumentCard: React.FC<Props> = ({
  docName,
  orderId,
  displayName,
  fileName
}: Props) => {
  const { setNotification } = useContext(NotificationContext);
  const getSasUrl = useGetSasUrl();
  const { t: translate } = useTranslation();
  const styles = documentCardStyles;
  const gtmEvents = useGtmEvents();
  const isLoading = getSasUrl.isPending;

  const onError = (error: unknown) => {
    if (error instanceof AxiosError && error.response?.status) {
      if (error.response?.status === 404) {
        setNotification({
          isError: true,
          message: translate(TranslationKeys.CommonComponents.Notification.DOCUMENT_NOT_FOUND)
        });
      }
    } else {
      setNotification({
        isError: true,
        message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE)
      });
    }
  };

  const onSuccess = (url: string) => {
    gtmEvents.fileDownload(docName);
    window.open(url, '_blank');
  };

  const openDocument = () => {
    if (fileName === '') {
      setNotification({
        isError: true,
        message: translate(TranslationKeys.CommonComponents.Notification.FILE_NOT_READY_ERROR)
      });
      return;
    }

    getSasUrl.mutate(
      { docRef: fileName, isBillingDocument: false },
      {
        onSuccess,
        onError
      }
    );
  };

  return (
    <ButtonBase {...styles.button} onClick={openDocument} disabled={isLoading}>
      <Grid container {...styles.card}>
        <Grid {...styles.documentData}>
          <Grid item style={{ alignContent: 'center' }}>
            {SIGNABLE_DOCUMENTS.includes(docName) ? (
              <CheckmarkIcon {...styles.checkmarkIcon} data-testid="checkmark-icon" />
            ) : (
              <DocumentIcon data-testid="document-icon" />
            )}
          </Grid>
          <Grid item {...styles.titleContainer}>
            <Typography {...styles.cardTitle}>{displayName}</Typography>
            {orderId && <Typography>#{orderId}</Typography>}
          </Grid>
        </Grid>
        <Grid item {...styles.iconContainer}>
          {isLoading ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            <DownloadIcon {...styles.downloadIcon} />
          )}
        </Grid>
      </Grid>
    </ButtonBase>
  );
};

// -- styles --
const documentCardStyles = {
  button: {
    sx: {
      display: 'flex',
      width: '100%',
      textAlign: 'unset'
    }
  },
  card: {
    sx: {
      gap: Design.Primitives.Spacing.sm,
      display: 'flex',
      flexDirection: 'row',
      padding: '16px',
      flexWrap: 'nowrap',
      justifyContent: 'space-between',
      borderRadius: '8px',
      border: '1px solid var(--color-neutral-neutral300, #CBCBCB)',
      background: 'var(--color-defaults-neutral100-D, #FFF)',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)'
    }
  },
  cardTitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.MdBold,
      color: Design.Alias.Color.accent900
    }
  },
  cardSubtitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: Design.Alias.Color.neutral700
    }
  },
  documentData: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  titleContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column'
    }
  },
  iconContainer: {
    sx: {
      alignContent: 'center'
    }
  },
  checkmarkIcon: {
    style: {
      color: Design.Alias.Color.successMain
    },
    sx: {
      width: '20px',
      height: '20px'
    }
  },
  downloadIcon: {
    sx: {
      width: '24px',
      height: '24px'
    }
  }
};
