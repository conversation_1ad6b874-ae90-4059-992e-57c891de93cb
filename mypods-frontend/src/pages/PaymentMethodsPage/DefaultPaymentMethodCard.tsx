import React from 'react';
import { Chip, Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { PaymentMethod } from '../../networkRequests/responseEntities/PaymentEntities';
import { CardIcon, PaymentTypeIcon } from '../../components/icons/PaymentTypeIcon';

// -- types --
interface Props {
  paymentMethod: PaymentMethod;
}

// -- impls --
export const DefaultPaymentMethodCard: React.FC<Props> = ({ paymentMethod }: Props) => {
  const { t: translate } = useTranslation();
  const isLineOfCredit = paymentMethod.cardType === CardIcon.LINE_OF_CREDIT;

  const renderPaymentDetails = ({
    cardType,
    cardNumberLastFourDigits,
    cardExpirationDate,
    accountId
  }: PaymentMethod) => {
    switch (cardType) {
      case CardIcon.PAYPAL:
        return (
          <Grid item {...styles.textContainer}>
            <Typography {...styles.paypal}>{accountId}</Typography>
          </Grid>
        );
      case CardIcon.LINE_OF_CREDIT: {
        if (paymentMethod.locLender?.includes('Upgrade')) {
          return translate(TranslationKeys.PaymentMethodsPage.PaymentType.PERSONAL_LOAN);
        }
        return;
      }
      default:
        return (
          <Grid item {...styles.textContainer}>
            <Typography>
              {translate(TranslationKeys.PaymentMethodsPage.PaymentType.CREDIT_CARD)}...
              {cardNumberLastFourDigits}
            </Typography>
            <Typography>|</Typography>
            <Typography>Exp. {format(cardExpirationDate, 'MM/yy')}</Typography>
          </Grid>
        );
    }
  };

  let renderPaymentType: string = paymentMethod.displayCardType;
  if (isLineOfCredit) {
    if (paymentMethod.locLender?.includes('Upgrade')) {
      renderPaymentType = `${translate(TranslationKeys.PaymentMethodsPage.PaymentType.MOVE_LOAN_UPGRADE_TEXT)}`;
    } else if (paymentMethod.locLender?.includes('Citi')) {
      renderPaymentType = `${translate(TranslationKeys.PaymentMethodsPage.PaymentType.MOVE_LOAN_CITI_BANK_TEXT)}`;
    }
  }

  return (
    <Grid {...styles.cardContainer} data-testid={`payment-method-${paymentMethod.paymentMethodId}`}>
      <Grid container {...styles.card}>
        <Grid item style={{ alignContent: 'center' }}>
          <PaymentTypeIcon cardType={paymentMethod.cardType as CardIcon} />
        </Grid>
        <Grid {...styles.cardDetails}>
          <Grid item {...styles.textContainer}>
            <Typography {...styles.cardTitle}>{renderPaymentType}</Typography>
            <Chip label={translate(TranslationKeys.PaymentMethodsPage.DEFAULT)} size="small" />
          </Grid>
          {renderPaymentDetails(paymentMethod)}
        </Grid>
      </Grid>
    </Grid>
  );
};

// -- styles --
const styles = {
  cardContainer: {
    sx: {
      width: '100%',
      textAlign: 'unset'
    }
  },
  card: {
    sx: {
      gap: Design.Primitives.Spacing.sm,
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'nowrap',
      padding: Design.Primitives.Spacing.sm,
      justifyContent: 'space-between',
      borderRadius: '8px',
      border: '1px solid var(--color-neutral-neutral300, #CBCBCB)',
      background: 'var(--color-defaults-neutral100-D, #FFF)',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)'
    }
  },
  cardDetails: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'flexStart',
      flex: 1
    }
  },
  textContainer: {
    sx: {
      display: 'flex',
      flex: 1,
      flexDirection: 'row',
      alignContent: 'center',
      gap: Design.Primitives.Spacing.xxs,
      ...Design.Alias.Text.BodyUniversal.Md
    }
  },
  paypal: {
    sx: {
      textWrap: 'wrap',
      overflowWrap: 'anywhere'
    }
  },
  cardTitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.LgBold,
      color: Design.Alias.Color.accent900
    }
  }
};
