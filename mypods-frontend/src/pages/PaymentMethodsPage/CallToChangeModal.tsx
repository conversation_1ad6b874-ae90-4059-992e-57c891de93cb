import { Grid, Box, Button, IconButton, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import CloseIcon from '@mui/icons-material/Close';
import { PodsModal } from '../../components/Modals/PodsModal';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { StretchableLoadingButton } from '../../components/buttons/StretchableLoadingButton';
import { theme } from '../../PodsTheme';
import { PodsModalContent } from '../../components/Modals/PodsModalBody';
import { Design } from '../../helpers/Design';

export interface CallToChangeModalProps {
  open: boolean;
  handleOnClosed: () => void;
}

const Tx = TranslationKeys.PaymentMethodsPage.CallToChangeModal;

export const CallToChangeModal = ({ open, handleOnClosed }: CallToChangeModalProps) => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = styleSheet(isMobile);
  const handleCallMe = () => {
    if (isMobile) {
      window.open('tel:************');
    }
  };
  return (
    <PodsModal open={open}>
      <PodsModalContent>
        <Grid container {...styles.header}>
          <IconButton onClick={handleOnClosed} aria-label="close">
            <CloseIcon />
          </IconButton>
        </Grid>
        <Grid container direction="column">
          <Grid {...styles.mainGrid}>
            <Grid item>
              <Typography {...styles.title}>{translate(Tx.TITLE)}</Typography>
            </Grid>
            <Grid item>
              <Typography {...styles.body}>{translate(Tx.BODY)}</Typography>
            </Grid>
          </Grid>

          <Box display="flex" justifyContent="center">
            <Grid container item {...styles.buttonContainer}>
              <StretchableLoadingButton
                onClick={handleCallMe}
                customStyles={styles.button}
                isMobile={isMobile}
                label={translate(Tx.Buttons.CALL_US)}
              />
            </Grid>
          </Box>
          <Box display="flex" justifyContent="center">
            <Grid item>
              <Button {...styles.button1} onClick={handleOnClosed} variant="text" color="secondary">
                {translate(Tx.Buttons.CLOSE)}
              </Button>
            </Grid>
          </Box>
        </Grid>
      </PodsModalContent>
    </PodsModal>
  );
};

const styleSheet = (isMobile: boolean) => ({
  header: {
    sx: {
      justifyContent: 'end'
    }
  },
  title: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Xl : Design.Alias.Text.Heading.Desktop.Lg),
      textAlign: 'center',
      paddingBottom: Design.Primitives.Spacing.xxs
    }
  },
  body: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Lg,
      textAlign: 'center'
    }
  },
  mainGrid: {
    sx: {
      paddingBottom: Design.Primitives.Spacing.lg
    }
  },
  buttonContainer: {
    sx: {
      alignSelf: 'center',
      maxWidth: '400px',
      justifyContent: 'center'
    }
  },
  button: { sx: { height: '56px' } },
  button1: {
    sx: {
      textDecoration: 'underline'
    }
  },
  closeTitle: {
    sx: {
      textAlign: 'center',
      paddingTop: Design.Primitives.Spacing.md,
      paddingBottom: Design.Primitives.Spacing.sm
    }
  }
});
