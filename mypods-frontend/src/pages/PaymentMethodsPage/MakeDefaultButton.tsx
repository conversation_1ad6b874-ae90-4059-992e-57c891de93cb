import React from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { LoadingTextButton } from '../../components/buttons/LoadingTextButton';

export const MakeDefaultButton = ({
  action,
  isLoading
}: {
  action: () => void;
  isLoading: boolean;
}) => {
  const { t: translate } = useTranslation();

  const label = isLoading
    ? translate(TranslationKeys.PaymentMethodsPage.MAKE_DEFAULT, {
        context: 'loading'
      })
    : translate(TranslationKeys.PaymentMethodsPage.MAKE_DEFAULT, {
        context: 'default'
      });

  return (
    <LoadingTextButton isLoading={isLoading} onClick={action} label={label} color="secondary" />
  );
};
