import React from 'react';
import { Checkbox, FormControlLabel, Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { MothCheckboxState } from './MothInspectionFormTypes';
import { theme } from '../../PodsTheme';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';

export const CheckBoxItem: React.FC<{ item: MothCheckboxState; onChange: () => void }> = ({
  item,
  onChange
}) => {
  const { t: translate } = useTranslation();

  return (
    <Grid item {...checkboxStyles(item.checked, item.restricted).checkboxContainer}>
      <FormControlLabel
        control={
          <Checkbox
            checked={item.checked}
            onChange={onChange}
            color={item.checked && item.restricted ? 'primary' : 'secondary'}
            {...checkboxStyles(item.checked, item.restricted).checkbox}
          />
        }
        label={<Typography variant="subtitle1">{item.label}</Typography>}
        labelPlacement="end"
        {...checkboxStyles(item.checked, item.restricted).label}
      />
      {item.checked && item.restricted && (
        <Typography
          variant="subtitle1"
          {...checkboxStyles(item.checked, item.restricted).restrictedWarning}>
          {translate(TranslationKeys.MothInspectionFormPage.RESTRICTED_ITEM_MESSAGE)}
        </Typography>
      )}
    </Grid>
  );
};

const checkboxStyles = (isChecked: boolean, isRestricted: boolean = false) => ({
  label: {
    sx: {
      width: '100%',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)',
      borderRadius: '8px',
      border: `2px solid ${Design.Alias.Color.neutral300}`,
      backgroundColor: Design.Alias.Color.neutral100,
      '&.MuiFormControlLabel-root': {
        margin: '0'
      },
      ...(isChecked && {
        border: `2px solid ${Design.Alias.Color.secondary500}`,
        backgroundColor: Design.Alias.Color.secondary100
      }),
      ...(isChecked &&
        isRestricted && {
          border: `2px solid ${Design.Alias.Color.primary500}`,
          backgroundColor: Design.Alias.Color.primary100
        })
    }
  },
  restrictedWarning: {
    sx: {
      width: '100%',
      color: Design.Alias.Color.primary500
    }
  },
  checkbox: {
    sx: {
      '& .MuiSvgIcon-root': { fontSize: '1rem' }
    }
  },
  checkboxContainer: {
    sx: {
      [theme.breakpoints.only('xs')]: { width: '100%' },
      [theme.breakpoints.up('sm')]: { width: '49%' },
      [theme.breakpoints.up('md')]: { width: '32%' }
    }
  }
});
