import { render } from '@testing-library/react';
import { CheckBoxItem } from '../CheckBoxItem';
import { MothCheckboxState } from '../MothInspectionFormTypes';
import { vi } from 'vitest';
import { act, screen, waitFor } from '@testing-library/react';
import { TranslationKeys } from '../../../locales/TranslationKeys';

const createItem = (args: Partial<MothCheckboxState> = {}): MothCheckboxState => ({
  checked: false,
  description: '',
  id: '',
  label: 'Item 1',
  pdfKey: '',
  restricted: false,
  ...args
});

const onChange = vi.fn();

describe('CheckBoxItem', () => {
  it('should display', () => {
    const item = createItem();
    render(<CheckBoxItem item={item} onChange={onChange} />);

    expect(
      screen.getByRole('checkbox', {
        name: item.label
      })
    ).toBeInTheDocument();
  });

  it('should display warning when item is restricted', () => {
    const item = createItem({ restricted: true, checked: true });

    render(<CheckBoxItem item={item} onChange={onChange} />);

    expect(
      screen.getByRole('checkbox', {
        name: item.label
      })
    ).toBeInTheDocument();

    expect(
      screen.getByRole('heading', {
        name: TranslationKeys.MothInspectionFormPage.RESTRICTED_ITEM_MESSAGE
      })
    ).toBeInTheDocument();
  });
});
