import { screen, waitFor } from '@testing-library/react';
import userEvent, { UserEvent } from '@testing-library/user-event';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises,
  testQueryClient
} from '../../../testUtils/RenderHelpers';
import {
  mockedUseFeatureFlags,
  mockGetCustomer,
  mockGetCustomerOrders,
  mockGetOrderDocuments,
  mockLegacyGetCustomer,
  mockNavigate,
  mockRefreshSession,
  mockScrollIntoView,
  mockSignMothAgreement
} from '../../../../setupTests';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { SignMothAgreementRequest } from '../../../networkRequests/responseEntities/DocumentApiEntities';
import {
  createContainer,
  createContainerApi,
  createCustomer,
  createMoveLeg,
  createMoveLegAddress,
  createMoveLegApi,
  createOrder,
  createOrder<PERSON><PERSON>,
  createOrderDocument,
  createRefreshSessionClaims,
  createSignMothAgreementRequest,
  createUseFeatureFlagResult
} from '../../../testUtils/MyPodsFactories';
import {
  Customer,
  formatAddress
} from '../../../networkRequests/responseEntities/CustomerEntities';
import { ROUTES } from '../../../Routes';
import {
  getOrdersWithSignedMothAgreements,
  setOrdersWithSignedMothAgreements
} from '../../../helpers/storageHelpers';
import { vi } from 'vitest';
import * as exports from '../mothInspectionConfig';
import { OrderAPI } from '../../../networkRequests/responseEntities/OrderAPIEntities';
import { RefreshSessionClaims } from '../../../networkRequests/responseEntities/AuthorizationEntities';
import { expectNotificationAlertContainsTitle } from '../../../testUtils/assertions';
import { QueryCacheKeys } from '../../../networkRequests/QueryCacheKeys';
import { MothInspectionVariantSelector } from '../MothInspectionVariantSelector';

const mothActions = (user: UserEvent) => ({
  submitForm: async () => {
    const agreeButton = screen.getByRole('button', {
      name: TranslationKeys.MothInspectionFormPage.AGREE_BUTTON
    });
    const addSignatureButton = screen.getByText(
      TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
    );
    await waitFor(() => user.click(addSignatureButton));
    await waitFor(() => user.click(agreeButton));
  }
});

const mockUseLocation = vi.hoisted(() => vi.fn());
vi.mock('react-router-dom', async () => ({
  ...((await vi.importActual('react-router-dom')) as any),
  useNavigate: () => mockNavigate,
  useLocation: mockUseLocation
}));

describe('MothInspectionVariantSelector', () => {
  let initialCustomer: RefreshSessionClaims;
  let user: UserEvent;
  let customer: Customer;
  let orderId: string;
  let actions: { submitForm: () => Promise<void> };
  let customerOrder: OrderAPI;

  beforeEach(() => {
    // customer & actions
    user = userEvent.setup();
    actions = mothActions(user);
    customer = createCustomer();
    mockGetCustomer.mockResolvedValue(customer);
    mockLegacyGetCustomer.mockResolvedValue(customer);
    initialCustomer = createRefreshSessionClaims();
    mockRefreshSession.mockResolvedValue(initialCustomer);
    testQueryClient().setQueryData([QueryCacheKeys.CUSTOMER_KEY], customer);
    mockUseLocation.mockReturnValue({ pathname: '/' });
    // order
    orderId = '12354';
    customerOrder = { ...createOrderApi({ orderId: orderId }) };
    mockGetCustomerOrders.mockResolvedValue([customerOrder]);
    // order & order docs
    const mothAgreement = createOrderDocument({
      orderId: orderId,
      docType: 'SPONGY_MOTH_FORM',
      docStatus: 'SENT'
    });
    mockSignMothAgreement.mockResolvedValue(createSignMothAgreementRequest(customer, { orderId }));
    mockGetOrderDocuments.mockResolvedValue({
      documents: [mothAgreement]
    });
    testQueryClient().setQueryData([QueryCacheKeys.ORDER_DOCUMENTS_KEY], {
      documents: [mothAgreement]
    });
  });

  const renderSubject = async () => {
    await waitFor(async () => {
      const result = renderWithPoetProvidersAndState(<MothInspectionVariantSelector />);
      await runPendingPromises();
      return result;
    });
  };

  afterEach(() => {
    setOrdersWithSignedMothAgreements(null);
    vi.resetAllMocks();
  });

  describe('isMothPagination feature flag is disabled', async () => {
    beforeEach(() => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isMothPaginationEnabled: () => false })
      );
    });
    it('should show the non-paginated Moth inspection form', async () => {
      await renderSubject();

      const nonPaginated = screen.getByTestId('moth-form-non-paginated');

      expect(nonPaginated).toBeInTheDocument();
    });

    it('should navigate back when back button clicked', async () => {
      await renderSubject();
      const backBtn = await screen.findByTestId('back-link');

      await waitFor(() => user.click(backBtn));

      expect(mockNavigate).toHaveBeenCalledWith(-1);
    });

    it('should navigate back when previous button clicked', async () => {
      await renderSubject();

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));

      const button = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.PREVIOUS_BUTTON
      });

      await waitFor(() => user.click(button));
      expect(mockNavigate).toHaveBeenCalledWith(-1);
    });

    it('when user clicks sign, enable agree button', async () => {
      await renderSubject();

      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const agreeButton = await screen.findByRole('button', {
        name: TranslationKeys.MothInspectionFormPage.AGREE_BUTTON
      });
      const addSignatureButton = await screen.findByText(
        TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
      );
      expect(agreeButton).toBeDisabled();

      await waitFor(() => user.click(addSignatureButton));

      expect(agreeButton).toBeEnabled();
    });

    it('when checkboxes are selected, and the form is submitted, then request is made to sign moth agreement with expected request', async () => {
      const expectedRequest: SignMothAgreementRequest = createSignMothAgreementRequest(customer, {
        orderId: orderId,
        selectedCheckboxes: ['bicycles-tricycles-scooters', 'sandboxes']
      });
      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const bikeCheckbox = await screen.findByLabelText('Bicycles, Tricycles, Scooters');
      const sandboxCheckbox = await screen.findByLabelText('Sandboxes');
      await waitFor(() => user.click(sandboxCheckbox));
      await waitFor(() => user.click(bikeCheckbox));

      await actions.submitForm();

      expect(mockSignMothAgreement).toHaveBeenCalledWith(expectedRequest);
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
      expectNotificationAlertContainsTitle(TranslationKeys.MothInspectionFormPage.SUCCESS_MESSAGE);
      expect(getOrdersWithSignedMothAgreements()).toContain(orderId);
    });

    it('when a restricted checkbox is selected, the form cannot be signed or submitted', async () => {
      const mockConfig = [
        {
          titleKey: 'Recreational, Child Play things',
          subcategories: [
            {
              subtitleKey: "Children's Playthings",
              checkboxes: [
                {
                  id: 'bicycles-tricycles-scooters',
                  label: 'Foo',
                  pdfKey: 'Bicycles Tricycles Scooters',
                  checked: false,
                  restricted: true
                }
              ]
            }
          ]
        }
      ];

      const mothFormConfigSpy = vi
        .spyOn(exports, 'mothFormConfig', 'get')
        .mockReturnValue(mockConfig);

      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const bikeCheckbox = await screen.findByLabelText('Foo');

      await waitFor(() => user.click(bikeCheckbox));

      const agreeButton = await screen.findByRole('button', {
        name: TranslationKeys.MothInspectionFormPage.AGREE_BUTTON
      });
      const addSignatureButton = await screen.findByText(
        TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
      );
      const restrictedTitleButton = await screen.findByText(
        TranslationKeys.MothInspectionFormPage.LegalCopy.RESTRICTED_ITEM_TITLE
      );
      expect(agreeButton).toBeDisabled();
      expect(addSignatureButton).toBeDisabled();
      expect(restrictedTitleButton).toBeInTheDocument();

      mothFormConfigSpy.mockRestore();
    });

    it('when "Other" checkboxes are selected & filled, and the form is submitted, then request is made to sign moth agreement with expected request', async () => {
      const expectedRequest: SignMothAgreementRequest = createSignMothAgreementRequest(customer, {
        orderId: orderId,
        otherCheckboxes: [{ id: 'other-child-playthings', description: 'ok' }]
      });
      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const otherCheckbox = (await screen.findAllByLabelText('Other'))[0];
      await waitFor(() => user.click(otherCheckbox));
      const otherTextField = await screen.findByRole('textbox', { name: /other/i });
      await waitFor(() => expect(otherTextField).toBeInTheDocument());
      await waitFor(() => user.type(otherTextField, 'ok'));

      await actions.submitForm();

      expect(mockSignMothAgreement).toHaveBeenCalledWith(expectedRequest);
      expectNotificationAlertContainsTitle(TranslationKeys.MothInspectionFormPage.SUCCESS_MESSAGE);
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
    });

    it('when the initial move leg is "self-initial delivery", then the request is made with my billing address', async () => {
      const deliveryMoveLeg = createMoveLegApi({ moveLegType: 'SELF_INITIAL_DELIVERY' });
      const orderApi = createOrderApi({
        orderId: '12354',
        containers: [createContainerApi({ moveLegs: [deliveryMoveLeg] })]
      });
      const expectedRequest: SignMothAgreementRequest = createSignMothAgreementRequest(customer, {
        orderId: orderApi.orderId,
        address: formatAddress(customer.billingAddress),
        selectedCheckboxes: ['sandboxes']
      });
      mockGetCustomerOrders.mockResolvedValue([customerOrder]);
      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const sandboxCheckbox = await screen.findByLabelText('Sandboxes');
      await waitFor(() => user.click(sandboxCheckbox));

      await actions.submitForm();

      expect(mockSignMothAgreement).toHaveBeenCalledWith(expectedRequest);
      expectNotificationAlertContainsTitle(TranslationKeys.MothInspectionFormPage.SUCCESS_MESSAGE);
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
    });

    it('when the initial move leg is "delivery", then the request is made with the origin address', async () => {
      const deliveryMoveLeg = createMoveLeg({
        moveLegType: 'INITIAL_DELIVERY',
        originationAddress: createMoveLegAddress({
          address1: '123 Lake Street',
          address2: '',
          city: 'Chicago',
          state: 'IL',
          postalCode: '12345'
        })
      });
      const order = createOrder({
        orderId: '12354',
        containers: [createContainer({ moveLegs: [deliveryMoveLeg] })]
      });
      const firstMoveLeg = order.containers[0].moveLegs[0];
      const expectedRequest: SignMothAgreementRequest = createSignMothAgreementRequest(customer, {
        orderId: order.orderId,
        address: formatAddress(firstMoveLeg.originationAddress),
        selectedCheckboxes: ['sandboxes']
      });
      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const sandboxCheckbox = await screen.findByLabelText('Sandboxes');
      await waitFor(() => user.click(sandboxCheckbox));

      await actions.submitForm();

      expect(mockSignMothAgreement).toHaveBeenCalledWith(expectedRequest);
      expectNotificationAlertContainsTitle(TranslationKeys.MothInspectionFormPage.SUCCESS_MESSAGE);
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
    });

    it('when form submission fails, display error snackbar', async () => {
      mockSignMothAgreement.mockRejectedValue(new Error('Something went wrong'));
      await renderSubject();
      const nextBtn = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(nextBtn));
      const sandboxCheckbox = await screen.findByLabelText('Sandboxes');
      await waitFor(() => user.click(sandboxCheckbox));

      await actions.submitForm();

      expectNotificationAlertContainsTitle(
        TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
      );
    });
  });
  describe('isMothPagination feature flag is enabled', async () => {
    beforeEach(() => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({
          isPoetEnabled: () => true,
          isMothPaginationEnabled: () => true
        })
      );

      const mockConfig = [
        {
          titleKey: 'Recreational, Child Play things',
          subcategories: [
            {
              subtitleKey: "Children's Playthings",
              checkboxes: [
                {
                  id: 'bicycles-tricycles-scooters',
                  label: 'Foo',
                  pdfKey: 'Bicycles Tricycles Scooters',
                  checked: false,
                  restricted: true
                }
              ]
            }
          ]
        }
      ];

      vi.spyOn(exports, 'mothFormConfig', 'get').mockReturnValue(mockConfig);
    });

    it('should display paginated version when paginated-moth-form flag is on', async () => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isMothPaginationEnabled: () => true })
      );
      await renderSubject();

      expect(
        await screen.findByText(TranslationKeys.MothInspectionFormPage.INTRO_DESC)
      ).toBeInTheDocument();
    });

    it('should have previous button when next button clicked', async () => {
      await renderSubject();
      const button = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });

      await waitFor(() => user.click(button));

      const preButton = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.PREVIOUS_BUTTON
      });
      expect(preButton).toBeInTheDocument();
    });

    it('should scroll to top when next button clicked', async () => {
      await renderSubject();
      const button = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });

      await waitFor(() => user.click(button));

      expect(mockScrollIntoView).toHaveBeenCalledTimes(2); // initial render + click next
    });

    it('when user clicks sign, enable agree button', async () => {
      await renderSubject();
      const button = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      await waitFor(() => user.click(button));
      const agreeButton = await screen.findByRole('button', {
        name: TranslationKeys.MothInspectionFormPage.AGREE_BUTTON
      });
      const addSignatureButton = await screen.findByText(
        TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
      );
      expect(agreeButton).toBeDisabled();

      await waitFor(() => user.click(addSignatureButton));

      expect(agreeButton).toBeEnabled();
    });

    it('when a restricted checkbox is selected, the form should show restricted item error message ', async () => {
      await renderSubject();
      const bikeCheckbox = await screen.findByLabelText('Foo');

      await waitFor(() => user.click(bikeCheckbox));

      const restrictedTitleButton = await screen.findByText(
        TranslationKeys.MothInspectionFormPage.RESTRICTED_ITEM_MESSAGE
      );
      expect(restrictedTitleButton).toBeInTheDocument();
    });

    it('when user is on the last page, the Agree button should show and the Next button should not be present', async () => {
      await renderSubject();
      const button = await screen.findByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });

      await waitFor(() => user.click(button));

      const agreeButton = await screen.findByRole('button', {
        name: TranslationKeys.MothInspectionFormPage.AGREE_BUTTON
      });
      const nextButton = screen.queryByRole('button', {
        name: TranslationKeys.CommonComponents.NEXT_BUTTON
      });
      expect(agreeButton).toBeInTheDocument();
      expect(nextButton).not.toBeInTheDocument();
    });
  });
});
