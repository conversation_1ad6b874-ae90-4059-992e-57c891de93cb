import { useTranslation } from 'react-i18next';
import { Grid, TextField, Typography, useMediaQuery } from '@mui/material';
import React, { ChangeEvent } from 'react';
import { Updater } from 'use-immer';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { theme } from '../../../PodsTheme';
import { Design } from '../../../helpers/Design';
import { MothCategoryState, MothCheckboxConfig } from '../MothInspectionFormTypes';
import { CheckBoxItem } from '../CheckBoxItem';

interface Props {
  category: MothCategoryState;
  titleIndex: number;
  checkboxState: MothCheckboxConfig;
  setCheckboxState: Updater<MothCheckboxConfig>;
}

export const MothFlyInspectionSubSection = ({ category, titleIndex, setCheckboxState }: Props) => {
  const { t: translate } = useTranslation();
  const Tx = TranslationKeys.MothInspectionFormPage;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = legalStyles(isMobile);
  const OTHER_CHECKBOX_LABEL = 'Other';

  const handleOtherTextChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    categoryIndex: number,
    subcategoryIndex: number,
    checkboxIndex: number
  ) => {
    setCheckboxState((mothCategoryState) => {
      const checkboxToUpdate =
        mothCategoryState[categoryIndex].subcategories[subcategoryIndex].checkboxes[checkboxIndex];
      checkboxToUpdate.description = event.target.value;
    });
  };

  const handleCheckboxChange = (
    categoryIndex: number,
    subcategoryIndex: number,
    checkboxIndex: number
  ) => {
    setCheckboxState((prev) => {
      // @ts-ignore
      const checkboxToUpdate =
        prev[categoryIndex].subcategories[subcategoryIndex].checkboxes[checkboxIndex];
      checkboxToUpdate.checked = !checkboxToUpdate.checked;
    });
  };

  return (
    <Grid item key={category.titleKey} gap={6} {...styles.category}>
      <Typography {...styles.categoryTitle}>{category.titleKey}</Typography>
      <Typography>{translate(Tx.INSTRUCTIONS)}</Typography>
      <Grid container flexDirection="column" gap={2} {...styles.subcategory}>
        {category.subcategories.map((subcategory, subcategoryIndex) => (
          <Grid container item flexDirection="column" key={subcategory.subtitleKey}>
            <Grid item>
              <Typography variant="h6" {...styles.subcategoryTitle}>
                {subcategory.subtitleKey}
              </Typography>
            </Grid>

            <Grid item container flexDirection="row" {...styles.checkboxesContainer}>
              {subcategory.checkboxes.map((item, checkboxIndex) => {
                function onChange() {
                  handleCheckboxChange(titleIndex, subcategoryIndex, checkboxIndex);
                }

                if (item.label === OTHER_CHECKBOX_LABEL)
                  return (
                    <Grid container key={item.id}>
                      <Grid container item direction="column" gap={Design.Primitives.Spacing.sm}>
                        <CheckBoxItem key={item.id} item={item} onChange={() => onChange()} />
                        {item.checked && (
                          <TextField
                            label="Other"
                            color="secondary"
                            value={item.description}
                            onChange={(event) =>
                              handleOtherTextChange(
                                event,
                                titleIndex,
                                subcategoryIndex,
                                checkboxIndex
                              )
                            }
                          />
                        )}
                      </Grid>
                    </Grid>
                  );
                return <CheckBoxItem key={item.id} item={item} onChange={() => onChange()} />;
              })}
            </Grid>
          </Grid>
        ))}
      </Grid>
    </Grid>
  );
};

const legalStyles = (isMobile: boolean) => ({
  category: {
    sx: {
      color: Design.Alias.Color.accent900
    }
  },
  categoryTitle: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Lg : Design.Alias.Text.Heading.Desktop.Lg),
      color: 'inherit'
    }
  },
  subcategory: {
    color: 'inherit',
    paddingTop: Design.Primitives.Spacing.md
  },
  subcategoryTitle: {
    sx: {
      paddingBottom: isMobile ? Design.Primitives.Spacing.sm : Design.Primitives.Spacing.xxs,
      color: 'inherit'
    }
  },
  checkboxesContainer: {
    gap: '14px' /* Setting 14px gap to arrange the items correctly for all screen sizes */
  }
});
