import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MothFlyInspectionSubSection } from '../MothFlyInspectionSubSection';
import { vi } from 'vitest';
import { TranslationKeys } from '../../../../locales/TranslationKeys';

const mockCategory = {
  titleKey: 'Test Category',
  subcategories: [
    {
      subtitleKey: 'Test Subcategory',
      checkboxes: [
        {
          id: 'test-checkbox-1',
          label: 'Test Checkbox 1',
          pdfKey: 'test-pdf-key-1',
          checked: false
        },
        {
          id: 'other-checkbox',
          label: 'Other',
          pdfKey: 'other-pdf-key',
          checked: false,
          description: ''
        }
      ]
    }
  ]
};

const mockCategoryWithCheckedOther = {
  titleKey: 'Test Category',
  subcategories: [
    {
      subtitleKey: 'Test Subcategory',
      checkboxes: [
        {
          id: 'test-checkbox-1',
          label: 'Test Checkbox 1',
          pdfKey: 'test-pdf-key-1',
          checked: false
        },
        {
          id: 'other-checkbox',
          label: 'Other',
          pdfKey: 'other-pdf-key',
          checked: true,
          description: ''
        }
      ]
    }
  ]
};

const mockCategoryWithOtherDescription = {
  titleKey: 'Test Category',
  subcategories: [
    {
      subtitleKey: 'Test Subcategory',
      checkboxes: [
        {
          id: 'test-checkbox-1',
          label: 'Test Checkbox 1',
          pdfKey: 'test-pdf-key-1',
          checked: false
        },
        {
          id: 'other-checkbox',
          label: 'Other',
          pdfKey: 'other-pdf-key',
          checked: true,
          description: 'Existing description'
        }
      ]
    }
  ]
};

const setCheckboxState = vi.fn();

describe('MothFlyInspectionSubSection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should display category and subcategory titles', () => {
    render(
      <MothFlyInspectionSubSection
        category={mockCategory}
        titleIndex={0}
        checkboxState={[]}
        setCheckboxState={setCheckboxState}
      />
    );

    expect(screen.getByText(mockCategory.titleKey)).toBeInTheDocument();
    expect(screen.getByText(mockCategory.subcategories[0].subtitleKey)).toBeInTheDocument();
    expect(
      screen.getByText(TranslationKeys.MothInspectionFormPage.INSTRUCTIONS)
    ).toBeInTheDocument();
  });

  it('should display checkboxes', () => {
    render(
      <MothFlyInspectionSubSection
        category={mockCategory}
        titleIndex={0}
        checkboxState={[]}
        setCheckboxState={setCheckboxState}
      />
    );

    expect(screen.getByRole('checkbox', { name: 'Test Checkbox 1' })).toBeInTheDocument();
    expect(screen.getByRole('checkbox', { name: 'Other' })).toBeInTheDocument();
  });

  it('should call setCheckboxState when checkbox is clicked', async () => {
    const user = userEvent.setup();

    render(
      <MothFlyInspectionSubSection
        category={mockCategory}
        titleIndex={0}
        checkboxState={[]}
        setCheckboxState={setCheckboxState}
      />
    );

    const checkbox = screen.getByRole('checkbox', { name: 'Test Checkbox 1' });
    await user.click(checkbox);

    expect(setCheckboxState).toHaveBeenCalledTimes(1);
  });

  it('should not display text field when Other checkbox is unchecked', () => {
    render(
      <MothFlyInspectionSubSection
        category={mockCategory}
        titleIndex={0}
        checkboxState={[]}
        setCheckboxState={setCheckboxState}
      />
    );

    expect(
      screen.queryByLabelText('Other', { selector: 'input[type="text"]' })
    ).not.toBeInTheDocument();
  });

  it('should display text field when Other checkbox is checked', () => {
    render(
      <MothFlyInspectionSubSection
        category={mockCategoryWithCheckedOther}
        titleIndex={0}
        checkboxState={[]}
        setCheckboxState={setCheckboxState}
      />
    );

    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('should call setCheckboxState when text is entered in Other field', async () => {
    const user = userEvent.setup();

    render(
      <MothFlyInspectionSubSection
        category={mockCategoryWithCheckedOther}
        titleIndex={0}
        checkboxState={[]}
        setCheckboxState={setCheckboxState}
      />
    );

    const textField = screen.getByRole('textbox');
    await user.type(textField, 'Test description');

    expect(setCheckboxState).toHaveBeenCalled();
  });

  it('should display existing description in Other field when checkbox is checked', () => {
    render(
      <MothFlyInspectionSubSection
        category={mockCategoryWithOtherDescription}
        titleIndex={0}
        checkboxState={[]}
        setCheckboxState={setCheckboxState}
      />
    );

    const textField = screen.getByRole('textbox');
    expect(textField).toHaveValue('Existing description');
  });
});
