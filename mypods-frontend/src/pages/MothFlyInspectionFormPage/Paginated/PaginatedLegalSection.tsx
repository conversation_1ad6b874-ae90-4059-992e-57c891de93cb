import { useTranslation } from 'react-i18next';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { ReactNode } from 'react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { theme } from '../../../PodsTheme';
import { Design } from '../../../helpers/Design';
import { PodsAlert, PodsAlertType } from '../../../components/alert/PodsAlert';

export const PaginatedLegalSection = ({
  isRestrictedSelected = false,
  children
}: {
  isRestrictedSelected: boolean;
  children?: ReactNode;
}) => {
  const { t: translate } = useTranslation();
  const Tx = TranslationKeys.MothInspectionFormPage.LegalCopy;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = legalStyles(isMobile);

  return (
    <>
      <Grid container gap={2}>
        {isRestrictedSelected && (
          <PodsAlert
            alertType={PodsAlertType.ERROR}
            title={translate(Tx.RESTRICTED_ITEM_TITLE)}
            description={translate(Tx.RESTRICTED_ITEM_DETAILS)}></PodsAlert>
        )}

        <Typography {...styles.legalCopySubtitle}>{translate(Tx.SUBTITLE)}</Typography>

        <Typography variant="subtitle1" {...styles.legalCopyDescription}>
          {translate(Tx.DESCRIPTION)}
        </Typography>
      </Grid>
      <Grid item {...styles.signatureContainer}>
        {children}
      </Grid>
    </>
  );
};

const legalStyles = (isMobile: boolean) => ({
  legalCopySubtitle: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Lg : Design.Alias.Text.Heading.Desktop.Lg),
      marginBottom: Design.Primitives.Spacing.xxs,
      color: Design.Alias.Color.accent900
    }
  },
  legalCopyDescription: {
    marginBottom: Design.Primitives.Spacing.sm
  },
  signatureContainer: {
    sx: {
      paddingTop: '32px',
      maxWidth: isMobile ? '100%' : '344px'
    }
  }
});
