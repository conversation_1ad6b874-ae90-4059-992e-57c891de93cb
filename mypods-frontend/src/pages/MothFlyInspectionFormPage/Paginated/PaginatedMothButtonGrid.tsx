import React from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { Button as PodsButton } from 'pods-component-library';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';
import { FixedBottomContainer } from '../../../components/FixedBottomContainer';
import { theme } from '../../../PodsTheme';

interface Props {
  isFirstStep: boolean;
  isLastStep: boolean;
  isPending: boolean;
  isSigned: boolean;
  hasRestrictedItems: boolean;
  handlePrevious: () => void;
  handleNext: () => void;
  handleSubmit: () => void;
}

export const PaginatedMothButtonGrid = ({
  isFirstStep,
  isLastStep,
  isPending,
  isSigned,
  hasRestrictedItems,
  handlePrevious,
  handleNext,
  handleSubmit
}: Props) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const Tx = TranslationKeys.MothInspectionFormPage;
  const { t: translate } = useTranslation();

  const renderButtons: () => React.ReactNode = () => (
    <Grid container gap={3} {...mothButtonStyles.buttonsContainer}>
      {!isFirstStep && (
        <Grid item {...mothButtonStyles.buttonGrid}>
          <PodsButton
            variant="outlined"
            color="primary"
            buttonSize="large"
            onPress={handlePrevious}>
            {translate(TranslationKeys.CommonComponents.PREVIOUS_BUTTON)}
          </PodsButton>
        </Grid>
      )}

      {!isLastStep && (
        <Grid item {...mothButtonStyles.buttonGrid}>
          <PodsButton
            variant="filled"
            color="primary"
            data-testid="paginated-moth-next-button"
            buttonSize="large"
            onPress={handleNext}>
            {translate(TranslationKeys.CommonComponents.NEXT_BUTTON)}
          </PodsButton>
        </Grid>
      )}

      {isLastStep && (
        <Grid item {...mothButtonStyles.buttonGrid}>
          <LoadingButton
            variant="contained"
            loading={isPending}
            disabled={!isSigned || hasRestrictedItems}
            onClick={handleSubmit}
            {...mothButtonStyles.agreeButton}>
            {translate(Tx.AGREE_BUTTON)}
          </LoadingButton>
        </Grid>
      )}
    </Grid>
  );

  return isMobile ? (
    <FixedBottomContainer>{renderButtons()}</FixedBottomContainer>
  ) : (
    renderButtons()
  );
};

// -- styles --
const mothButtonStyles = {
  buttonsContainer: {
    sx: {
      justifyContent: 'flex-end',
      flexWrap: 'nowrap'
    }
  },
  agreeButton: {
    sx: {
      width: '100%',
      height: '48px',
      backgroundColor: Design.Alias.Color.secondary500
    }
  },
  buttonGrid: {
    lg: 2,
    md: 3,
    sm: 6,
    xs: 6
  }
};
