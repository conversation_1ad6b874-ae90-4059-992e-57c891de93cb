import React from 'react';
import { Box, Grid } from '@mui/material';
import { Design } from '../../../helpers/Design';

interface Props {
  totalSteps: number;
  activeStep: number;
}

export const StepTracker = ({ totalSteps, activeStep }: Props) => (
  <Grid container gap={5}>
    <Box gap={2} {...mothButtonStyles.stepsContainer}>
      {[...Array(totalSteps + 1)].map((_, stepIndex) => {
        const isActive = activeStep >= stepIndex;
        return (
          <Box
            key={stepIndex}
            {...mothButtonStyles.stepIndicator}
            style={{
              backgroundColor: isActive
                ? Design.Primitives.Color.Blue.oasis
                : Design.Alias.Color.neutral300
            }}
          />
        );
      })}
    </Box>

    <Grid container {...mothButtonStyles.stepTracker}>
      {activeStep + 1} of {totalSteps + 1}
    </Grid>
  </Grid>
);

// -- styles --
const mothButtonStyles = {
  stepsContainer: {
    sx: {
      width: '100%',
      display: 'flex'
    }
  },
  stepTracker: {
    sx: {
      fontSize: '18px',
      fontWeight: '700',
      justifyContent: 'start',
      alignSelf: 'stretch'
    }
  },
  stepIndicator: {
    sx: {
      width: '13%',
      height: '4px',
      borderRadius: '2px'
    }
  }
};
