import { Grid, TextField, Typography, useMediaQuery } from '@mui/material';
import React, { ChangeEvent, useEffect, useState } from 'react';
import Button from '@mui/material/Button';
import { useTranslation } from 'react-i18next';
import { LoadingButton } from '@mui/lab';
import { useNavigate } from 'react-router-dom';
import { Updater } from 'use-immer';
import { BackLink } from '../../components/buttons/BackLink';
import { Design } from '../../helpers/Design';
import { theme } from '../../PodsTheme';
import { FixedBottomContainer } from '../../components/FixedBottomContainer';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { MothCategoryState, MothCheckboxConfig } from './MothInspectionFormTypes';
import { isNonProdEnv } from '../DebugPage/DebugPage';
import { LegalSection } from './LegalSection';
import { CheckBoxItem } from './CheckBoxItem';
import { ViewAgreementSteps } from '../../config/google/GoogleEntities';
import { Customer } from '../../networkRequests/responseEntities/CustomerEntities';
import { useGtmEventsWithCustomer } from '../../config/google/useGtmEvents';

const Tx = TranslationKeys.MothInspectionFormPage;
const OTHER_CHECKBOX_LABEL = 'Other';

interface Props {
  handleSubmit: () => void;
  checkboxState: MothCheckboxConfig;
  setCheckboxState: Updater<MothCheckboxConfig>;
  orderId: string;
  isPending: boolean;
  customer: Customer;
}

export const SinglePageMothFlyInspectionForm = ({
  handleSubmit,
  checkboxState,
  setCheckboxState,
  orderId,
  isPending,
  customer
}: Props) => {
  const gtmEvents = useGtmEventsWithCustomer(customer);
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t: translate } = useTranslation();
  const [isSigned, setIsSigned] = useState(false);
  const styles = mothStyles(isMobile);

  useEffect(() => {
    gtmEvents.viewAgreementStep(ViewAgreementSteps.moth.form, orderId);
  }, []);

  const selectAll = () => {
    checkboxState.forEach((category: MothCategoryState, categoryIndex) => {
      category.subcategories.forEach((subCategories, subcategoryIndex) => {
        subCategories.checkboxes.forEach((_, checkboxIndex) => {
          handleCheckboxChange(categoryIndex, subcategoryIndex, checkboxIndex);
        });
      });
    });
  };

  const hasRestrictedItems = !!checkboxState.find(
    (category: MothCategoryState) =>
      !!category.subcategories.find(
        (subcategory) =>
          !!subcategory.checkboxes.find((checkbox) => checkbox.checked && checkbox.restricted)
      )
  );

  const handleCheckboxChange = (
    categoryIndex: number,
    subcategoryIndex: number,
    checkboxIndex: number
  ) => {
    setCheckboxState((prev) => {
      // @ts-ignore
      const checkboxToUpdate =
        prev[categoryIndex].subcategories[subcategoryIndex].checkboxes[checkboxIndex];
      checkboxToUpdate.checked = !checkboxToUpdate.checked;
    });
  };

  const handleOtherTextChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    categoryIndex: number,
    subcategoryIndex: number,
    checkboxIndex: number
  ) => {
    setCheckboxState((mothCategoryState) => {
      const checkboxToUpdate =
        mothCategoryState[categoryIndex].subcategories[subcategoryIndex].checkboxes[checkboxIndex];
      checkboxToUpdate.description = event.target.value;
    });
  };

  const renderButtons = () => (
    <Grid container justifyContent="center">
      <Grid container {...styles.buttonInnerContainer} spacing={2}>
        <Grid item xs={6}>
          <Button variant="outlined" onClick={() => navigate(-1)} {...styles.previousButton}>
            {translate(TranslationKeys.CommonComponents.PREVIOUS_BUTTON)}
          </Button>
        </Grid>
        <Grid item xs={6}>
          <LoadingButton
            variant="contained"
            loading={isPending}
            disabled={!isSigned || hasRestrictedItems}
            onClick={handleSubmit}
            {...styles.agreeButton}>
            {translate(Tx.AGREE_BUTTON)}
          </LoadingButton>
        </Grid>
      </Grid>
    </Grid>
  );

  return (
    <>
      <Grid>
        <BackLink route={-1} />
      </Grid>
      {isNonProdEnv() && (
        <Grid sx={{ paddingTop: '16px' }}>
          <Button variant="contained" color="secondary" onClick={selectAll}>
            Select All
          </Button>
        </Grid>
      )}
      <Grid container flexDirection="column">
        {checkboxState.map((category, titleIndex) => (
          <Grid item key={category.titleKey} {...styles.category}>
            <Typography {...styles.categoryTitle}>{category.titleKey}</Typography>
            <Typography {...styles.selectAll}>{translate(Tx.INSTRUCTIONS)}</Typography>
            <Grid container flexDirection="column" {...styles.subcategory}>
              {category.subcategories.map((subcategory, subcategoryIndex) => (
                <Grid container item flexDirection="column" key={subcategory.subtitleKey}>
                  <Grid item>
                    <Typography variant="h6" {...styles.subcategoryTitle}>
                      {subcategory.subtitleKey}
                    </Typography>
                  </Grid>
                  <Grid item container flexDirection="row" {...styles.checkboxesContainer}>
                    {subcategory.checkboxes.map((item, checkboxIndex) => {
                      function onChange() {
                        handleCheckboxChange(titleIndex, subcategoryIndex, checkboxIndex);
                      }

                      if (item.label === OTHER_CHECKBOX_LABEL)
                        return (
                          <Grid container key={item.id}>
                            <Grid
                              container
                              item
                              direction="column"
                              gap={Design.Primitives.Spacing.sm}>
                              <CheckBoxItem key={item.id} item={item} onChange={() => onChange()} />
                              {item.checked && (
                                <TextField
                                  label="Other"
                                  color="secondary"
                                  onChange={(event) =>
                                    handleOtherTextChange(
                                      event,
                                      titleIndex,
                                      subcategoryIndex,
                                      checkboxIndex
                                    )
                                  }
                                />
                              )}
                            </Grid>
                          </Grid>
                        );
                      return <CheckBoxItem key={item.id} item={item} onChange={() => onChange()} />;
                    })}
                  </Grid>
                </Grid>
              ))}
            </Grid>
          </Grid>
        ))}
      </Grid>
      <LegalSection
        isSigned={isSigned}
        isRestrictedSelected={hasRestrictedItems}
        handleSignClicked={() => setIsSigned(true)}
        customer={customer}
      />
      <Grid {...styles.buttonsOuterContainer}>
        {isMobile ? (
          <FixedBottomContainer>{renderButtons()}</FixedBottomContainer>
        ) : (
          renderButtons()
        )}
      </Grid>
    </>
  );
};

const mothStyles = (isMobile: boolean) => ({
  category: {
    sx: {
      paddingTop: Design.Primitives.Spacing.lg,
      color: Design.Alias.Color.accent900
    }
  },
  categoryTitle: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Lg : Design.Alias.Text.Heading.Desktop.Lg),
      paddingY: Design.Primitives.Spacing.xxs,
      color: 'inherit'
    }
  },
  subcategory: {
    gap: '1rem',
    color: 'inherit'
  },
  selectAll: {
    sx: {
      fontSize: isMobile ? Design.Alias.Text.BodyUniversal.Sm : Design.Alias.Text.BodyUniversal.Lg,
      paddingBottom: Design.Primitives.Spacing.lgPlus
    }
  },
  subcategoryTitle: {
    sx: {
      paddingBottom: isMobile ? Design.Primitives.Spacing.sm : Design.Primitives.Spacing.xxs,
      color: 'inherit'
    }
  },
  checkboxesContainer: {
    gap: '1rem'
  },
  buttonsOuterContainer: {
    sx: {
      paddingTop: Design.Primitives.Spacing.lgPlus,
      ...(!isMobile && { paddingBottom: Design.Primitives.Spacing.lgPlus })
    }
  },
  buttonInnerContainer: {
    sx: {
      maxWidth: isMobile ? undefined : '400px'
    }
  },
  previousButton: {
    sx: {
      width: '100%',
      color: Design.Alias.Color.secondary500,
      backgroundColor: Design.Alias.Color.neutral100
    }
  },
  agreeButton: {
    sx: {
      width: '100%',
      backgroundColor: Design.Alias.Color.secondary500
    }
  }
});
