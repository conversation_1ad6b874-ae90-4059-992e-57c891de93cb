import { Updater } from 'use-immer';
import React, { useState } from 'react';
import { PageLayout } from '../../components/PageLayout';
import { MothInspectionPage } from '../MothInspectionPage/MothInspectionPage';
import { MothCheckboxConfig } from './MothInspectionFormTypes';
import { SinglePageMothFlyInspectionForm } from './SinglePageMothFlyInspectionForm';
import { Customer } from '../../networkRequests/responseEntities/CustomerEntities';
import { useGtmEventsWithCustomer } from '../../config/google/useGtmEvents';

interface Props {
  handleSubmit: () => void;
  checkboxState: MothCheckboxConfig;
  setCheckboxState: Updater<MothCheckboxConfig>;
  firstOutstandingMothAgreementOrderId: string;
  isPending: boolean;
  customer: Customer;
}

export const NonPaginatedMothContainer = ({
  handleSubmit,
  checkboxState,
  setCheckboxState,
  firstOutstandingMothAgreementOrderId,
  isPending,
  customer
}: Props) => {
  type PageStep = 'INTRO_PAGE' | 'FORM';
  const [currentStep, setCurrentStep] = useState<PageStep>('INTRO_PAGE');
  const gtmEvents = useGtmEventsWithCustomer(customer);
  const handleNextClick = () => {
    setCurrentStep('FORM');
  };

  return (
    <PageLayout columnsLg={12}>
      {currentStep === 'FORM' ? (
        <SinglePageMothFlyInspectionForm
          checkboxState={checkboxState}
          setCheckboxState={setCheckboxState}
          handleSubmit={handleSubmit}
          orderId={firstOutstandingMothAgreementOrderId}
          isPending={isPending}
          customer={customer}
        />
      ) : (
        <MothInspectionPage
          handleNextButtonClick={handleNextClick}
          orderId={firstOutstandingMothAgreementOrderId}
          viewAgreementStep={gtmEvents.viewAgreementStep}
          startAgreement={gtmEvents.startAgreement}
        />
      )}
    </PageLayout>
  );
};
