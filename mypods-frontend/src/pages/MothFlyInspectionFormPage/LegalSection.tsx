import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardMedia, Grid, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { theme } from '../../PodsTheme';
import { Design } from '../../helpers/Design';
import { AddSignatureButtonCustomer } from '../../components/buttons/AddSignature';
import { PodsAlert, PodsAlertType } from '../../components/alert/PodsAlert';
import { Customer } from '../../networkRequests/responseEntities/CustomerEntities';

export const LegalSection = ({
  isSigned,
  isRestrictedSelected = false,
  handleSignClicked,
  customer
}: {
  isSigned: boolean;
  handleSignClicked: () => void;
  isRestrictedSelected: boolean;
  customer: Customer;
}) => {
  const { t: translate } = useTranslation();
  const Tx = TranslationKeys.MothInspectionFormPage.LegalCopy;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = legalStyles(isMobile);

  return (
    <>
      <Grid>
        <Grid>
          <Typography variant="h3" {...styles.legalTitle}>
            {translate(Tx.TITLE)}
          </Typography>
        </Grid>
        <Grid {...styles.legalImageContainer}>
          <Card>
            <CardMedia
              {...styles.mothImage}
              component="img"
              image="https://prodmypods.azureedge.net/-/media/pods/button-row/gypsy.jpg"
              alt="Moth Image 1"
            />
            <CardContent>
              <Typography gutterBottom variant="h5" component="div">
                {translate(Tx.IMAGE1_CAPTION)}
              </Typography>
            </CardContent>
          </Card>
          <Card>
            <CardMedia
              {...styles.mothImage}
              component="img"
              image="https://prodmypods.azureedge.net/-/media/pods/button-row/lanternfly.jpg"
              alt="Moth Image 2"
            />
            <CardContent>
              <Typography gutterBottom variant="h5" component="div">
                {translate(Tx.IMAGE2_CAPTION)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {isRestrictedSelected && (
          <Grid sx={{ marginBottom: '24px' }}>
            <PodsAlert
              alertType={PodsAlertType.ERROR}
              title={translate(Tx.RESTRICTED_ITEM_TITLE)}
              description={translate(Tx.RESTRICTED_ITEM_DETAILS)}></PodsAlert>
          </Grid>
        )}

        <Grid>
          <Typography {...styles.legalCopySubtitle}>{translate(Tx.SUBTITLE)}</Typography>
        </Grid>
        <Grid>
          <Typography variant="subtitle1" {...styles.legalCopyDescription}>
            {translate(Tx.DESCRIPTION)}
          </Typography>
        </Grid>
      </Grid>
      <Grid item {...styles.signatureContainer}>
        <AddSignatureButtonCustomer
          isSigned={isSigned}
          handleSignClicked={handleSignClicked}
          isDisabled={isRestrictedSelected}
          customer={customer}
        />
      </Grid>
    </>
  );
};

const legalStyles = (isMobile: boolean) => ({
  legalTitle: {
    sx: {
      marginBottom: Design.Primitives.Spacing.sm,
      marginTop: Design.Primitives.Spacing.lg // TODO: remove when Legal is on another page
    }
  },
  legalCopySubtitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.MdBold,
      marginBottom: Design.Primitives.Spacing.xxs
    }
  },
  legalImageContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      gap: isMobile ? Design.Primitives.Spacing.sm : Design.Primitives.Spacing.md,
      marginBottom: Design.Primitives.Spacing.md
    }
  },
  mothImage: {
    width: isMobile ? '169px' : '240px',
    height: isMobile ? '143px' : '202px'
  },
  legalCopyDescription: {
    marginBottom: Design.Primitives.Spacing.sm
  },
  signatureContainer: {
    sx: {
      paddingTop: '1rem',
      maxWidth: isMobile ? '100%' : '344px'
    }
  }
});
