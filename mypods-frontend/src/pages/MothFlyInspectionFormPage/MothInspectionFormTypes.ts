export type MothCategoryState = {
  titleKey: string;
  subcategories: MothSubcategoryState[];
};
export type MothSubcategoryState = {
  subtitleKey: string;
  checkboxes: MothCheckboxState[];
};
export type MothCheckboxState = {
  id: string;
  label: string;
  pdfKey: string;
  checked: boolean;
  description?: string;
  restricted?: boolean;
};
export type MothCheckboxConfig = MothCategoryState[];
