import { useTranslation } from 'react-i18next';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { formatByCurrency } from '../../../networkRequests/responseEntities/BillingEntities';
import { Design } from '../../../helpers/Design';
import { theme } from '../../../PodsTheme';
import { HeaderCardWrapper } from './HeaderCardWrapper';
import { BlueLink } from '../../../components/buttons/NavigationLink';
import { ROUTES } from '../../../Routes';
import { useGetBillingInformation } from '../../../networkRequests/queries/useGetBillingInformation';

const formatBalance = (balance: number) => {
  const prefix = balance < 0 ? '+' : '';
  return prefix + formatByCurrency(Math.abs(balance), 'USD');
};

export const AccountBalanceCard = () => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = style(isMobile);
  const {
    isRefetching,
    billingInformation: { totalBalance }
  } = useGetBillingInformation();
  const isCredit = totalBalance < 0;
  const canMakePayment = totalBalance > 0;

  return (
    <HeaderCardWrapper>
      <Grid container {...styles.totalAccountBalance}>
        <Typography variant={isMobile ? 'h4' : 'h5'} {...styles.accountHeading}>
          {translate(
            isCredit
              ? TranslationKeys.BillingPage.TOTAL_ACCOUNT_CREDIT
              : TranslationKeys.BillingPage.TOTAL_ACCOUNT_BALANCE
          )}
        </Typography>
        {isRefetching ? (
          <Typography {...styles.pendingPayment}>
            {translate(TranslationKeys.BillingPage.PROCESSING_PAYMENT)}
          </Typography>
        ) : (
          <Typography {...styles.accountBalance}>{formatBalance(totalBalance)}</Typography>
        )}
        {canMakePayment && (
          <BlueLink to={ROUTES.MAKE_PAYMENT}>
            {translate(TranslationKeys.BillingPage.MAKE_PAYMENT_LINK)}
          </BlueLink>
        )}
      </Grid>
    </HeaderCardWrapper>
  );
};

const style = (isMobile: boolean) => ({
  accountHeading: {
    sx: {
      color: Design.Alias.Color.accent900
    }
  },
  pendingPayment: {
    sx: {
      color: '#0E2E3B', // TODO: change color or add to design theme
      ...Design.Alias.Text.Heading.Desktop.Lg
    }
  },
  accountBalance: {
    sx: {
      color: '#0E2E3B', // TODO: change color or add to design theme
      ...Design.Alias.Text.Heading.Desktop.Xl
    }
  },
  totalAccountBalance: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      justifyContent: 'flex-start',
      gap: isMobile ? Design.Primitives.Spacing.md : '32px'
    }
  }
});
