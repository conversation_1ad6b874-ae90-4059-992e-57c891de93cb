import { PrimaryPaymentMethodCard } from '../PrimaryPaymentMethodCard';
import { screen, waitFor } from '@testing-library/react';
import { renderWithPoetProvidersAndState } from '../../../../testUtils/RenderHelpers';
import { mockGetPaymentMethods, mockRefreshSession } from '../../../../../setupTests';
import {
  createPaymentMethod,
  createRefreshSessionClaims
} from '../../../../testUtils/MyPodsFactories';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { ROUTES } from '../../../../Routes';
import React from 'react';
import { PaymentMethod } from '../../../../networkRequests/responseEntities/PaymentEntities';

describe('PrimaryPaymentMethodCard', () => {
  const Tx = TranslationKeys.BillingPage.PaymentMethods.DefaultPaymentMethod;

  const renderPrimaryPaymentMethodCard = (
    primaryPaymentMethods: PaymentMethod[] = [createPaymentMethod()]
  ) => {
    mockGetPaymentMethods.mockResolvedValue(primaryPaymentMethods);
    return renderWithPoetProvidersAndState(<PrimaryPaymentMethodCard />);
  };

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
  });

  it('should render primary payment method card', async () => {
    const paymentMethods = [createPaymentMethod()];
    const defaultPaymentMethod = paymentMethods[0];

    renderPrimaryPaymentMethodCard(paymentMethods);

    expect(await screen.findByText(Tx.HEADER)).toBeInTheDocument();
    expect(
      await screen.findByText(defaultPaymentMethod.cardType, { exact: false })
    ).toBeInTheDocument();
    expect(
      await screen.findByText(defaultPaymentMethod.cardNumberLastFourDigits)
    ).toBeInTheDocument();
    expect(await screen.findByText(TranslationKeys.PaymentMethodsPage.DEFAULT)).toBeInTheDocument();
  });

  it('should render No Payments Found message if no primary payment method exists and show link to payment method page', async () => {
    renderPrimaryPaymentMethodCard([createPaymentMethod({ isPrimary: false })]);

    expect(await screen.findByText(Tx.NOT_FOUND)).toBeInTheDocument();
    const link = await screen.findByRole('link', {
      name: TranslationKeys.BillingPage.PaymentMethods.DefaultPaymentMethod
        .VIEW_PAYMENT_METHODS_LINK
    });
    expect(link.getAttribute('href')).toBe(ROUTES.VIEW_PAYMENT_METHODS);
  });

  it('should alert user if failure to retrieve primary payment method occurs', () => {
    mockGetPaymentMethods.mockRejectedValue({});

    renderPrimaryPaymentMethodCard();

    const genericFailure = TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE;
    waitFor(() => expect(screen.queryByText(genericFailure)).toBeInTheDocument());
  });

  it('shows a link to the payment methods page', async () => {
    renderPrimaryPaymentMethodCard();

    const link = await screen.findByRole('link', {
      name: TranslationKeys.BillingPage.PaymentMethods.DefaultPaymentMethod
        .VIEW_PAYMENT_METHODS_LINK
    });
    expect(link.getAttribute('href')).toBe(ROUTES.VIEW_PAYMENT_METHODS);
  });

  it('should render Upgrade finance loan', async () => {
    const paymentMethod = [
      createPaymentMethod({
        cardType: 'LineOfCredit',
        locAmount: 5185.0,
        locApplicationId: 1111471,
        locBalance: 2000.97,
        locIsActive: true,
        locLender: 'Upgrade Indirect',
        locOriginationDate: new Date('08-20-2023'),
        locProviderName: 'Acorn Financial',
        locTerminationDate: new Date('08-10-2023')
      })
    ];

    renderPrimaryPaymentMethodCard(paymentMethod);

    expect(await screen.findByText(Tx.HEADER)).toBeInTheDocument();
    expect(
      await screen.findByText(TranslationKeys.PaymentMethodsPage.PaymentType.MOVE_LOAN_UPGRADE_TEXT)
    ).toBeInTheDocument();
    expect(await screen.findByText(TranslationKeys.PaymentMethodsPage.DEFAULT)).toBeInTheDocument();
  });

  it('should render Citi Bank finance loan', async () => {
    const paymentMethod = [
      createPaymentMethod({
        cardType: 'LineOfCredit',
        locAmount: 5185.0,
        locApplicationId: 1111471,
        locBalance: 2000.97,
        locIsActive: true,
        locLender: 'Citi Bank',
        locOriginationDate: new Date('08-20-2023'),
        locProviderName: 'Citi Bank',
        locTerminationDate: new Date('08-10-2023')
      })
    ];

    renderPrimaryPaymentMethodCard(paymentMethod);

    expect(await screen.findByText(Tx.HEADER)).toBeInTheDocument();
    expect(
      await screen.findByText(
        TranslationKeys.PaymentMethodsPage.PaymentType.MOVE_LOAN_CITI_BANK_TEXT
      )
    ).toBeInTheDocument();
    expect(await screen.findByText(TranslationKeys.PaymentMethodsPage.DEFAULT)).toBeInTheDocument();
  });
});
