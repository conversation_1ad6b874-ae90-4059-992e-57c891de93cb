import { renderWithPoetProvidersAndState } from '../../../../testUtils/RenderHelpers';
import React from 'react';
import { AccountBalanceCard } from '../AccountBalanceCard';
import { screen } from '@testing-library/react';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { ROUTES } from '../../../../Routes';
import { vi } from 'vitest';
import { mockRefreshSession } from '../../../../../setupTests';
import {
  createBillingInformation,
  createRefreshSessionClaims
} from '../../../../testUtils/MyPodsFactories';

const mockUseBillingInformation = vi.hoisted(() => vi.fn());
vi.mock('../../../../networkRequests/queries/useGetBillingInformation', () => {
  return {
    useGetBillingInformation: mockUseBillingInformation
  };
});

describe('Account Balance Card', () => {
  describe('when data is fetched', () => {
    const renderWithBillingContext = (totalBalance: number = 0) => {
      const billingInformation = createBillingInformation({ totalBalance });
      mockUseBillingInformation.mockReturnValue({
        billingInformation: billingInformation,
        isRefetching: false
      });
      renderWithPoetProvidersAndState(<AccountBalanceCard />);
    };

    beforeEach(() => {
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    });

    it('should render account balance message and payment link', async () => {
      renderWithBillingContext(100);

      expect(await screen.findByText('$100.00')).toBeInTheDocument();
      expect(
        await screen.findByText(TranslationKeys.BillingPage.TOTAL_ACCOUNT_BALANCE)
      ).toBeInTheDocument();
      expect(
        await screen.findByRole('link', { name: TranslationKeys.BillingPage.MAKE_PAYMENT_LINK })
      ).toHaveAttribute('href', ROUTES.MAKE_PAYMENT);
    });

    it('should not render payment link when account balance is 0', async () => {
      renderWithBillingContext(0);

      expect(
        await screen.findByText(TranslationKeys.BillingPage.TOTAL_ACCOUNT_BALANCE)
      ).toBeInTheDocument();
      expect(await screen.findByText('$0.00')).toBeInTheDocument();
      expect(
        screen.queryByRole('link', { name: TranslationKeys.BillingPage.MAKE_PAYMENT_LINK })
      ).not.toBeInTheDocument();
    });

    it('should render account credit when account balance is negative', async () => {
      renderWithBillingContext(-100);

      expect(
        await screen.findByText(TranslationKeys.BillingPage.TOTAL_ACCOUNT_CREDIT)
      ).toBeInTheDocument();
      expect(await screen.findByText('+$100.00')).toBeInTheDocument();
      expect(
        screen.queryByRole('link', { name: TranslationKeys.BillingPage.MAKE_PAYMENT_LINK })
      ).not.toBeInTheDocument();
    });

    afterEach(() => {
      mockUseBillingInformation.mockClear();
      mockUseBillingInformation.mockReset();
    });
  });

  describe('when is refetching', () => {
    const renderWithBillingContext = () => {
      const billingInformation = createBillingInformation({ totalBalance: 123 });
      mockUseBillingInformation.mockReturnValue({
        billingInformation: billingInformation,
        isRefetching: true
      });
      renderWithPoetProvidersAndState(<AccountBalanceCard />);
    };
    beforeEach(() => {
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    });

    it('should render Processing Payment message', async () => {
      renderWithBillingContext();
      expect(
        await screen.findByText(TranslationKeys.BillingPage.PROCESSING_PAYMENT)
      ).toBeInTheDocument();
    });
  });
});
