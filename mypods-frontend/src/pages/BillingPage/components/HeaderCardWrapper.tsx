import React, { ReactNode } from 'react';
import { Card } from '@mui/material';
import { Design } from '../../../helpers/Design';

export const HeaderCardWrapper = ({ children }: { children: ReactNode }) => (
  <Card {...cardStyles}>{children}</Card>
);

const cardStyles = {
  sx: {
    padding: Design.Primitives.Spacing.md,
    border: '1px solid var(--color-neutral-neutral300, #CBCBCB)',
    borderRadius: '8px',
    background: 'var(--color-defaults-neutral100-D, #FFF)',
    boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)',
    height: '100%' // Required for stretching in parent container
  }
};
