import React from 'react';
import { Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';

export type BillingFAQCardProps = {
  showCustomStatementWarning: boolean;
};

export const BillingFaqCard = ({ showCustomStatementWarning }: BillingFAQCardProps) => {
  const { t: translate } = useTranslation();
  return (
    <Grid {...styles.card}>
      <Typography {...styles.title}>
        {translate(
          showCustomStatementWarning
            ? TranslationKeys.BillingPage.BillingFaqCard.COMBINED_TITLE
            : TranslationKeys.BillingPage.BillingFaqCard.BILLING_TITLE
        )}
      </Typography>
      <div>
        <Typography {...styles.subHeader}>
          {translate(TranslationKeys.BillingPage.BillingFaqCard.LOOKING_FOR_MORE_DETAILS_TITLE)}
        </Typography>
        <Typography
          {...styles.details}
          dangerouslySetInnerHTML={{
            __html: translate(
              TranslationKeys.BillingPage.BillingFaqCard.LOOKING_FOR_MORE_DETAILS_DESC
            )
          }}></Typography>
      </div>
      {showCustomStatementWarning && (
        <div>
          <Typography {...styles.subHeader}>
            {translate(TranslationKeys.BillingPage.BillingFaqCard.STATEMENT_INFO_ALERT_TITLE)}
          </Typography>
          <Typography {...styles.details}>
            {translate(TranslationKeys.BillingPage.BillingFaqCard.STATEMENT_INFO_ALERT_DESC)}
          </Typography>
        </div>
      )}
    </Grid>
  );
};

const styles = {
  card: {
    sx: {
      marginTop: '2rem',
      padding: '20px',
      backgroundColor: Design.Alias.Color.neutral175,
      display: 'flex',
      gap: 2,
      flexDirection: 'column',
      borderRadius: 8
    }
  },
  title: {
    sx: {
      ...Design.Alias.Text.Heading.Desktop.Sm,
      color: Design.Alias.Color.accent900
    }
  },
  subHeader: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.SmSemi,
      color: Design.Alias.Color.accent900
    }
  },
  details: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Alias.Color.neutral700
    }
  }
};
