import { screen } from '@testing-library/react';

const views = {
  upcomingPayments: {
    section: () => screen.findByTestId('upcoming-payments-section')
  },
  paymentHistory: {
    section: () => screen.findByTestId('payment-history-section')
  },
  statements: {
    section: () => screen.queryByTestId('statements-section')
  },
  statementInfoAlert: {
    section: () => screen.queryByTestId('statement-info-alert')
  }
};
export const billingPageViews = views;
