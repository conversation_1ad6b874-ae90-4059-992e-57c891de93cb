import React from 'react';
import { vi } from 'vitest';
import { screen, within } from '@testing-library/react';
import {
  createBillingInformation,
  createBillingInvoice,
  createBillingQueryResult,
  createBillingRequest,
  createMonthlyStatement,
  createRefreshSessionClaims
} from '../../../testUtils/MyPodsFactories';
import { renderWithPoetProvidersAndState } from '../../../testUtils/RenderHelpers';
import { BillingPage } from '../BillingPage';
import { billingPageViews as views } from './BillingPageViews';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { CustomerType } from '../../../networkRequests/responseEntities/CustomerEntities';
import {
  mockGetBillingInformation,
  mockGetCustomerDocuments,
  mockGetCustomerOrders,
  mockGetPaymentMethods,
  mockRefreshSession
} from '../../../../setupTests';
import { useGetBillingInformation } from '../../../networkRequests/queries/useGetBillingInformation';

const mockUseGetBillingInformation = vi.hoisted(() => vi.fn<typeof useGetBillingInformation>());
vi.mock('../../../networkRequests/queries/v2/useGetBillingInformation', () => {
  return {
    useGetBillingInformation: mockUseGetBillingInformation
  };
});

vi.mock('../components/AcornFinancing', () => ({
  AcornFinancing: () => {
    return <p>Financing Card</p>;
  }
}));

describe('Billing Page', () => {
  const statements = [createMonthlyStatement()];
  const paidInvoice = createBillingInvoice();
  const unpaidInvoice = createBillingInvoice({
    isPaid: false
  });
  const invoices = [paidInvoice, unpaidInvoice];
  const billingInformation = createBillingInformation({
    invoices,
    monthlyStatements: statements,
    totalBalance: 9.99
  });

  function renderPage() {
    const billingResponse = createBillingRequest({ billingInformation });
    mockGetBillingInformation.mockResolvedValue({ billingInformation });
    mockUseGetBillingInformation.mockImplementation(() =>
      createBillingQueryResult({
        data: billingResponse,
        billingInformation: billingInformation,
        error: null
      })
    );
    mockGetPaymentMethods.mockResolvedValue([]);
    return renderWithPoetProvidersAndState(<BillingPage />);
  }

  beforeEach(() => {
    mockGetCustomerDocuments.mockResolvedValue([]);
    mockGetCustomerOrders.mockResolvedValue([]);
  });

  describe('when customerType is COMMERCIAL', () => {
    beforeEach(async () => {
      const commercialCustomer = createRefreshSessionClaims({
        type: CustomerType.COMMERCIAL
      });
      mockRefreshSession.mockResolvedValue(commercialCustomer);
    });

    it('displays account balance and make payment link', async () => {
      renderPage();

      expect(
        await screen.findByRole('heading', {
          name: `${TranslationKeys.BillingPage.TOTAL_ACCOUNT_BALANCE}`
        })
      ).toBeInTheDocument();
      expect(
        await screen.findByRole('link', {
          name: `${TranslationKeys.BillingPage.MAKE_PAYMENT_LINK}`
        })
      ).toBeInTheDocument();
    });

    it('loads sections of the billing page', async () => {
      renderPage();

      expect(await views.upcomingPayments.section()).toBeInTheDocument();
      expect(await views.paymentHistory.section()).toBeInTheDocument();
      expect(views.statements.section()).toBeInTheDocument();
    });

    it('renders the section headings with an accordion that is expanded by default', async () => {
      renderPage();

      const upcomingPaymentsHeader = await within(
        await views.upcomingPayments.section()
      ).findByRole('heading', {
        name: TranslationKeys.BillingPage.UpcomingPayments.HEADER
      });
      expect(upcomingPaymentsHeader).toBeInTheDocument();
      expect(await screen.findByTestId('upcoming-payments-content')).toBeVisible();
      expect(
        await within(await views.paymentHistory.section()).findByRole('heading', {
          name: TranslationKeys.BillingPage.PaymentHistory.HEADER
        })
      ).toBeInTheDocument();
      expect(await screen.findByTestId('payment-history-content')).toBeVisible();
      expect(
        await within(views.statements.section()!).findByRole('heading', {
          name: TranslationKeys.BillingPage.Statements.HEADER
        })
      ).toBeInTheDocument();
      expect(await screen.findByTestId('statements-content')).toBeVisible();
    });
  });

  describe('when customerType is RESIDENTIAL', () => {
    beforeEach(() => {
      const residentialCustomer = createRefreshSessionClaims({
        type: CustomerType.RESIDENTIAL
      });
      mockRefreshSession.mockResolvedValue(residentialCustomer);
    });

    it('loads all sections of the billing page except for the monthly statements', async () => {
      renderPage();

      expect(await views.upcomingPayments.section()).toBeInTheDocument();
      expect(await views.paymentHistory.section()).toBeInTheDocument();
      expect(views.statements.section()).not.toBeInTheDocument();
    });

    it('loads the Custom Statement link next to the Paid invoices header', async () => {
      renderPage();

      expect(
        within(await views.paymentHistory.section()).getByRole('link', {
          name: TranslationKeys.CustomStatementPage.LINK
        })
      ).toBeInTheDocument();
    });

    it('show Statement info alert when customer type is residential', async () => {
      renderPage();
      const statementAlterTitle = await screen.findByText(
        TranslationKeys.BillingPage.BillingFaqCard.STATEMENT_INFO_ALERT_TITLE
      );
      expect(statementAlterTitle).toBeInTheDocument();
    });
  });
});
