export const AcornFinancingHelper = (widgetKind: string) => {
  const USER_HAS_CLOSED_BANNER_KEY = 'userHasClosedAcornBanner';

  const setBannerClosed = (): void => {
    if (widgetKind === 'banner') {
      localStorage.setItem(USER_HAS_CLOSED_BANNER_KEY, 'true');
    }
  };
  const getBannerClosed = () =>
    widgetKind === 'banner' && localStorage.getItem(USER_HAS_CLOSED_BANNER_KEY) === 'true';

  return {
    setUserClosed: setBannerClosed,
    getBannerClosed
  };
};
