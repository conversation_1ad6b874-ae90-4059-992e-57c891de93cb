import { formatDate, isWithin24Hours } from '../../../helpers/dateHelpers';
import { MoveLeg, Order, OrderType } from '../../../domain/OrderEntities';
import { Customer } from '../../../networkRequests/responseEntities/CustomerEntities';
import { PaymentMethod } from '../../../networkRequests/responseEntities/PaymentEntities';

export type OrdersForFinancing = {
  price: number;
  quoteId: number;
  orderId: string;
  scheduledDate: string;
};

export const eligibleOrders = (orders: Order[]) => {
  const myOrders: OrdersForFinancing[] = [];

  const isValidDeliveryLeg = (moveLeg: MoveLeg) =>
    ['INITIAL_DELIVERY', 'SELF_INITIAL_DELIVERY'].includes(moveLeg.moveLegType) &&
    !isWithin24Hours(moveLeg.scheduledDate) &&
    moveLeg.originationAddress.country === 'US' &&
    moveLeg.destinationAddress.country === 'US';

  const isValidPickupLeg = (moveLeg: MoveLeg) =>
    ['FINAL_PICKUP', 'SELF_FINAL_PICKUP', 'CITY_SERVICE_FINAL_PICKUP'].includes(
      moveLeg.moveLegType
    ) &&
    moveLeg.originationAddress.country === 'US' &&
    moveLeg.destinationAddress.country === 'US';

  orders?.forEach((order) => {
    let isEligible = true;
    let scheduledDate: Date = new Date();
    order.containers.forEach((container) => {
      if (!isEligible) return;
      const moveLegs = container.moveLegs.filter(
        (moveLeg) => isValidDeliveryLeg(moveLeg) || isValidPickupLeg(moveLeg)
      );
      isEligible = moveLegs.length === 2;
      if (isEligible && moveLegs[0].scheduledDate) {
        scheduledDate = moveLegs[0].scheduledDate;
      }
    });
    if (isEligible)
      myOrders.push({
        orderId: order.orderId,
        quoteId: order.quoteId,
        scheduledDate: formatDate(scheduledDate, 'yyyy-MM-dd'),
        price: order.price
      });
  });

  return myOrders;
};

const findMostExpensiveOrder = (allEligibleOrders: OrdersForFinancing[]) =>
  allEligibleOrders.sort((a, b) => a.price - b.price)[allEligibleOrders.length - 1];

export const findMostExpensiveEligibleOrder = (orders: Order[]) =>
  findMostExpensiveOrder(eligibleOrders(orders));

export const financingEligible = (
  customer: Customer,
  acornFinancingEnabled: boolean,
  paymentMethods: PaymentMethod[],
  orders: Order[],
  mostExpensiveOrder: OrdersForFinancing = findMostExpensiveEligibleOrder(orders)
) =>
  acornFinancingEnabled &&
  customer.customerType === 'RESIDENTIAL' &&
  paymentMethods !== undefined &&
  !paymentMethods.some((method) => method.cardType === 'LineOfCredit') &&
  !!mostExpensiveOrder &&
  orders.some((order) => order.orderType === OrderType.IF);
