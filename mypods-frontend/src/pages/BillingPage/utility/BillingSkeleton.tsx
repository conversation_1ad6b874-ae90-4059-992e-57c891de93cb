import { AccordionSummary, Grid, Skeleton, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { PageLayout } from '../../../components/PageLayout';
import { theme } from '../../../PodsTheme';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { HeaderCardWrapper } from '../components/HeaderCardWrapper';
import { RightChevronIcon } from '../../../components/icons/RightChevronIcon';
import { Design } from '../../../helpers/Design';

export const BillingSkeleton = () => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = billingStyles(isMobile);
  const { t: translate } = useTranslation();

  return (
    <PageLayout columnsLg={6}>
      <Grid data-testid="billing-page-skeleton" {...styles.page}>
        <Typography variant="h1" {...styles.skeletonHeaderText}>
          {translate(TranslationKeys.BillingPage.HEADER)}
        </Typography>
        <Grid
          container
          spacing={2}
          alignItems="stretch"
          flexDirection={isMobile ? 'column' : 'row'}>
          <Grid item sm={7} flexDirection="column" display="flex">
            <HeaderCardWrapper>
              <Grid container {...styles.skeletonAccountBalance}>
                <HorizontalBarSkeleton width={isMobile ? 150 : 255} />
                <HorizontalBarSkeleton width={82} />
                <HorizontalBarSkeleton width={120} />
              </Grid>
            </HeaderCardWrapper>
          </Grid>

          <Grid item sm={5} flexDirection="column" display="flex">
            <HeaderCardWrapper>
              <Grid container {...styles.skeletonPaymentMethods}>
                <HorizontalBarSkeleton width={isMobile ? 150 : 255} />
                <HorizontalBarSkeleton width={82} />
                <HorizontalBarSkeleton width={120} />
              </Grid>
            </HeaderCardWrapper>
          </Grid>
        </Grid>
      </Grid>

      <Grid {...styles.skeletonAccordionSummary}>
        <AccordionSummary
          {...styles.skeletonAccordionSummary}
          expandIcon={<RightChevronIcon {...styles.rightChevronIcon} />}>
          <HorizontalBarSkeleton width={isMobile ? 150 : 255} />
        </AccordionSummary>
      </Grid>

      <Grid {...styles.skeletonAccordionSummary}>
        <AccordionSummary
          {...styles.skeletonAccordionSummary}
          expandIcon={<RightChevronIcon {...styles.rightChevronIcon} />}>
          <HorizontalBarSkeleton width={isMobile ? 150 : 255} />
        </AccordionSummary>
      </Grid>
    </PageLayout>
  );
};

const HorizontalBarSkeleton = ({ width }: { width: number }) => (
  <Skeleton variant="rectangular" height={18} sx={{ borderRadius: '4px', width: `${width}px` }} />
);

const billingStyles = (isMobile: boolean) => ({
  page: {
    sx: {
      gap: '32px',
      display: 'flex',
      flexDirection: 'column'
    }
  },
  skeletonHeaderText: {
    sx: {
      color: Design.Alias.Color.errorDark,
      paddingBottom: Design.Primitives.Spacing.xxxs
    }
  },
  skeletonAccountBalance: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      justifyContent: 'flex-start',
      paddingX: Design.Primitives.Spacing.sm,
      gap: isMobile ? Design.Primitives.Spacing.md : '32px'
    }
  },
  skeletonPaymentMethods: {
    sx: {
      paddingX: Design.Primitives.Spacing.sm,
      gap: isMobile ? Design.Primitives.Spacing.md : '20px'
    }
  },
  skeletonAccordionSummary: {
    sx: {
      minHeight: 'fit-content',
      flexDirection: 'row-reverse',
      '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
        transform: 'rotate(90deg)'
      },
      '& .MuiAccordionSummary-content': {
        margin: 0
      },
      gap: Design.Primitives.Spacing.xxs,
      paddingTop: '20px'
    }
  },
  rightChevronIcon: {
    sx: {
      width: '16px',
      height: '16px'
    },
    style: {
      color: Design.Alias.Color.accent900
    }
  }
});
