import { createContainer, createMoveLeg, createOrder } from '../../../../testUtils/MyPodsFactories';
import { addHours } from 'date-fns';
import { eligibleOrders } from '../FinancingEligibility';
import { formatDate } from '../../../../helpers/dateHelpers';

describe('eligibleOrders', () => {
  it('should do stuff', () => {
    const initialDate = addHours(Date.now(), 26);
    const endDate = addHours(Date.now(), 30);

    const order = createOrder({
      containers: [
        createContainer({
          moveLegs: [
            createMoveLeg({
              scheduledDate: initialDate
            }),
            createMoveLeg({
              moveLegType: 'FINAL_PICKUP',
              scheduledDate: endDate
            })
          ]
        })
      ]
    });

    const orders = [order];

    expect(eligibleOrders(orders)).toHaveLength(1);
    expect(eligibleOrders(orders)[0]).toEqual({
      orderId: order.orderId,
      quoteId: order.quoteId,
      scheduledDate: formatDate(initialDate, 'yyyy-MM-dd'),
      price: order.price
    });
  });
});
