import { AcornFinancingHelper } from '../AcornFinancingHelper';

describe('AcornFinancingHelper', () => {
  describe('getBannerClosed()', () => {
    describe('when widgetKind is banner', () => {
      const subject = AcornFinancingHelper('banner');

      it('should be false if the user has not closed the banner', () => {
        expect(subject.getBannerClosed()).toBe(false);
      });

      it('should be true if the user has closed the banner', () => {
        subject.setUserClosed();

        expect(subject.getBannerClosed()).toBe(true);
      });
    });

    describe('when widgetKind is not banner', () => {
      ['card', 'button'].forEach((method) => {
        const subject = AcornFinancingHelper(method);

        it(`should be false if the user has not closed the ${method}`, () => {
          expect(subject.getBannerClosed()).toBe(false);
        });

        it(`should be false if the user has closed the ${method}`, () => {
          subject.setUserClosed();

          expect(subject.getBannerClosed()).toBe(false);
        });
      });
    });
  });
});
