import {
  FormControl<PERSON>abel,
  Grid,
  MenuItem,
  Select,
  Switch,
  TextField,
  Typography
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import Cookies from 'universal-cookie';
import { Navigate } from 'react-router-dom';
import Button from '@mui/material/Button';
import { v4 } from 'uuid';
import { QueryClient } from '@tanstack/react-query';
import { datadogLogs } from '@datadog/browser-logs';
import { addDays } from '../../helpers/dateHelpers';
import { Environment, ENVIRONMENT } from '../../environment';
import { ROUTES } from '../../Routes';
import { SharedLibraryDemo } from './SharedLibraryDemo';
import { useMyPodsService } from '../../networkRequests/MyPodsService';
import { setBillingDocumentToken, setDocumentToken } from '../../helpers/storageHelpers';
import { sessionTimeoutMinutesKey } from '../../components/session/SessionHandler';

export const isNonProdEnv = () => ENVIRONMENT !== Environment.PROD;

export const isDebug = (location: any) => location.pathname === ROUTES.DEBUG && isNonProdEnv();

const getCurrentSessionTimeoutMinutes = () =>
  localStorage.getItem(sessionTimeoutMinutesKey) ?? 'Unset';

const SELECTED_SCENARIO_NAME = 'selected-scenario';

const getScenarioCookie = () => {
  const cookies = new Cookies();

  const result = cookies.get(SELECTED_SCENARIO_NAME);

  return result || 'Generic';
};

export const DebugPage = ({ client }: { client: QueryClient }) => {
  const styles = debugStyles();
  const [accessToken, setAccessToken] = useState<string>('accessTokenCookie');
  const [customerId, setCustomerId] = useState<string>(getScenarioCookie());
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>(customerId);
  const [converted, setConverted] = useState<boolean>(true);
  const [refreshToken, setRefreshToken] = useState<string>('refreshTokenCookie');
  const [sessionTimeoutMinutes, setSessionTimeoutMinutes] = useState<string>('1');
  const [currentSessionTimeoutMinutes, setCurrentSessionTimeoutMinutes] = useState<string>(
    getCurrentSessionTimeoutMinutes()
  );
  const [dataDogLogMessage, setDataDogLogMessage] = useState<string>('Default log message');

  const { createAccessToken } = useMyPodsService();
  const ACCESS_TOKEN_NAME = 'pods-user-access-token';
  const REFRESH_TOKEN_NAME = 'pods-user-refresh-token';

  useEffect(() => {
    setCustomerId(selectedCustomerId ?? '');
  }, [selectedCustomerId]);

  if (ENVIRONMENT === Environment.PROD) return <Navigate to={ROUTES.NOT_FOUND} />;

  const handleDataDogLogMessage = () => {
    console.log(`Tried to send this message to DataDog: ${dataDogLogMessage}`);
    datadogLogs.logger.log(dataDogLogMessage, { aaa: 'aaa', bbb: { ccc: 'ccc' } });
  };

  const generateAccessTokenForCuid = async () => {
    client.clear();
    const claims = {
      converted,
      customerId: customerId.trim(),
      ownedCuids: [customerId.trim()],
      trackingUuid: v4(),
      firstName: 'John',
      lastName: 'Doe',
      username: '<EMAIL>',
      type: 'RESIDENTIAL'
    };
    const generatedToken = await createAccessToken(claims);
    setLocalSessionCookies(generatedToken);
  };

  const clearAccessToken = () => {
    setLocalSessionCookies('', '', addDays(new Date(), -1));
  };

  const clearDocumentToken = () => {
    setDocumentToken(null);
    setBillingDocumentToken(null);
  };

  const handleException = () => {
    throw new Error('Exception test');
  };

  const scenariosList = [
    'Generic',
    'Residential',
    'Commercial',
    'OutstandingRentalAgreements',
    'AllBanners',
    'ServiceCountdown',
    'LoanFinanced',
    'StorageUpNext',
    'ContainerVisit',
    'ContainerPlacement',
    'MultipleStorageLegs',
    'PriceDiff',
    'StorageAtHome',
    'StorageAtWarehouse',
    'LocalMove',
    'MultipleOrders',
    'WarehouseToWarehouse',
    'LocalSelfDelivery',
    'IFSelfDelivery',
    'IFSelfPickup',
    'Interfranchise',
    'Honolulu',
    'HonoluluTampa',
    'CityServiceOrder',
    'InternationalOrder',
    'EligibleToFinanceOrder',
    'failMoveLeg',
    'NoBillingAddress'
  ];

  const setLocalSessionCookies = (
    accessTokenToSet = accessToken,
    refreshTokenToSet = refreshToken,
    expiration = addDays(new Date(), 90)
  ) => {
    const cookies = new Cookies();
    if (ENVIRONMENT === Environment.LOCAL) {
      cookies.set(ACCESS_TOKEN_NAME, accessTokenToSet, {
        path: '/',
        expires: expiration,
        secure: false
      });
      cookies.set(REFRESH_TOKEN_NAME, refreshTokenToSet, {
        path: '/',
        expires: expiration,
        secure: false
      });
    } else {
      cookies.set(ACCESS_TOKEN_NAME, accessTokenToSet, {
        path: '/',
        expires: expiration,
        secure: true,
        httpOnly: false,
        sameSite: 'strict',
        domain: 'pods.com'
      });
      cookies.set(REFRESH_TOKEN_NAME, refreshTokenToSet, {
        path: '/',
        expires: expiration,
        secure: true,
        httpOnly: false,
        sameSite: 'strict',
        domain: 'pods.com'
      });
    }
  };

  const handleScenarioSelection = (scenario: string) => {
    setSelectedCustomerId(scenario);

    const expiration = addDays(new Date(), 90);

    const cookies = new Cookies();

    cookies.set(SELECTED_SCENARIO_NAME, scenario, {
      path: '/',
      expires: expiration,
      secure: false
    });
  };

  return (
    <Grid container gap={2} sx={{ justifyContent: 'center', display: 'flex' }}>
      <Grid {...styles.outlinedGrid}>
        <Grid item container>
          <Typography>Create session cookies</Typography>
        </Grid>
        <Select
          value={selectedCustomerId}
          onChange={(e) => handleScenarioSelection(e.target.value)}>
          {scenariosList.map((item) => (
            <MenuItem value={item} key={item}>
              {item}
            </MenuItem>
          ))}
        </Select>
        <TextField
          onChange={(e) => setCustomerId(e.target.value)}
          value={customerId}
          label="Customer Id"
        />
        <FormControlLabel
          control={<Switch onChange={(e) => setConverted(e.target.checked)} checked={converted} />}
          label="Converted"
        />
        <Grid sx={{ display: 'flex', gap: '8px', justifyContent: 'right' }}>
          <Button
            variant="contained"
            color="secondary"
            onClick={() => {
              client.clear();
              clearAccessToken();
              clearDocumentToken();
            }}
            sx={{ width: 'fit-content' }}>
            Clear token
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={generateAccessTokenForCuid}
            sx={{ width: 'fit-content' }}>
            Generate token
          </Button>
        </Grid>
      </Grid>
      <Grid {...styles.outlinedGrid}>
        <Grid item container>
          <Typography>Set session cookies</Typography>
        </Grid>
        <Grid item container>
          <TextField
            onChange={(e) => setAccessToken(e.target.value)}
            value={accessToken}
            label="accessToken"
          />
        </Grid>
        <Grid item container>
          <TextField
            onChange={(e) => setRefreshToken(e.target.value)}
            value={refreshToken}
            label="refreshToken"
          />
        </Grid>
        <Grid item container>
          <Button variant="contained" color="secondary" onClick={() => setLocalSessionCookies()}>
            Click Here
          </Button>
        </Grid>
      </Grid>
      <Grid {...styles.outlinedGrid}>
        <Grid item container>
          <Typography>Set Session Timeout In Minutes</Typography>
        </Grid>
        <Grid item container>
          <TextField
            onChange={(e) => setSessionTimeoutMinutes(e.target.value)}
            value={sessionTimeoutMinutes}
            label="Change setting"
          />
        </Grid>
        <Grid item container>
          <TextField value={currentSessionTimeoutMinutes} label="Current Setting" />
        </Grid>
        <Grid item container>
          <Button
            variant="contained"
            color="secondary"
            onClick={() => {
              setCurrentSessionTimeoutMinutes('Unset');
              localStorage.removeItem(sessionTimeoutMinutesKey);
            }}>
            Clear
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              setCurrentSessionTimeoutMinutes(sessionTimeoutMinutes);
              localStorage.setItem(sessionTimeoutMinutesKey, sessionTimeoutMinutes);
            }}>
            Set
          </Button>
        </Grid>
      </Grid>
      <Grid {...styles.outlinedGrid}>
        <SharedLibraryDemo />
      </Grid>
      <Grid {...styles.outlinedGrid}>
        <Grid item container sx={{ justifyContent: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleException}
            sx={{ width: 'fit-content' }}>
            Throw Exception
          </Button>
        </Grid>
      </Grid>
      <Grid {...styles.outlinedGrid}>
        <Grid>Version: {import.meta.env.VITE_APP_GIT_SHA}</Grid>
      </Grid>
      <Grid {...styles.outlinedGrid}>
        <Grid item container>
          <Typography>Log a message to DataDog (Stage only)</Typography>
        </Grid>
        <TextField
          onChange={(e) => setDataDogLogMessage(e.target.value)}
          value={dataDogLogMessage}
          label="Log Message"
        />
        <Grid item container>
          <Button
            variant="contained"
            color="primary"
            onClick={handleDataDogLogMessage}
            sx={{ width: 'fit-content' }}>
            Log to DataDog
          </Button>
        </Grid>
      </Grid>
    </Grid>
  );
};

const debugStyles = () => ({
  outlinedGrid: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      border: '1px solid grey',
      borderRadius: '8px',
      padding: '10px',
      gap: '16px',
      marginBottom: 'auto'
    },
    xs: 4,
    item: true
  }
});
