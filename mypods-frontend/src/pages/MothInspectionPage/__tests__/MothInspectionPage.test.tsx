import { screen, waitFor, waitForElementToBeRemoved } from '@testing-library/react';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { vi } from 'vitest';
import { renderWithPoetProvidersAndState } from '../../../testUtils/RenderHelpers';
import { mockedUseFeatureFlags, mockNavigate, mockRefreshSession } from '../../../../setupTests';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import {
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../testUtils/MyPodsFactories';
import { ROUTES } from '../../../Routes';
import { MothInspectionPage } from '../MothInspectionPage';

describe('MothInspectionPage', () => {
  let user: UserEvent;

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    user = userEvent.setup();
  });

  const renderPage = async ({
    handleNextButtonClick,
    orderId
  }: { handleNextButtonClick?: () => void; orderId?: string } = {}) => {
    const result = renderWithPoetProvidersAndState(
      <MothInspectionPage
        handleNextButtonClick={handleNextButtonClick ?? vi.fn()}
        orderId={orderId ?? 'orderId'}
        viewAgreementStep={vi.fn()}
        startAgreement={vi.fn()}
      />
    );
    await waitForElementToBeRemoved(() => result.queryByText(/Loading/i));
    return result;
  };

  it('should navigate back when back button clicked', async () => {
    renderPage();

    await screen.findByTestId('back-link');
    await waitFor(() => user.click(screen.getByTestId('back-link')));

    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it('should navigate to paginated moth inspection form when isMothPagination feature flag enabled', async () => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isMothPaginationEnabled: () => true })
    );

    await renderPage();

    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.MOTH_FLY_INSPECTION_FORM);
  });

  it('render the current form when isMothPagination feature flag disabled', async () => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isMothPaginationEnabled: () => false })
    );
    await renderPage();

    const nonPaginated = screen.getByTestId('moth-form-non-paginated');

    expect(nonPaginated).toBeInTheDocument();
  });

  it('calls the click handler prop, when next is clicked', async () => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({ isMothPaginationEnabled: () => false })
    );

    const handleNextButtonClick = vi.fn();

    await renderPage({ handleNextButtonClick });
    const nextButton = screen.getByRole('button', {
      name: TranslationKeys.CommonComponents.NEXT_BUTTON
    });

    await waitFor(async () => user.click(nextButton));

    expect(handleNextButtonClick).toHaveBeenCalled();
  });
});
