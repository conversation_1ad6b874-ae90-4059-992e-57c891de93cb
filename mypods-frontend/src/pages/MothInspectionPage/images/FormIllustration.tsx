import React from 'react';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { SvgIcon } from '@mui/material';

// -- impls --
export const FormIllustration: React.FC<SvgIconProps> = (props: SvgIconProps) => (
  <SvgIcon {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 96 96" fill="none">
      <g clipPath="url(#clip0_3590_236761)">
        <rect width="128" height="128" fill="#EBF3F6" />
        <rect x="15" y="24" width="69" height="78" fill="#0058C0" />
        <path
          d="M81 103H15V31.7522C18.1108 31.7487 21.0932 30.5094 23.2929 28.3062C25.4925 26.103 26.7298 23.1158 26.7333 20H69.2667C69.2634 21.5435 69.5657 23.0723 70.1562 24.498C70.7467 25.9237 71.6136 27.2179 72.7067 28.3059C73.7929 29.4011 75.0851 30.2697 76.5087 30.8612C77.9322 31.4528 79.4588 31.7556 81 31.7522V103Z"
          fill="white"
        />
        <rect x="15.5" y="20.5" width="65" height="82" fill="white" stroke="#CBCBCB" />
        <path
          d="M48 68C57.9411 68 66 59.9411 66 50C66 40.0589 57.9411 32 48 32C38.0589 32 30 40.0589 30 50C30 59.9411 38.0589 68 48 68Z"
          fill="#DC3333"
        />
        <path
          d="M54.2326 58.425L48 52.1924L41.7674 58.425L39.6899 56.3474L45.9225 50.1149L39.6899 43.8823L41.7674 41.8048L48 48.0374L54.2326 41.8048L56.3101 43.8823L50.0775 50.1149L56.3101 56.3474L54.2326 58.425Z"
          fill="white"
        />
        <rect x="26" y="71" width="43" height="4" rx="2" fill="#CBCBCB" />
        <rect x="26" y="79" width="43" height="4" rx="2" fill="#CBCBCB" />
      </g>
      <defs>
        <clipPath id="clip0_3590_236761">
          <rect width="96" height="96" rx="48" fill="white" />
        </clipPath>
      </defs>
    </svg>
  </SvgIcon>
);
