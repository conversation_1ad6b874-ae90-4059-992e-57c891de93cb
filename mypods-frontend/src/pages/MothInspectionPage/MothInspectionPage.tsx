import React, { useEffect } from 'react';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import Button from '@mui/material/Button';
import { useNavigate } from 'react-router-dom';
import { PageLayout } from '../../components/PageLayout';
import { Design } from '../../helpers/Design';
import { theme } from '../../PodsTheme';
import { BackLink } from '../../components/buttons/BackLink';
import { ROUTES } from '../../Routes';
import { StretchableLoadingButton } from '../../components/buttons/StretchableLoadingButton';
import { MothInspectionCard } from './MothInspectionCard';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { FaqSection } from './components/FaqSection';
import { FixedBottomContainer } from '../../components/FixedBottomContainer';
import {
  GtmAgreementType,
  ViewAgreementStep,
  ViewAgreementSteps
} from '../../config/google/GoogleEntities';
import { MOTH_PAGINATION_ENABLED, useFeatureFlags } from '../../helpers/useFeatureFlags';

interface Props {
  handleNextButtonClick: () => void;
  orderId: string;
  viewAgreementStep: (step: ViewAgreementStep, orderId: string) => void;
  startAgreement: (agreementType: GtmAgreementType, orderId: string) => void;
}
const Tx = TranslationKeys.MothInspectionPage;

export const MothInspectionPage = ({
  handleNextButtonClick,
  orderId,
  viewAgreementStep,
  startAgreement
}: Props) => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = mothInspectionPageStyles(isMobile);
  const navigate = useNavigate();
  const { isMothPaginationEnabled } = useFeatureFlags([MOTH_PAGINATION_ENABLED]);

  useEffect(() => {
    if (isMothPaginationEnabled()) {
      startAgreement('moth_fly_inventory', orderId);
      navigate(ROUTES.MOTH_FLY_INSPECTION_FORM);
    } else if (orderId) {
      startAgreement('moth_fly_inventory', orderId);
      viewAgreementStep(ViewAgreementSteps.moth.faq, orderId);
    }
  }, []);

  return (
    <PageLayout columnsLg={9}>
      <Grid data-testid="moth-form-non-paginated" container {...styles.mothInspectionPage}>
        <Grid {...styles.titleSection}>
          <BackLink route={-1} />
          <Grid {...styles.titleContainer}>
            <Typography variant="h1" {...styles.title}>
              {translate(Tx.HEADER)}
            </Typography>
            <Typography {...styles.subtitle}>{translate(Tx.SUBTITLE)}</Typography>
          </Grid>
        </Grid>
        <Grid {...styles.explanationSection}>
          <MothInspectionCard cardType="fiveDays" />
          <MothInspectionCard cardType="hawaii" />
          <MothInspectionCard cardType="dontRiskIt" />
        </Grid>
        {!isMobile && (
          <Grid container item xs={12} md={7} {...styles.buttonSection}>
            <StretchableLoadingButton
              label={translate(TranslationKeys.CommonComponents.NEXT_BUTTON)}
              isMobile={isMobile}
              onClick={handleNextButtonClick}
            />
          </Grid>
        )}
        <Grid>
          <Typography variant="subtitle2" {...styles.footnoteText}>
            {translate(Tx.FOOTNOTE)}
          </Typography>
        </Grid>
        <FaqSection />
        {isMobile && (
          <FixedBottomContainer>
            <Button
              variant="contained"
              color="secondary"
              {...styles.mobileButtonSection}
              onClick={handleNextButtonClick}>
              {translate(TranslationKeys.CommonComponents.NEXT_BUTTON)}
            </Button>
          </FixedBottomContainer>
        )}
      </Grid>
    </PageLayout>
  );
};

// -- styles --
const mothInspectionPageStyles = (isMobile: boolean) => ({
  mothInspectionPage: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: isMobile ? Design.Primitives.Spacing.lgPlus : '4rem'
    }
  },
  titleSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      columnGap: Design.Primitives.Spacing.sm
    }
  },
  titleContainer: {
    sx: {
      paddingBlock: Design.Primitives.Spacing.sm,
      color: Design.Alias.Color.accent900
    }
  },
  title: {
    sx: {
      paddingBottom: Design.Primitives.Spacing.sm,
      fontSize: isMobile
        ? Design.Alias.Text.Heading.Mobile.Lg
        : Design.Alias.Text.Heading.Desktop.Xxl
    }
  },
  subtitle: {
    sx: {
      ...(isMobile ? Design.Alias.Text.BodyUniversal.Md : Design.Alias.Text.BodyUniversal.Lg),
      color: Design.Alias.Color.accent900
    }
  },
  explanationSection: {
    sx: {
      display: 'flex',
      flexDirection: isMobile ? 'column' : 'row',
      gap: isMobile ? Design.Primitives.Spacing.md : Design.Primitives.Spacing.lg
    }
  },
  footnoteText: {
    sx: {
      color: Design.Alias.Color.neutral700,
      fontSize: '10px'
    }
  },
  buttonSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      alignSelf: 'center'
    }
  },
  mobileButtonSection: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.MdBold,
      flex: '1',
      height: '100%'
    }
  }
});
