import { useTranslation } from 'react-i18next';
import { Grid, Link, Typography } from '@mui/material';
import React from 'react';
import { ArrowSquareOut } from '@phosphor-icons/react';
import { MothFaqContext } from '../../../locales/TranslationConstants';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { AccordionItem } from './AccordionItem';

const Tx = TranslationKeys.MothInspectionPage;

export const FaqSection = () => {
  const { t: translate } = useTranslation();
  const styles = faqStyles;

  return (
    <Grid container {...styles.faqSection}>
      <Grid item>
        <Typography variant="h1" {...styles.faqTitle}>
          {translate(Tx.Faq.HEADER)}
        </Typography>
        <Typography variant="subtitle1" {...styles.faqSubtitle}>
          {translate(Tx.Faq.SUBTITLE)}&nbsp;
          <Link
            href="https://www.pods.com/resource-center/spongy-moth-inspection"
            target="blank"
            underline="none"
            sx={{
              ...Design.Alias.Text.BodyUniversal.Sm,
              color: Design.Alias.Color.secondary500
            }}>
            {translate(Tx.Faq.LINK_TITLE)}
            <span style={{ position: 'relative', top: '4px', left: '2px' }}>
              <ArrowSquareOut size={20} />
            </span>
          </Link>
        </Typography>
      </Grid>
      {Object.values(MothFaqContext).map((contextValue) => (
        <AccordionItem key={contextValue} context={contextValue} />
      ))}
    </Grid>
  );
};

const faqStyles = {
  faqSection: {
    sx: {
      gap: Design.Primitives.Spacing.md
    }
  },
  faqTitle: {
    sx: {
      paddingBlock: Design.Primitives.Spacing.sm
    }
  },
  faqSubtitle: {
    sx: {
      color: Design.Primitives.Color.NeutralDark.charcoal
    }
  },
  linkedText: {
    sx: {
      fontWeight: 700,
      color: Design.Alias.Color.secondary500,
      textDecorationColor: Design.Alias.Color.secondary500,
      cursor: 'pointer'
    }
  }
};
