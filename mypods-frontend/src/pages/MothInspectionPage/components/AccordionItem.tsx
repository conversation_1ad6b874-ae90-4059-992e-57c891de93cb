import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { Grid, IconButton, Typography } from '@mui/material';
import Divider from '@mui/material/Divider';
import { MinusIcon } from '../../../components/icons/MinusIcon';
import { PlusIcon } from '../../../components/icons/PlusIcon';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';

const Tx = TranslationKeys.MothInspectionPage;

export const AccordionItem = ({ context }: { context: string }) => {
  const { t: translate } = useTranslation();
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const handleOnClick = () => setIsOpen(!isOpen);
  const styles = accordionStyles(isOpen);

  return (
    <Grid item {...styles.faqPrompt}>
      <Grid onClick={handleOnClick} item {...styles.faqPromptHeader}>
        <Typography {...styles.faqPromptTitle}>{translate(Tx.Faq.PROMPT, { context })}</Typography>
        <IconButton>{isOpen ? <MinusIcon /> : <PlusIcon />}</IconButton>
      </Grid>
      <Grid item>
        {isOpen && (
          <Typography {...styles.faqAnswer}>{translate(Tx.Faq.ANSWER, { context })}</Typography>
        )}
        <Divider />
      </Grid>
    </Grid>
  );
};

const accordionStyles = (isOpen: boolean) => ({
  faqPrompt: {
    sx: {
      width: '100%'
    }
  },
  faqPromptHeader: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Lg,
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingBottom: Design.Primitives.Spacing.xxs
    }
  },
  faqPromptTitle: {
    fontWeight: isOpen ? '700' : '400'
  },
  faqAnswer: {
    sx: {
      paddingBottom: Design.Primitives.Spacing.sm
    }
  }
});
