import React from 'react';
import { Grid, IconButton, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import CloseIcon from '@mui/icons-material/Close';
import { Design } from '../../helpers/Design';
import { theme } from '../../PodsTheme';
import { MothInspectionCard } from './MothInspectionCard';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { FaqSection } from './components/FaqSection';

export interface MothInspectionPageModalProps {
  handleOnClosed: () => void;
}

const Tx = TranslationKeys.MothInspectionPage;

export const MothInspectionLearnMore: React.FC<MothInspectionPageModalProps> = ({
  handleOnClosed
}) => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = mothInspectionLearnMoreStyles(isMobile);

  return (
    <>
      <Grid container {...styles.header}>
        <IconButton onClick={handleOnClosed} aria-label="close">
          <CloseIcon />
        </IconButton>
      </Grid>
      <Grid container gap={4} {...styles.learnMoreContainer}>
        <Grid {...styles.titleSection} style={{ marginTop: '0rem' }}>
          <Grid {...styles.titleContainer}>
            <Typography variant="h1" {...styles.title}>
              {translate(Tx.MULTI_PAGE_HEADER)}
            </Typography>
            <Typography {...styles.subtitle}>{translate(Tx.MULTI_PAGE_SUBTITLE)}</Typography>
          </Grid>
        </Grid>
        <Grid {...styles.explanationSection}>
          <MothInspectionCard cardType="fiveDays" />
          <MothInspectionCard cardType="hawaii" />
          <MothInspectionCard cardType="dontRiskIt" />
        </Grid>
        <FaqSection />
      </Grid>
    </>
  );
};

// -- styles --
const mothInspectionLearnMoreStyles = (isMobile: boolean) => ({
  learnMoreContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      alignSelf: 'center',
      width: '90%'
    }
  },
  titleSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      columnGap: Design.Primitives.Spacing.sm
    }
  },
  titleContainer: {
    sx: {
      paddingBlock: Design.Primitives.Spacing.sm,
      color: Design.Alias.Color.accent900
    }
  },
  title: {
    sx: {
      paddingBottom: Design.Primitives.Spacing.sm,
      fontSize: isMobile
        ? Design.Alias.Text.Heading.Mobile.Lg
        : Design.Alias.Text.Heading.Desktop.Xxl
    }
  },
  subtitle: {
    sx: {
      ...(isMobile ? Design.Alias.Text.BodyUniversal.Md : Design.Alias.Text.BodyUniversal.Lg),
      color: Design.Alias.Color.accent900,
      fontSize: Design.Primitives.Font.Size.BodyUniversal.md
    }
  },
  explanationSection: {
    sx: {
      display: 'flex',
      flexDirection: isMobile ? 'column' : 'row',
      gap: isMobile ? Design.Primitives.Spacing.md : Design.Primitives.Spacing.lg
    }
  },
  header: {
    sx: {
      justifyContent: 'end'
    }
  }
});
