import React from 'react';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Button } from 'pods-component-library';
import { css } from 'pods-component-library/styled-system/css';
import { useNavigate } from 'react-router-dom';
import { ENV_VARS } from '../../environment';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { Design } from '../../helpers/Design';
import { Txt } from '../../components/Txt';
import { theme } from '../../PodsTheme';
import { ROUTES } from '../../Routes';

// -- types --
interface Props {}

// -- constants --
const Tx = TranslationKeys.PodsReady.SuccessPage;
const orderCount = 1;
// -- impls --
// eslint-disable-next-line no-empty-pattern
export const PodsReadySuccessPage: React.FC<Props> = ({}: Props) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const buttonStyle = isMobile
    ? { width: '100%' }
    : { width: 'fit-content', paddingRight: '40px', paddingLeft: '40px' };

  const { t: translate } = useTranslation();
  const navigate = useNavigate();

  return (
    <Grid container {...styles.page}>
      <Grid item {...styles.container}>
        <Grid item {...styles.imageContainer}>
          <img
            width="100%"
            src={`${ENV_VARS.ASSETS_BASE_URL}/images/pods-ready/PR_Success.gif`}
            alt={translate(Tx.ALT_TEXT)}
          />
        </Grid>
        <Grid container {...styles.text}>
          <Typography {...styles.woohoo}>{translate(Tx.SUCCESS_MESSAGE)}</Typography>
          <Txt {...styles.title} i18nKey={Tx.TITLE} />
          <Typography {...styles.subtitle}>
            {translate(Tx.SUBTITLE, { count: orderCount })}
          </Typography>
        </Grid>
        <Grid container {...styles.buttonContainer}>
          <Button
            variant="filled"
            buttonSize="large"
            color="primary"
            css={css.raw({ ...buttonStyle })}
            onPress={() => {
              navigate(ROUTES.HOME, { replace: true });
            }}>
            {translate(Tx.BUTTON_TEXT)}
          </Button>
        </Grid>
      </Grid>
    </Grid>
  );
};

// -- styles --
const styles = {
  page: {
    sx: {
      height: '100%',
      justifyContent: 'center',
      alignItems: 'flex-start'
    }
  },
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: { xs: 'flex-end', md: 'flex-start' },
      alignItems: 'center'
    }
  },
  text: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
      marginTop: {
        sm: Design.Primitives.Spacing.md,
        md: Design.Primitives.Spacing.lgPlus
      },
      gap: {
        xs: Design.Primitives.Spacing.sm,
        md: Design.Primitives.Spacing.md
      },
      paddingX: {
        xs: Design.Primitives.Spacing.md
      }
    }
  },
  imageContainer: {
    sx: {
      width: {
        xs: '278px',
        md: '378px'
      },
      marginTop: {
        xs: 'clamp(20px, 15vh, 158px)',
        md: '100px'
      },
      objectFit: 'contain'
    }
  },
  woohoo: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Lg,
      color: Design.Alias.Color.neutral700,
      textAlign: 'inherit'
    }
  },
  title: {
    sx: {
      ...Design.Alias.Text.Heading.Mobile.Xxl,
      fontSize: {
        xs: Design.Alias.Text.Heading.Mobile.Xxl.fontSize,
        md: Design.Alias.Text.Heading.Desktop.Xxl.fontSize
      },
      color: Design.Alias.Color.accent900,
      strong: {
        color: Design.Alias.Color.primary500
      }
    }
  },
  subtitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      fontSize: {
        xs: Design.Alias.Text.BodyUniversal.Sm.fontSize,
        md: Design.Alias.Text.BodyUniversal.Md.fontSize
      },
      maxWidth: {
        xs: '90%',
        md: '348px'
      },
      color: Design.Alias.Color.neutral700
    }
  },
  buttonContainer: {
    sx: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: {
        xs: Design.Primitives.Spacing.xxl,
        md: Design.Primitives.Spacing.lgPlus
      },
      padding: {
        xs: '2rem',
        md: 0
      },
      position: {
        xs: 'absolute',
        md: 'relative'
      },
      bottom: {
        xs: '0px'
      }
    }
  }
};
