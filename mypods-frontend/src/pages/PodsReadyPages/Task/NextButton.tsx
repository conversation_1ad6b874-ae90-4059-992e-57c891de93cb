import React from 'react';
import { Grid } from '@mui/material';
import { CaretRight } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';

export interface NextButtonProps {
  isComplete: boolean;
  isMobile: boolean;
}

export const NextButton = (props: NextButtonProps) => {
  const { isComplete, isMobile } = props;
  const { t } = useTranslation();
  const style = isMobile && !isComplete ? { ...styles, ...styles.mobile } : { ...styles };
  return (
    <Grid item xs={12} md={2} sx={style}>
      {!isComplete &&
        (isMobile ? (
          <div>{t(TranslationKeys.TaskPage.REVIEW_AND_SIGN_BUTTON)}</div>
        ) : (
          <CaretRight data-testid="next-icon" size={28} color={Design.Alias.Color.accent900} />
        ))}
    </Grid>
  );
};

const styles = {
  marginTop: 'auto',
  marginBottom: 'auto',
  textAlign: 'end',
  mobile: {
    backgroundColor: Design.Alias.Color.primary500,
    width: '100%',
    textAlign: 'center',
    padding: '8px 16px',
    borderRadius: '4px',
    color: Design.Alias.Color.neutral100,
    ...Design.Alias.Text.BodyUniversal.SmBold,
    marginTop: '16px'
  }
};
