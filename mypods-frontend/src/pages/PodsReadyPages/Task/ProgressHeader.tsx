import React from 'react';
import { LinearProgress, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Target } from '@phosphor-icons/react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';
import { theme } from '../../../PodsTheme';

export interface ProgressHeaderProps {
  percentComplete: number;
}

export const ProgressHeader = (props: ProgressHeaderProps) => {
  const translationKeys = TranslationKeys.TaskPage;
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = getStyles(isMobile);

  return (
    <div style={styles.wrapper}>
      <div style={styles.header}>
        <Typography sx={styles.header.title}>
          <Target size={32} />
          {t(translationKeys.ProgressBar.TITLE)}
        </Typography>
        <span style={styles.header.percentage}>
          {t(TranslationKeys.CommonComponents.PERCENT, { amount: props.percentComplete })}
          <span style={styles.header.percentage.completed}>
            {t(translationKeys.ProgressBar.COMPLETED)}
          </span>
        </span>
      </div>
      <LinearProgress sx={styles.progressBar} variant="determinate" value={props.percentComplete} />
    </div>
  );
};

const getStyles = (isMobile: boolean) => ({
  wrapper: {
    width: '100%',
    marginTop: isMobile ? Design.Primitives.Spacing.sm : '32px',
    marginBottom: isMobile ? Design.Primitives.Spacing.xs : Design.Primitives.Spacing.md
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    title: {
      color: Design.Alias.Color.accent900,
      ...Design.Alias.Text.Heading.Desktop.Md,
      fontWeight: 800,
      svg: {
        position: 'relative',
        top: '8px',
        color: Design.Alias.Color.primary500
      }
    },
    percentage: {
      marginTop: 'auto',
      marginBottom: 'auto',
      ...Design.Alias.Text.BodyUniversal.LgBold,
      completed: {
        marginLeft: '6px',
        color: Design.Alias.Color.neutral700,
        ...Design.Alias.Text.BodyUniversal.MdBold
      }
    }
  },
  progressBar: {
    width: '100%',
    backgroundColor: Design.Alias.Color.neutral200,
    '& .MuiLinearProgress-bar': {
      backgroundColor: Design.Primitives.Color.Blue.oasis
    }
  }
});
