import React, { useEffect } from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { CalendarCheck, Bug, Signature } from '@phosphor-icons/react';
import { subDays } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import isEmpty from 'lodash/isEmpty';
import { PageLayout } from '../../../components/PageLayout';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ProgressHeader } from './ProgressHeader';
import { TaskCard } from './TaskCard';
import { formatToLocale } from '../../../helpers/dateHelpers';
import { theme } from '../../../PodsTheme';
import { OrderStatusCard } from './OrderStatusCard/OrderStatusCard';
import { WelcomeCard } from './WelcomeCard';
import { useGetPodsReadyCustomerOrders } from '../../../networkRequests/queries/podsReady/useGetPodsReadyCustomerOrders';
import { PodsReadyRoutes } from '../../../PodsReadyRoutes';
import { PODS_READY_PASSWORD_ONBOARDING, useFeatureFlags } from '../../../helpers/useFeatureFlags';
import { useStartPodsReadySession } from '../../../networkRequests/queries/podsReady/useStartPodsReadySession';
import { useSplitEvents } from '../../../config/useSplitEvents';
import { SplitEventType } from '../../../config/SplitEventTypes';
import { useGetOrderDocuments } from '../../../networkRequests/queries/v2/useGetOrderDocuments';
import { rentalAgreementDueDate } from '../../../domain/OrderEntities';
import { orderHasSignedMothAgreement } from '../../../helpers/storageHelpers';
import { hasMothForm } from '../../../networkRequests/responseEntities/AuthorizationEntities';

export const TaskPage = () => {
  const translationKeys = TranslationKeys.TaskPage;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t } = useTranslation();
  const { isPodsReadyPasswordOnboardingEnabled } = useFeatureFlags([
    PODS_READY_PASSWORD_ONBOARDING
  ]);
  const { customerOrders: orders } = useGetPodsReadyCustomerOrders();
  const {
    podsReadySessionClaims: { hasPassword, customerId }
  } = useStartPodsReadySession();
  const splitEvents = useSplitEvents(customerId);
  const { outstandingRentalAgreements, outstandingMothAgreements } = useGetOrderDocuments();
  const firstOrder = orders?.[0];
  const { orderDate, orderId } = firstOrder;
  const hadMothForm = hasMothForm(outstandingMothAgreements, orderId);
  const successRouteBasedOnPasswordOnboarding = () => {
    if (isPodsReadyPasswordOnboardingEnabled()) {
      return hasPassword ? PodsReadyRoutes.SUCCESS : PodsReadyRoutes.SET_PASSWORD;
    }
    return PodsReadyRoutes.SUCCESS;
  };
  const navigate = useNavigate();

  useEffect(() => {
    splitEvents.send(SplitEventType.PODS_READY_START);
  }, []);

  const showRentalAgreement = () => {
    const mothFormComplete = outstandingMothAgreements.length === 0;
    const podsReadyRoute = mothFormComplete
      ? successRouteBasedOnPasswordOnboarding()
      : PodsReadyRoutes.TASKS;

    navigate(PodsReadyRoutes.RENTAL_AGREEMENT, {
      state: { onSuccessRoute: podsReadyRoute }
    });
  };

  const handleMothFormCardClick = () => {
    const rentalAgreementComplete = outstandingRentalAgreements.length === 0;
    const podsReadyRoute = rentalAgreementComplete
      ? successRouteBasedOnPasswordOnboarding()
      : PodsReadyRoutes.TASKS;

    navigate(PodsReadyRoutes.MOTH_FLY_INSPECTION, {
      state: { onSuccessRoute: podsReadyRoute }
    });
  };

  const taskCards = [
    {
      Icon: Signature,
      title: t(translationKeys.RentalAgreement.TITLE),
      description: t(translationKeys.RentalAgreement.DESCRIPTION),
      dueDate: rentalAgreementDueDate(firstOrder) ?? new Date(),
      isComplete: outstandingRentalAgreements.length === 0,
      onClick: showRentalAgreement,
      testId: 'rental-agreement-task-card'
    },
    ...(hadMothForm
      ? [
          {
            Icon: Bug,
            title: t(translationKeys.InvasiveSpecies.TITLE),
            description: t(translationKeys.InvasiveSpecies.DESCRIPTION),
            dueDate: rentalAgreementDueDate(firstOrder) ?? new Date(),
            isComplete:
              isEmpty(outstandingMothAgreements) ||
              orderHasSignedMothAgreement(outstandingMothAgreements[0].orderId),
            onClick: handleMothFormCardClick,
            testId: 'moth-inspection-task-card'
          }
        ]
      : []),
    ...(orderDate
      ? [
          {
            Icon: CalendarCheck,
            title: t(translationKeys.Order.TITLE),
            description: t(translationKeys.Order.DESCRIPTION, {
              date: formatToLocale(orderDate)
            }),
            dueDate: subDays(orderDate, -1),
            isComplete: true
          }
        ]
      : [])
  ];

  const totalTasks = taskCards.length;
  const completedTasks = taskCards.filter((task) => task.isComplete).length;
  const percentComplete = totalTasks === 0 ? 0 : Math.round((completedTasks / totalTasks) * 100);

  const sortedTasks = taskCards.sort((a, b) => Number(a.isComplete) - Number(b.isComplete));

  return (
    <Grid container {...styles.page}>
      <PageLayout columnsMd={12} columnsLg={8}>
        <Grid container item lg={12} {...styles.headerPanel.mainPanel}>
          <WelcomeCard />
          <OrderStatusCard order={firstOrder} />
        </Grid>
        <Grid container item lg={12}>
          <ProgressHeader percentComplete={percentComplete} />
        </Grid>
        <Grid container item lg={12}>
          {sortedTasks.map((task, index) => (
            <TaskCard
              key={task.testId ?? index}
              Icon={task.Icon}
              title={task.title}
              description={task.description}
              dueDate={task.dueDate}
              isComplete={task.isComplete}
              isMobile={isMobile}
              onClick={task.onClick}
              testId={task.testId}
            />
          ))}
        </Grid>
      </PageLayout>
    </Grid>
  );
};

const styles = {
  page: {
    sx: {
      marginTop: Design.Primitives.Spacing.md,
      justifyContent: 'center',
      alignItems: 'flex-start'
    }
  },
  headerPanel: {
    mainPanel: {
      sx: {
        gap: Design.Primitives.Spacing.xs,
        flexWrap: 'nowrap',
        flexDirection: {
          xs: 'column',
          md: 'row'
        },
        marginTop: {
          xs: '12px',
          md: '48px'
        }
      }
    }
  }
};
