import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import React from 'react';
import { TaskPage } from '../TaskPage';
import { screen, waitFor, within } from '@testing-library/react';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import {
  mockedUseFeatureFlags,
  mockGetOrderDocuments,
  mockGetPodsReadyOrders,
  mockNavigate,
  mockOrderHasSignedMothAgreement,
  mockRefreshSession,
  mockStartPodsReadySession
} from '../../../../../setupTests';
import {
  createContainer<PERSON><PERSON>,
  createMoveLeg<PERSON>pi,
  createOrder<PERSON>pi,
  createOrderDocument,
  createPodsReadyClaims,
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../../testUtils/MyPodsFactories';
import {
  mothFormDocumentTypes,
  rentalAgreementDocumentTypes
} from '../../../../networkRequests/responseEntities/DocumentSharedSubtypes';
import userEvent from '@testing-library/user-event';
import { PodsReadyRoutes } from '../../../../PodsReadyRoutes';
import { SplitEventType } from '../../../../config/SplitEventTypes';

const mockSendSplitEvent = vi.hoisted(() => vi.fn());
vi.mock('../../../../config/useSplitEvents', async () => ({
  useSplitEvents: () => ({
    send: mockSendSplitEvent
  })
}));
describe('TaskPage', () => {
  const sessionClaims = createPodsReadyClaims();
  const moveLegs = [createMoveLegApi()];
  const containers = [createContainerApi({ moveLegs })];
  const rentalAgreement = createOrderDocument({
    docType: rentalAgreementDocumentTypes[1],
    docStatus: 'SENT'
  });
  const signedRentalAgreement = createOrderDocument({
    docType: rentalAgreementDocumentTypes[1],
    docStatus: 'COMPLETED'
  });
  const mothAgreement = createOrderDocument({
    docType: mothFormDocumentTypes[1],
    docStatus: 'SENT'
  });
  async function renderPage() {
    const result = renderWithPoetProvidersAndState(<TaskPage />, {});
    await runPendingPromises();
    return result;
  }

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    mockGetPodsReadyOrders.mockResolvedValue([createOrderApi({ containers })]);
    mockGetOrderDocuments.mockResolvedValue({
      documents: [rentalAgreement]
    });
    mockStartPodsReadySession.mockResolvedValue(sessionClaims);
  });

  it('displays welcome', async () => {
    await renderPage();

    expect(
      await screen.findByText(TranslationKeys.TaskPage.WelcomePanel.TITLE)
    ).toBeInTheDocument();
  });

  it('sends a PODS_READY_START event on load', async () => {
    await renderPage();

    expect(mockSendSplitEvent).toHaveBeenCalledWith(SplitEventType.PODS_READY_START);
  });

  describe('has a unsigned rental agreement', () => {
    it('displays rental agreement task card', async () => {
      await renderPage();

      expect(
        await screen.findByText(TranslationKeys.TaskPage.RentalAgreement.TITLE)
      ).toBeInTheDocument();
      const taskCard = await screen.findByTestId('rental-agreement-task-card');
      expect(await within(taskCard).findByText('taskPage.dueDate[May 23]')).toBeInTheDocument();
      expect(within(taskCard).queryByText(TranslationKeys.TaskPage.DONE)).not.toBeInTheDocument();
    });

    describe('when clicked, it navigates to the rental agreement with a previous component and onSuccess route', () => {
      it('passes the success page route, when passwordOnboarding is disabled', async () => {
        await renderPage();

        const taskCard = await screen.findByTestId('rental-agreement-task-card');
        await waitFor(async () => {
          await userEvent.click(taskCard);
        });

        expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.RENTAL_AGREEMENT, {
          state: {
            onSuccessRoute: PodsReadyRoutes.SUCCESS
          }
        });
      });

      it('passes the success page route, when passwordOnboarding is enabled & the user has a password', async () => {
        mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
          createUseFeatureFlagResult({ isPodsReadyPasswordOnboardingEnabled: () => true })
        );

        mockStartPodsReadySession.mockResolvedValue(createPodsReadyClaims({ hasPassword: true }));

        await renderPage();

        const taskCard = await screen.findByTestId('rental-agreement-task-card');
        await waitFor(async () => {
          await userEvent.click(taskCard);
        });

        expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.RENTAL_AGREEMENT, {
          state: {
            onSuccessRoute: PodsReadyRoutes.SUCCESS
          }
        });
      });

      it('passes the set password page route, when passwordOnboarding is enabled  & the user needs a password', async () => {
        mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
          createUseFeatureFlagResult({ isPodsReadyPasswordOnboardingEnabled: () => true })
        );

        await renderPage();

        const taskCard = await screen.findByTestId('rental-agreement-task-card');
        await waitFor(async () => {
          await userEvent.click(taskCard);
        });

        expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.RENTAL_AGREEMENT, {
          state: {
            onSuccessRoute: PodsReadyRoutes.SET_PASSWORD
          }
        });
      });
    });
  });

  describe('has a signed rental agreement', () => {
    beforeEach(() => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [signedRentalAgreement]
      });
    });

    it('displays rental agreement task card with done', async () => {
      await renderPage();

      const taskCard = await screen.findByTestId('rental-agreement-task-card');
      expect(await within(taskCard).findByText(TranslationKeys.TaskPage.DONE)).toBeInTheDocument();
      expect(within(taskCard).queryByText('Due May 23')).not.toBeInTheDocument();
    });

    it('clicking rental agreement task does nothing', async () => {
      await renderPage();

      const taskCard = await screen.findByTestId('rental-agreement-task-card');
      await waitFor(async () => {
        await userEvent.click(taskCard);
      });

      expect(mockNavigate).not.toHaveBeenCalledWith(PodsReadyRoutes.RENTAL_AGREEMENT);
    });
  });

  describe('does not have a moth Inspection Form', async () => {
    it('does not display moth Inspection task card', async () => {
      mockOrderHasSignedMothAgreement.mockReturnValue(false);
      await renderPage();

      expect(
        screen.queryByText(TranslationKeys.TaskPage.InvasiveSpecies.TITLE)
      ).not.toBeInTheDocument();
    });
  });
  describe('has an unsigned moth Inspection Form', () => {
    beforeEach(() => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [mothAgreement]
      });
    });

    describe('but that form was previously signed', () => {
      beforeEach(() => {
        mockOrderHasSignedMothAgreement.mockReturnValue(true);
      });

      it('shows the moth form as done', async () => {
        await renderPage();

        expect(
          await within(await screen.findByTestId('moth-inspection-task-card')).findByText(
            TranslationKeys.TaskPage.DONE
          )
        ).toBeInTheDocument();
      });
    });
    describe("and that form wasn't previously signed", () => {
      beforeEach(() => {
        mockOrderHasSignedMothAgreement.mockReturnValue(false);
      });

      it('displays moth Inspection task card, with upcoming due date', async () => {
        await renderPage();

        expect(
          await screen.findByText(TranslationKeys.TaskPage.InvasiveSpecies.TITLE)
        ).toBeInTheDocument();
        expect(
          await screen.findByText(TranslationKeys.TaskPage.InvasiveSpecies.DESCRIPTION)
        ).toBeInTheDocument();

        expect(
          await within(await screen.findByTestId('moth-inspection-task-card')).findByText(
            'taskPage.dueDate[May 23]'
          )
        ).toBeInTheDocument();
      });
      describe('when clicked, it navigates to the moth form with a previous component and onSuccess route', () => {
        const bothTasksIncomplete = {
          documents: [rentalAgreement, mothAgreement]
        };

        it('passes the tasks route, when there are remaining tasks', async () => {
          mockGetOrderDocuments.mockResolvedValue(bothTasksIncomplete);
          await renderPage();
          const mothFormCard = screen.getByTestId('moth-inspection-task-card');

          await waitFor(async () => {
            await userEvent.click(mothFormCard);
          });

          expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.MOTH_FLY_INSPECTION, {
            state: {
              onSuccessRoute: PodsReadyRoutes.TASKS
            }
          });
        });
        describe('when all tasks are complete', () => {
          const rentalAgreementComplete = {
            documents: [signedRentalAgreement, mothAgreement]
          };

          beforeEach(() => {
            mockGetOrderDocuments.mockResolvedValue(rentalAgreementComplete);
          });

          it('passes the success page route, when password onboarding is disabled', async () => {
            await renderPage();
            const mothFormCard = await screen.findByTestId('moth-inspection-task-card');

            await waitFor(async () => {
              await userEvent.click(mothFormCard);
            });

            expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.MOTH_FLY_INSPECTION, {
              state: {
                onSuccessRoute: PodsReadyRoutes.SUCCESS
              }
            });
          });

          it('passes the success page route, when password onboarding is enabled & the user needs a password', async () => {
            mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
              createUseFeatureFlagResult({ isPodsReadyPasswordOnboardingEnabled: () => true })
            );
            await renderPage();
            const mothFormCard = screen.getByTestId('moth-inspection-task-card');

            await waitFor(async () => {
              await userEvent.click(mothFormCard);
            });

            expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.MOTH_FLY_INSPECTION, {
              state: {
                onSuccessRoute: PodsReadyRoutes.SET_PASSWORD
              }
            });
          });

          it('passes the success page route, when password onboarding is enabled & the user has a password', async () => {
            mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
              createUseFeatureFlagResult({ isPodsReadyPasswordOnboardingEnabled: () => true })
            );
            mockStartPodsReadySession.mockResolvedValue(
              createPodsReadyClaims({ hasPassword: true })
            );
            await renderPage();
            const mothFormCard = await screen.findByTestId('moth-inspection-task-card');

            await waitFor(async () => {
              await userEvent.click(mothFormCard);
            });

            expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.MOTH_FLY_INSPECTION, {
              state: {
                onSuccessRoute: PodsReadyRoutes.SUCCESS
              }
            });
          });
        });
      });
    });
  });
  describe('has a signed moth Inspection Form', () => {
    beforeEach(() => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [signedRentalAgreement, mothAgreement]
      });
      mockOrderHasSignedMothAgreement.mockReturnValue(true);
    });

    it('it displays the moth inspection task card, with done status ', async () => {
      await renderPage();

      expect(
        await screen.findByText(TranslationKeys.TaskPage.InvasiveSpecies.TITLE)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(TranslationKeys.TaskPage.InvasiveSpecies.DESCRIPTION)
      ).toBeInTheDocument();

      expect(
        await within(await screen.findByTestId('moth-inspection-task-card')).findByText(
          TranslationKeys.TaskPage.DONE
        )
      ).toBeInTheDocument();
    });

    it('does not navigate away from the tasks page when clicked', async () => {
      await renderPage();
      const mothFormCard = screen.getByTestId('moth-inspection-task-card');
      await waitFor(async () => {
        await userEvent.click(mothFormCard);
      });

      expect(mockNavigate).not.toHaveBeenCalledWith(PodsReadyRoutes.MOTH_FLY_INSPECTION);
    });
  });

  describe('task completion percentage calculation', () => {
    it('shows 100% completion when all tasks are complete', async () => {
      const allTasksComplete = {
        documents: [signedRentalAgreement]
      };
      mockGetOrderDocuments.mockResolvedValue(allTasksComplete);
      mockOrderHasSignedMothAgreement.mockReturnValue(true);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '100');

      expect(await screen.findByText(/100/)).toBeInTheDocument();
    });

    it('shows 67% completion when 2 out of 3 tasks are complete (rental agreement incomplete)', async () => {
      const partialComplete = {
        documents: [rentalAgreement]
      };
      mockGetOrderDocuments.mockResolvedValue(partialComplete);
      mockOrderHasSignedMothAgreement.mockReturnValue(true);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '67');

      expect(await screen.findByText(/67/)).toBeInTheDocument();
    });

    it('shows 67% completion when 2 out of 3 tasks are complete (moth form incomplete)', async () => {
      const partialComplete = {
        documents: [mothAgreement]
      };
      mockGetOrderDocuments.mockResolvedValue(partialComplete);
      mockOrderHasSignedMothAgreement.mockReturnValue(false);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '67');

      expect(await screen.findByText(/67/)).toBeInTheDocument();
    });

    it('shows 33% completion when only 1 out of 3 tasks are complete', async () => {
      const minimalComplete = {
        documents: [rentalAgreement, mothAgreement]
      };
      mockGetOrderDocuments.mockResolvedValue(minimalComplete);
      mockOrderHasSignedMothAgreement.mockReturnValue(false);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '33');

      expect(await screen.findByText(/33/)).toBeInTheDocument();
    });

    it('shows 100% completion when moth form is previously signed but outstanding', async () => {
      const mothPreviouslySigned = {
        documents: [mothAgreement]
      };
      mockGetOrderDocuments.mockResolvedValue(mothPreviouslySigned);
      mockOrderHasSignedMothAgreement.mockReturnValue(true);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '100');

      expect(await screen.findByText(/100/)).toBeInTheDocument();
    });

    it('verifies progress bar receives correct percentage value', async () => {
      const partialComplete = {
        documents: [rentalAgreement]
      };
      mockGetOrderDocuments.mockResolvedValue(partialComplete);
      mockOrderHasSignedMothAgreement.mockReturnValue(true);

      await renderPage();

      const progressBar = await screen.findByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '67');
    });
  });
});
