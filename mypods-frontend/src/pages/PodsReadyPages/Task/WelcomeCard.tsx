import React from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { ENV_VARS } from '../../../environment';
import { theme } from '../../../PodsTheme';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Txt } from '../../../components/Txt';
import { Design } from '../../../helpers/Design';

const translationKeys = TranslationKeys.TaskPage;

// -- impls --
export const WelcomeCard: React.FC = () => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = welcomeStyles(isMobile);

  return (
    <Grid item {...styles.welcomePanel}>
      <Typography {...styles.welcomePanel.header}>
        {t(translationKeys.WelcomePanel.TITLE)}
      </Typography>
      <Txt {...styles.welcomePanel.body} i18nKey={translationKeys.WelcomePanel.BODY} />
    </Grid>
  );
};

// -- styles --
const welcomeStyles = (isMobile: boolean) => ({
  welcomePanel: {
    sx: {
      padding: '32px',
      color: Design.Alias.Color.accent900,
      borderRadius: Design.Primitives.Spacing.md,
      border: `1px solid ${Design.Alias.Color.neutral200}`,
      overflow: 'hidden',
      backgroundImage: `url(${ENV_VARS.ASSETS_BASE_URL}/PB_WelcomeCard.svg)`,
      backgroundPosition: 'left 50px top',
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'auto',
      height: {
        sm: '170px',
        md: '220px'
      },
      strong: {
        color: Design.Alias.Color.primary500,
        fontWeight: 900
      }
    },
    header: {
      sx: {
        color: Design.Alias.Color.neutral700,
        marginBottom: '16px',
        ...(isMobile ? Design.Alias.Text.BodyUniversal.Md : Design.Alias.Text.BodyUniversal.Lg)
      }
    },
    body: {
      sx: {
        ...(isMobile ? Design.Alias.Text.Heading.Mobile.Xl : Design.Alias.Text.Heading.Desktop.Xl),
        fontWeight: 900,
        color: Design.Alias.Color.accent900
      }
    }
  }
});
