import React from 'react';
import { Design } from '../../../helpers/Design';

export interface TitleProps {
  title: string;
  isComplete: boolean;
}

export const Title = (props: TitleProps) => {
  const { title, isComplete } = props;
  const color = isComplete ? Design.Alias.Color.neutral700 : Design.Alias.Color.accent900;
  return <div style={{ ...styles, color }}>{title}</div>;
};

const styles = {
  ...Design.Alias.Text.BodyUniversal.LgBold,
  marginBottom: '8px'
};
