import React from 'react';

export interface IconInCircleProps {
  Icon: React.ElementType;
  color: string;
}

export const IconInCircle = (props: IconInCircleProps) => {
  const { Icon, color } = props;
  const backgroundColor = `${color}15`;

  return (
    <div data-testid="icon-in-circle" style={{ ...styles, color, backgroundColor }}>
      <Icon size={40} />
    </div>
  );
};

const styles = {
  marginTop: 'auto',
  marginBottom: 'auto',
  marginRight: '32px',
  padding: '8px',
  borderRadius: '60px'
};
