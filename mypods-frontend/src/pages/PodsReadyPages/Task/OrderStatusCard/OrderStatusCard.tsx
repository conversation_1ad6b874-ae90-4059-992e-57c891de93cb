import React from 'react';
import { Grid, Chip } from '@mui/material';
import { Design } from '../../../../helpers/Design';
import { Order, orderToOrderStatus } from '../../../../domain/OrderEntities';
import { OrderDetails } from './OrderDetails';
import { StorageStatus } from './StorageStatus';
import { MovingStatus } from './MovingStatus';

// -- types --
interface Props {
  order: Order;
}

// -- impls --
export const OrderStatusCard: React.FC<Props> = ({ order }: Props) => {
  const summary = orderToOrderStatus(order);

  return (
    <Grid container item {...styles.statusPanel}>
      <Grid item>
        <Chip label={summary.service} variant="outlined" size="small" {...styles.chip} />
      </Grid>
      {summary.service === 'STORAGE' ? (
        <StorageStatus
          origin={summary.origin}
          streetAddress={order.containers[0].moveLegs[0].originationAddress.address1}
          scheduledDate={summary.scheduledDate}
        />
      ) : (
        <MovingStatus
          origin={summary.origin}
          destination={summary.destination}
          startingDate={summary.scheduledDate}
        />
      )}
      <OrderDetails orderId={order.orderId} containers={summary.containers} />
    </Grid>
  );
};

// -- styles --
const styles = {
  statusPanel: {
    sx: {
      color: Design.Alias.Color.neutral100,
      backgroundColor: Design.Primitives.Color.Blue.oasis,
      borderRadius: Design.Primitives.Spacing.md,
      width: '100%',
      flexDirection: 'column',
      flexWrap: 'nowrap',
      gap: Design.Primitives.Spacing.md,
      paddingX: Design.Primitives.Spacing.sm,
      paddingTop: Design.Primitives.Spacing.md,
      paddingBottom: Design.Primitives.Spacing.sm
    }
  },
  chip: {
    sx: {
      border: `1px solid ${Design.Alias.Color.neutral100}`,
      '.MuiChip-label': {
        ...Design.Alias.Text.BodyUniversal.Xxs,
        color: Design.Alias.Color.neutral100,
        lineHeight: 1,
        paddingX: Design.Primitives.Spacing.xxs,
        paddingBottom: '2px',
        paddingTop: '2px'
      }
    }
  }
};
