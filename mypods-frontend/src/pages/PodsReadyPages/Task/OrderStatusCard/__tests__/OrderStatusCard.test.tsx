import { render, screen, waitFor } from '@testing-library/react';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { Order } from '../../../../../domain/OrderEntities';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { OrderStatusCard } from '../OrderStatusCard';
import {
  createOrder,
  createContainer,
  createMoveLeg,
  createMoveLegAddress
} from '../../../../../testUtils/MyPodsFactories';
import { parseISO } from 'date-fns';

const Tx = TranslationKeys.TaskPage.OrderStatusCard;

describe('OrderStatusCard', () => {
  let user: UserEvent;

  const renderTaskCard = (order: Order) => {
    return render(<OrderStatusCard order={order} />);
  };

  beforeEach(() => {
    user = userEvent.setup();
  });

  describe('when the order is for storage service', () => {
    const firstMoveLeg = createMoveLeg({
      scheduledDate: parseISO('2025-12-31T09:53:00.3731149'),
      destinationAddress: createMoveLegAddress({ city: 'Destination', state: 'MO' })
    });
    const lastMoveLeg = createMoveLeg({
      originationAddress: createMoveLegAddress({ city: 'Origin', state: 'IL' })
    });
    const container = createContainer({ moveLegs: [firstMoveLeg, lastMoveLeg] });
    const order = createOrder({ containers: [container] });

    beforeEach(() => {
      renderTaskCard(order);
    });

    it('renders the correct chip and status section', async () => {
      //chip
      expect(await screen.findByText('STORAGE')).toBeInTheDocument();
      // status section
      expect(await screen.findByText(firstMoveLeg.destinationAddress.city)).toBeInTheDocument();
      expect(screen.queryByText(lastMoveLeg.originationAddress.city)).not.toBeInTheDocument();

      expect(
        await screen.findByText(firstMoveLeg.destinationAddress.address1!)
      ).toBeInTheDocument();
      expect(await screen.findByText('Dec 31')).toBeInTheDocument();

      // container summary
      expect(await screen.findByText(Tx.CONTAINERS)).toBeInTheDocument();
      expect(await screen.findByText(/16*ft/)).toBeInTheDocument();
      expect(await screen.findByText(Tx.ORDER_NO)).toBeInTheDocument();
      expect(await screen.findByText(/987654/)).toBeInTheDocument();
    });
  });

  describe('when the order is for moving service', () => {
    const firstMoveLeg = createMoveLeg({
      scheduledDate: parseISO('2025-12-31T09:53:00.3731149'),
      destinationAddress: createMoveLegAddress({
        city: 'Destination',
        state: 'MO',
        postalCode: '54321'
      })
    });
    const lastMoveLeg = createMoveLeg({
      originationAddress: createMoveLegAddress({
        city: 'Origin',
        state: 'IL',
        postalCode: '12345'
      })
    });

    describe('for single or multiple containers', () => {
      const container = createContainer({ moveLegs: [firstMoveLeg, lastMoveLeg] });
      const order = createOrder({ containers: [container] });

      beforeEach(() => {
        renderTaskCard(order);
      });

      it('renders the correct chip and status section', async () => {
        //chip
        expect(await screen.findByText('MOVING')).toBeInTheDocument();
        // status section
        expect(await screen.findByText(firstMoveLeg.destinationAddress.city)).toBeInTheDocument();
        expect(await screen.findByText(lastMoveLeg.originationAddress.city)).toBeInTheDocument();

        expect(await screen.findByText('Dec 31')).toBeInTheDocument();

        // container summary
        expect(await screen.findByText(Tx.CONTAINERS)).toBeInTheDocument();
        expect(await screen.findByText(/16*ft/)).toBeInTheDocument();
        expect(await screen.findByText(Tx.ORDER_NO)).toBeInTheDocument();
        expect(await screen.findByText(/987654/)).toBeInTheDocument();
      });

      it('renders the ProgressBar', async () => {
        expect(await screen.findByTestId('order-status-progress-bar')).toBeInTheDocument();
      });
    });

    describe('with multiple containers', () => {
      const firstMoveLeg = createMoveLeg({
        originationAddress: createMoveLegAddress({
          postalCode: '12345'
        })
      });
      const lastMoveLeg = createMoveLeg({
        destinationAddress: createMoveLegAddress({
          postalCode: '54321'
        })
      });
      const container = createContainer({
        moveLegs: [firstMoveLeg, lastMoveLeg],
        containerSize: '16'
      });
      const smallContainer = createContainer({
        moveLegs: [firstMoveLeg, lastMoveLeg],
        containerSize: '8'
      });

      it('increases the number of containers, when there are multiples of a given size', async () => {
        const doubleSixteenOrder = createOrder({ containers: [container, container] });
        renderTaskCard(doubleSixteenOrder);

        expect(await screen.findByText('2')).toBeInTheDocument();
        expect(await screen.findByText(/16*ft/)).toBeInTheDocument();
      });

      it('creates another section, when there are different container sizes', async () => {
        const doubleSixteenOrder = createOrder({ containers: [smallContainer, container] });
        renderTaskCard(doubleSixteenOrder);

        expect((await screen.findAllByText('1')).length).toEqual(2);
        expect(await screen.findByText(/\b8(?:ft)?\b/m)).toBeInTheDocument();
        expect(await screen.findByText(/\b16(?:ft)?\b/m)).toBeInTheDocument();
      });
    });
  });
});
