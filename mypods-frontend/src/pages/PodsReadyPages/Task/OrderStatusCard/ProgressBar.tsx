import { Grid, Typography } from '@mui/material';
import { Truck } from '@phosphor-icons/react';
import React from 'react';
import { Design } from '../../../../helpers/Design';

interface Props {
  startingDate?: string;
}

export const ProgressBar: React.FC<Props> = ({ startingDate }: Props) => (
  <Grid container direction="column" data-testid="order-status-progress-bar">
    <div style={styles.progressBar}>
      <div style={styles.completed}></div>
      <div style={styles.upcomingSection}>
        <div style={styles.truck}>
          <Truck aria-hidden size={24} weight="fill" />
        </div>
        <div style={styles.upcoming}></div>
      </div>
    </div>
    {startingDate && <Typography {...styles.date}>{startingDate}</Typography>}
  </Grid>
);

// -- styles --
const styles = {
  progressBar: {
    width: '100%',
    display: 'flex'
  },
  completed: {
    backgroundColor: Design.Alias.Color.accent900,
    borderTopLeftRadius: Design.Primitives.Spacing.xxs,
    borderBottomLeftRadius: Design.Primitives.Spacing.xxs,
    height: Design.Primitives.Spacing.xxxs,
    marginRight: Design.Primitives.Spacing.xxxs,
    transition: 'width 300ms ease-in',
    width: '3%' // this can be passed in, once we have a percentage?
  },
  upcomingSection: {
    display: 'flex',
    flex: 1,
    position: 'relative',
    transition: 'width 300ms ease-in'
  } as React.CSSProperties,
  truck: {
    position: 'absolute',
    left: -3,
    top: -12,
    zIndex: 1
  } as React.CSSProperties,
  upcoming: {
    backgroundColor: Design.Alias.Color.accent900,
    borderRadius: Design.Primitives.Spacing.xxs,
    height: Design.Primitives.Spacing.xxxs,
    opacity: '30%',
    width: '100%'
  },
  date: {
    ...Design.Alias.Text.BodyUniversal.Xxs,
    color: Design.Alias.Color.neutral100,
    marginTop: Design.Primitives.Spacing.sm
  }
};
