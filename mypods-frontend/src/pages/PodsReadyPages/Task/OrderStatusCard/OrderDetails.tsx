import { Grid, Typography } from '@mui/material';
import React from 'react';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { ContainerSummary } from '../../../../networkRequests/responseEntities/PodsReadyEntities';
import { Txt } from '../../../../components/Txt';

interface Props {
  orderId: string;
  containers: ContainerSummary[];
}
const Tx = TranslationKeys.TaskPage.OrderStatusCard;

export const OrderDetails: React.FC<Props> = ({ orderId, containers }: Props) => (
  <Grid container {...styles.container}>
    <Grid key="order-containers" {...styles.section}>
      <Txt i18nKey={Tx.CONTAINERS} {...styles.sectionHead} />
      {containers.map((container: ContainerSummary, index) => (
        <Grid container key={`${container}-${index}`} {...styles.containerSection}>
          <Grid key={`${container}-${index}-count`} {...styles.containerCount}>
            {container.count}
          </Grid>
          <Typography key={`${container}-${index}-size`} {...styles.sectionBody}>
            {container.containerSize}ft
          </Typography>
          {containers.length - 1 !== index && <Typography {...styles.dot}> {'\u2022'} </Typography>}
        </Grid>
      ))}
    </Grid>
    <Grid key="order-number" {...styles.section}>
      <Txt i18nKey={Tx.ORDER_NO} {...styles.sectionHead} />
      <Typography {...styles.sectionBody}>#{orderId}</Typography>
    </Grid>
  </Grid>
);

const styles = {
  container: {
    sx: {
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.md,
      color: Design.Alias.Color.neutral100
    }
  },
  section: {
    sx: {
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxxs
    }
  },
  sectionHead: {
    sx: {
      color: 'inherit',
      ...Design.Alias.Text.BodyUniversal.Xs
    }
  },
  sectionBody: {
    sx: {
      color: 'inherit',
      ...Design.Alias.Text.BodyUniversal.SmBold
    }
  },
  containerSection: {
    sx: {
      alignItems: 'center',
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.xxxs
    }
  },
  containerCount: {
    ...Design.Alias.Text.BodyUniversal.XxsBold,
    display: 'flex',
    alignItems: 'center',
    borderRadius: '50%',
    backgroundColor: Design.Alias.Color.accent900,
    color: 'inherit',
    height: Design.Primitives.Spacing.sm,
    justifyContent: 'center',
    lineHeight: 1, // for vertical centering
    width: Design.Primitives.Spacing.sm
  },
  dot: {
    sx: {
      color: 'inherit',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      ...Design.Alias.Text.BodyUniversal.XxsBold
    }
  }
};
