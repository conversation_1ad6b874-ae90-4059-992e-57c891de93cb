import React from 'react';
import { Grid, Typography } from '@mui/material';
import { Design } from '../../../../helpers/Design';
import { CityState } from '../../../../networkRequests/responseEntities/PodsReadyEntities';

interface Props {
  origin: CityState;
  streetAddress?: string;
  scheduledDate?: string;
}

export const StorageStatus: React.FC<Props> = ({ origin, streetAddress, scheduledDate }: Props) => (
  <Grid {...styles.container}>
    <Grid>
      <Grid {...styles.addressBlock}>
        <Typography {...styles.city}>{origin.city}</Typography>
        <Typography {...styles.state}>{origin.state}</Typography>
      </Grid>
      <Grid {...styles.detailsBlock}>
        {streetAddress && (
          <>
            <Typography {...styles.detailsText}>{streetAddress}</Typography>
            <Typography {...styles.detailsText}> {'\u2022'} </Typography>
          </>
        )}
        {scheduledDate && <Typography {...styles.detailsText}>{scheduledDate}</Typography>}
      </Grid>
    </Grid>
  </Grid>
);

const styles = {
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      color: Design.Alias.Color.neutral100
    }
  },
  addressBlock: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'baseline',
      color: 'inherit',
      gap: Design.Primitives.Spacing.xxxs
    }
  },
  city: {
    sx: {
      ...Design.Alias.Text.Heading.Desktop.Xl,
      color: 'inherit'
    }
  },
  state: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: 'inherit'
    }
  },
  detailsBlock: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.xxxs
    }
  },
  detailsText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: 'inherit'
    }
  }
};
