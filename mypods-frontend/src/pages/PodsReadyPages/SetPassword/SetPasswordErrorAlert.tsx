import React from 'react';
import { Alert, AlertTitle } from '@mui/material';
import { ExclamationIcon } from '../../../components/icons/ExclamationIcon';
import { Design } from '../../../helpers/Design';

export type Props = {
  title: string;
  message: string;
};

// -- impls --
export const SetPasswordFieldAlert: React.FC<Props> = ({ title, message }: Props) => (
  <Alert
    severity="error"
    icon={<ExclamationIcon style={{ color: Design.Alias.Color.errorMain }} />}
    {...styles.alert}>
    <AlertTitle
      sx={{
        ...Design.Alias.Text.BodyUniversal.SmBold,
        color: Design.Alias.Color.errorDark
      }}>
      {title}
    </AlertTitle>
    {message}
  </Alert>
);

// -- styles --
const styles = {
  alert: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Alias.Color.errorDark,
      backgroundColor: Design.Alias.Color.errorLight,
      iconColor: Design.Alias.Color.errorMain,
      '.MuiAlert-icon': {
        alignItems: 'center'
      }
    }
  }
};
