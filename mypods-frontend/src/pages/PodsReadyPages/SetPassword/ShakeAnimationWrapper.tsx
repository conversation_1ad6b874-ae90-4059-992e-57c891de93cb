import React, { ReactNode } from 'react';
import { keyframes } from '@mui/system';
import { Grid } from '@mui/material';

// -- Animation Keyframes --
const shakeAnimation = keyframes`
  0% {
    transform: translateX(0)
  }
  10% {
    transform: translateX(-7px)
  }
  20% {
    transform: translateX(7px)
  }
  30% {
    transform: translateX(-7px)
  }
  40% {
    transform: translateX(7px)
  }
  50% {
    transform: translateX(-4px)
  }
  60% {
    transform: translateX(4px)
  }
  70% {
    transform: translateX(-2px)
  }
  80% {
    transform: translateX(2px)
  }
  90% {
    transform: translateX(-1px)
  }
  100% {
    transform: translateX(0)
  }
`;

// -- types --
interface Props {
  shake: boolean;
  children: ReactNode;
}

// -- impls --
export const ShakeAnimationWrapper: React.FC<Props> = ({ shake, children }: Props) => {
  const styles = componentStyles(shake);

  return (
    <Grid container {...styles.container}>
      {children}
    </Grid>
  );
};

// -- styles --
const componentStyles = (shake: boolean) => ({
  container: {
    sx: {
      animation: shake ? `${shakeAnimation} 0.5s ease-in-out` : 'none',
      display: 'inline-block'
    }
  }
});
