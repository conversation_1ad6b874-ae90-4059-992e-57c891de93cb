import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FormHelperText, Grid, TextField } from '@mui/material';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ShowHideButton } from '../../../components/buttons/ShowHideButton';
import { Design } from '../../../helpers/Design';
import { ShakeAnimationWrapper } from './ShakeAnimationWrapper';

export type SetPasswordTextFieldProps = {
  hasAutoFocus: boolean;
  onChange: (password: string) => void;
  helperText?: React.ReactNode;
};
const Tx = TranslationKeys.AccountPage.AccountInfo.Password;

export const SetPasswordTextField: React.FC<SetPasswordTextFieldProps> = ({
  hasAutoFocus,
  onChange,
  helperText
}) => {
  const [shake, setShake] = useState(false);
  const [error, setError] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState<boolean>(false);
  const { t: translate } = useTranslation();

  React.useEffect(() => {
    const hasError = helperText !== undefined;
    setError(hasError);

    if (hasError) {
      setShake(true);
      const timer = setTimeout(() => setShake(false), 500);
      return () => clearTimeout(timer);
    }
  }, [helperText]);

  return (
    <Grid>
      <Grid container {...styles.fieldValue}>
        <ShakeAnimationWrapper shake={shake}>
          <TextField
            autoFocus={hasAutoFocus}
            InputProps={{
              endAdornment: (
                <ShowHideButton
                  onClick={() => setPasswordVisible(!passwordVisible)}
                  dataVisible={passwordVisible}
                  color={Design.Alias.Color.accent900}
                />
              )
            }}
            fullWidth
            color="neutral"
            label={translate(Tx.Labels.VIEW_PASSWORD)}
            error={error}
            onChange={(event) => onChange(event.target.value)}
            type={passwordVisible ? 'text' : 'password'}
            {...styles.passwordInput}
          />
          {helperText && (
            <FormHelperText component="div" {...styles.formHelperText}>
              {helperText}
            </FormHelperText>
          )}
        </ShakeAnimationWrapper>
      </Grid>
    </Grid>
  );
};
const styles = {
  fieldValue: {
    sx: {
      flexDirection: 'column',
      marginBottom: Design.Primitives.Spacing.sm
    }
  },
  formHelperText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Primitives.Color.Semantic.error
    }
  },
  passwordInput: {
    sx: {
      '.MuiFormLabel-root.Mui-focused': {
        color: Design.Alias.Color.accent900
      }
    }
  }
};
