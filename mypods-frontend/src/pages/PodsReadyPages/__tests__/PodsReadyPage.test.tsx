import { PodsReadyRoutes } from '../../../PodsReadyRoutes';
import {
  mockNavigate,
  mockRefreshSession,
  mockSendPasswordOnboardingStart
} from '../../../../setupTests';
import {
  createPodsReadyClaims,
  createRefreshSessionClaims,
  createShowPodsReadySingleOrderResponse,
  mockIsPodsReadyPasswordOnboardingEnabled
} from '../../../testUtils/MyPodsFactories';
import {
  renderWithPoetProvidersAndState,
  runPendingPromises
} from '../../../testUtils/RenderHelpers';
import { PodsReadyPage } from '../PodsReadyPage';
import { vi } from 'vitest';

const mockUseShowPodsReadySingleOrder = vi.hoisted(() => vi.fn());
const mockUseStartPodsReadySession = vi.hoisted(() => vi.fn());

vi.mock('../../../helpers/useShowPodsReadySingleOrder', () => ({
  useShowPodsReadySingleOrder: mockUseShowPodsReadySingleOrder
}));
vi.mock('../../../networkRequests/queries/podsReady/useStartPodsReadySession', () => ({
  useStartPodsReadySession: mockUseStartPodsReadySession
}));

describe('PodsReadyPage', () => {
  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
  });

  const renderPage = async () => {
    const result = renderWithPoetProvidersAndState(<PodsReadyPage />);
    await runPendingPromises();
    return result;
  };

  describe('when password onboarding true', async () => {
    beforeEach(() => {
      mockIsPodsReadyPasswordOnboardingEnabled.mockReturnValue(true);
    });

    describe('When show pods ready single order is enabled', () => {
      beforeEach(() => {
        mockUseShowPodsReadySingleOrder.mockReturnValue(
          createShowPodsReadySingleOrderResponse({ showPodsReadySingleOrder: true })
        );
      });

      it('redirects a user without a password to task page', async () => {
        mockUseStartPodsReadySession.mockReturnValue({
          hasToken: true,
          podsReadySessionClaims: createPodsReadyClaims({ hasPassword: false })
        });

        await renderPage();

        expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.TASKS);
      });

      it('redirects a user with a password main login', async () => {
        mockUseStartPodsReadySession.mockReturnValue({
          hasToken: true,
          podsReadySessionClaims: createPodsReadyClaims({ hasPassword: true })
        });

        await renderPage();

        expect(mockNavigate).not.toHaveBeenCalled();
      });
    });

    describe('When show pods ready single order is not enabled', () => {
      beforeEach(() => {
        mockUseShowPodsReadySingleOrder.mockReturnValue(
          createShowPodsReadySingleOrderResponse({ showPodsReadySingleOrder: false })
        );
      });

      it('redirects a user without a password, but with a token param, to password page and send event', async () => {
        mockUseStartPodsReadySession.mockReturnValue({
          hasToken: true,
          podsReadySessionClaims: createPodsReadyClaims({ hasPassword: false })
        });

        await renderPage();

        expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.SET_PASSWORD, { replace: true });
        expect(mockSendPasswordOnboardingStart).toHaveBeenCalled();
      });

      it('redirects a user without a password and without token sends start password event', async () => {
        mockUseStartPodsReadySession.mockReturnValue({
          hasToken: false,
          podsReadySessionClaims: createPodsReadyClaims({ hasPassword: false })
        });

        await renderPage();

        expect(mockSendPasswordOnboardingStart).toHaveBeenCalled();
      });

      describe('and password onboarding true', () => {
        beforeEach(() => {
          mockIsPodsReadyPasswordOnboardingEnabled.mockReturnValue(true);
        });

        it('redirects a user without a password to set password', async () => {
          mockUseStartPodsReadySession.mockReturnValue({
            hasToken: true,
            podsReadySessionClaims: createPodsReadyClaims({ hasPassword: false })
          });

          await renderPage();

          expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.SET_PASSWORD, {
            replace: true
          });
        });

        it('redirects a user with a password to main login', async () => {
          mockUseStartPodsReadySession.mockReturnValue({
            hasToken: true,
            podsReadySessionClaims: createPodsReadyClaims({ hasPassword: true })
          });

          await renderPage();

          expect(mockNavigate).not.toHaveBeenCalled();
        });
      });

      describe('and password onboarding false', () => {
        beforeEach(() => {
          mockIsPodsReadyPasswordOnboardingEnabled.mockReturnValue(false);
        });

        it('does nothing because backend will throw 403 Forbidden', () => {
          expect(mockNavigate).not.toHaveBeenCalled();
        });
      });
    });
  });
});
