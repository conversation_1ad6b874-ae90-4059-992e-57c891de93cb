import React, { FC } from 'react';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { render, screen, waitFor, within } from '@testing-library/react';
import { mockRefreshSession, mockNavigate } from '../../../../setupTests';
import { createRefreshSessionClaims } from '../../../testUtils/MyPodsFactories';
import {
  renderWithLegacyProvidersAndState,
  runPendingPromises
} from '../../../testUtils/RenderHelpers';
import { PodsReadySuccessPage } from '../PodsReadySuccessPage';
import { ROUTES } from '../../../Routes';
import { TranslationKeys } from '../../../locales/TranslationKeys';

const Tx = TranslationKeys.PodsReady.SuccessPage;
describe('PodsReadySuccessPage', () => {
  let user: UserEvent;
  beforeEach(() => {
    user = userEvent.setup();
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
  });

  const renderPage = async () => {
    const result = renderWithLegacyProvidersAndState(<PodsReadySuccessPage />);
    await runPendingPromises();
    return result;
  };

  it('redirect to the home page when button is clicked', async () => {
    await renderPage();

    const button = screen.getByRole('button', { name: Tx.BUTTON_TEXT });
    await waitFor(async () => await user.click(button));

    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.HOME, { replace: true });
  });
});
