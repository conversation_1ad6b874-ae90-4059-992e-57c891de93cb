import React, { ReactNode } from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { Design } from '../../helpers/Design';
import { Txt } from '../../components/Txt';
import { theme } from '../../PodsTheme';

export const Section: React.FC<{ translationKey: string; children: ReactNode }> = ({
  translationKey,
  children
}) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Grid container>
      <Txt
        variant="h2"
        style={
          isMobile ? Design.Alias.Text.Heading.Mobile.Md : Design.Alias.Text.Heading.Desktop.Md
        }
        sx={{
          color: Design.Alias.Color.accent900
        }}
        i18nKey={translationKey}
      />
      <Grid container item flexDirection="column">
        {children}
      </Grid>
    </Grid>
  );
};
