import { AccountPage } from '../AccountPage';
import { screen, waitFor, within } from '@testing-library/react';
import React, { act } from 'react';
import {
  CustomerContextViewer,
  expectCustomerContextContains,
  renderWithPoetProvidersAndState
} from '../../../testUtils/RenderHelpers';
import {
  createCustomer,
  createCustomerAddress,
  createEmail,
  createEmptyCustomerAddress,
  createPhone,
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../testUtils/MyPodsFactories';
import {
  Customer,
  CustomerAddress,
  CustomerType,
  formatAddress,
  UpdateBillingAddressRequest,
  UpdateSecondaryPhoneRequest,
  UpdateShippingAddressRequest,
  UpdateSmsOptInRequest
} from '../../../networkRequests/responseEntities/CustomerEntities';
import { accountPageActions as actions, accountPageViews as views } from './AccountPageViews';
import userEvent from '@testing-library/user-event';
import {
  BILLING_ADDRESS_TYPE,
  NEW_IDENTITY,
  US_REGION_CODE
} from '../../../networkRequests/MyPodsConstants';
import {
  mockedUseFeatureFlags,
  mockGetCustomer,
  mockRefreshSession,
  mockUpdateBillingAddress,
  mockUpdateSecondaryPhone,
  mockUpdateShippingAddress,
  mockUpdateSmsOptIn
} from '../../../../setupTests';
import { RefreshSessionClaims } from '../../../networkRequests/responseEntities/AuthorizationEntities';

describe('AccountPage', () => {
  const customerId = '*********';
  const refresh = createRefreshSessionClaims({ customerId });
  const customer = createCustomer({
    id: customerId,
    primaryPhone: createPhone({ number: '************' }),
    secondaryPhone: createPhone({ number: '************' }),
    billingAddress: createCustomerAddress({ address1: '123 Maple Ave' }),
    shippingAddress: createCustomerAddress({ address1: '321 Oak Blvd' }),
    smsOptIn: true,
    securityQuestionAnswer: {
      question: 'What is your favorite state fair?',
      answer: 'Hometown, USA'
    },
    customerType: CustomerType.RESIDENTIAL
  });
  const sessionClaims = createRefreshSessionClaims({
    customerId: customerId,
    firstName: customer.firstName,
    lastName: customer.lastName,
    username: customer.email?.address,
    type: customer.customerType,
    ownedCuids: [customerId]
  });

  async function renderPage(
    initialCustomer: Customer = customer,
    initialRefresh: RefreshSessionClaims = refresh
  ) {
    mockGetCustomer.mockResolvedValue(initialCustomer);
    mockRefreshSession.mockResolvedValue(initialRefresh);
    let result = renderWithPoetProvidersAndState(
      <>
        <AccountPage />
        <CustomerContextViewer />
      </>
    );

    await waitFor(() => views.addressInfo.section());
    return result;
  }

  async function renderPageWithDesyncedEmails() {
    mockGetCustomer.mockResolvedValue(
      createCustomer({
        isConverted: true,
        email: createEmail({ address: '<EMAIL>' }),
        username: '<EMAIL>'
      })
    );

    mockRefreshSession.mockResolvedValue(
      createRefreshSessionClaims({ username: '<EMAIL>' })
    );

    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({
        isEmailVerificationEnabled: () => true
      })
    );

    renderWithPoetProvidersAndState(
      <>
        <AccountPage />
        <CustomerContextViewer />
      </>
    );

    await waitFor(() => views.addressInfo.section());
  }

  beforeEach(() => {
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() => createUseFeatureFlagResult());
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims(sessionClaims));
    mockGetCustomer.mockResolvedValue(customer);
  });

  describe('Account Info', () => {
    it('should display first and last name', async () => {
      await renderPage();

      const fullName = customer.firstName + ' ' + customer.lastName;
      expect(await screen.findByText(fullName)).toBeInTheDocument();
    });

    it('should render the password section if the customer is converted', async () => {
      await renderPage();

      expect(within(await views.accountInfo.section()).getByText('********')).toBeInTheDocument();
    });

    it('should display email', async () => {
      await renderPage();

      expect(
        within(await views.accountInfo.section()).getByText(customer.email!.address!)
      ).toBeInTheDocument();
    });

    describe('email', () => {
      it('when email verification is off, is not editable', async () => {
        mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
          createUseFeatureFlagResult({
            isEmailVerificationEnabled: () => false
          })
        );
        await renderPage();

        expect(
          within(await views.accountInfo.section()).getByTestId('email-field')
        ).toBeInTheDocument();

        expect(
          within(await screen.findByTestId('email-field')).queryByTestId('account-edit-button')
        ).not.toBeInTheDocument();
      });

      describe('when email verification flag is enabled', () => {
        it('is editable', async () => {
          //  Component Tests are in the Editable Email Field Test file
          mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
            createUseFeatureFlagResult({
              isEmailVerificationEnabled: () => true
            })
          );
          await renderPage();

          expect(
            within(await views.accountInfo.section()).getByTestId('otp-email-field')
          ).toBeInTheDocument();

          expect(
            within(await screen.findByTestId('otp-email-field')).getByTestId('account-edit-button')
          ).toBeInTheDocument();
        });

        it('when the customer email is out of sync', async () => {
          //  Component Tests are in the OutOfSyncEmailField Test file
          await renderPageWithDesyncedEmails();

          expect(
            within(await views.accountInfo.section()).getByTestId('out-of-sync-email-field')
          ).toBeInTheDocument();
        });
      });
    });
  });

  describe('Contact Info', () => {
    it('should display primary phone number', async () => {
      await renderPage();

      expect(await screen.findByText(customer.primaryPhone!.number!));
    });

    it('should display secondary phone number for residential customers', async () => {
      await renderPage();

      expect(await screen.findByText(customer.secondaryPhone!.number!));
    });

    it('should NOT display secondary phone number for commercial customers', async () => {
      const commercialCustomer: RefreshSessionClaims = {
        ...sessionClaims,
        type: CustomerType.COMMERCIAL
      };
      mockGetCustomer.mockResolvedValue(commercialCustomer);

      await renderPage(customer, commercialCustomer);

      expect(screen.queryByText(customer.secondaryPhone!.number!)).not.toBeInTheDocument();
    });

    it('should display N/A when phone number not provided', async () => {
      const customerWithoutSecondaryPhone = { ...customer, secondaryPhone: undefined };
      mockGetCustomer.mockResolvedValue(customerWithoutSecondaryPhone);

      await renderPage(customerWithoutSecondaryPhone);

      expect(within(await views.contactInfo.section()).getByText('N/A')).toBeInTheDocument();
    });

    it('should update primary phone number', async () => {
      // See EditableAccountField tests;
      return true;
    });

    it('should update secondary phone number', async () => {
      let newPhoneNumber = '************';

      await renderPage();
      await actions.updateSecondaryPhoneNumber(newPhoneNumber);

      await act(async () => {
        await userEvent.click(views.secondaryPhone.saveButton());
      });

      const expectedRequest: UpdateSecondaryPhoneRequest = {
        phone: { id: customer.secondaryPhone!.id, number: newPhoneNumber }
      };
      expect(views.secondaryPhone.accountField()).toBeInTheDocument(); // Restore the display view
      expect(mockUpdateSecondaryPhone).toHaveBeenCalledWith(expectedRequest);
      expect(screen.getByText(newPhoneNumber)).toBeInTheDocument();
      await expectCustomerContextContains(newPhoneNumber);
    });
  });

  describe('Communication', () => {
    it('should display enabled switch when sms opt in is true', async () => {
      await renderPage();
      await waitFor(() => views.smsPreferences.switch());

      expect(views.smsPreferences.switch()).toHaveProperty('checked', true);
    });

    it('should update sms opt in and toggle switch when switch is clicked', async () => {
      await renderPage();
      await waitFor(() => views.smsPreferences.switch());

      await act(async () => {
        await userEvent.click(views.smsPreferences.switch());
      });

      const expectedRequest: UpdateSmsOptInRequest = {
        lastName: customer.lastName,
        primaryPhone: customer.primaryPhone,
        secondaryPhone: customer.secondaryPhone,
        newSmsOptIn: false
      };
      expect(mockUpdateSmsOptIn).toHaveBeenCalledWith(expectedRequest);
      expect(views.smsPreferences.switch()).toHaveProperty('checked', false);
      await expectCustomerContextContains('"smsOptIn":false');
    });

    it('should NOT display SMS Preferences for commercial customers', async () => {
      const commercialCustomer: RefreshSessionClaims = {
        ...sessionClaims,
        type: CustomerType.COMMERCIAL
      };
      mockGetCustomer.mockResolvedValue(commercialCustomer);

      await renderPage(customer, commercialCustomer);

      expect(screen.queryByTestId('sms-opt-in-switch')).not.toBeInTheDocument();
    });
  });

  describe('Address Info', () => {
    it('should format and render the billing address', async () => {
      await renderPage();

      expect(await screen.findByText(formatAddress(customer.billingAddress))).toBeInTheDocument();
    });

    it('should display N/A shipping address when missing', async () => {
      let customerWithNoShippingAddress: Customer = { ...customer, shippingAddress: undefined };
      mockGetCustomer.mockResolvedValue(customerWithNoShippingAddress);

      await renderPage(customerWithNoShippingAddress);

      expect(within(await views.addressInfo.section()).getByText('N/A')).toBeInTheDocument();
    });

    it('should update billing address', async () => {
      const updatedAddress: CustomerAddress = {
        ...customer.billingAddress!!,
        address1: '123 State St'
      };

      await renderPage();
      await actions.updateBillingAddress(updatedAddress);
      await act(async () => {
        await userEvent.click(views.billingAddress.saveButton());
      });

      const expectedRequest: UpdateBillingAddressRequest = { address: updatedAddress };
      expect(views.billingAddress.accountField()).toBeInTheDocument(); // Restore the display view
      expect(mockUpdateBillingAddress).toHaveBeenCalledWith(expectedRequest);
      expect(screen.getByText(formatAddress(updatedAddress))).toBeInTheDocument();
      await expectCustomerContextContains(updatedAddress.address1!!);
    });

    it('should update shipping address', async () => {
      const updatedAddress: CustomerAddress = {
        ...customer.billingAddress!!,
        address1: '123 Madison Rd'
      };

      await renderPage();
      await actions.updateShippingAddress(updatedAddress.address1!!);

      await act(async () => {
        await userEvent.click(views.shippingAddress.saveButton());
      });

      const expectedRequest: UpdateShippingAddressRequest = { address: updatedAddress };
      expect(views.shippingAddress.accountField()).toBeInTheDocument(); // Restore the display view
      expect(mockUpdateShippingAddress).toHaveBeenCalledWith(expectedRequest);
      expect(screen.getByText(formatAddress(updatedAddress))).toBeInTheDocument();
      await expectCustomerContextContains(updatedAddress.address1!!);
    });

    it('should update billing address when current is null', async () => {
      const updatedAddress: CustomerAddress = {
        ...customer.billingAddress!!,
        address1: '123 State St'
      };

      const emptyBillingAddress: CustomerAddress = createEmptyCustomerAddress({
        addressType: BILLING_ADDRESS_TYPE
      });
      let customerWithNoBillingAddress: Customer = {
        ...customer,
        billingAddress: emptyBillingAddress
      };
      mockGetCustomer.mockResolvedValue(customerWithNoBillingAddress);

      await renderPage(customerWithNoBillingAddress);

      await actions.updateBillingAddress(updatedAddress);
      await act(async () => {
        await userEvent.click(views.billingAddress.saveButton());
      });

      const expectedRequest: UpdateBillingAddressRequest = {
        address: {
          ...updatedAddress,
          id: NEW_IDENTITY,
          address2: '',
          addressType: BILLING_ADDRESS_TYPE,
          regionCode: US_REGION_CODE
        }
      };
      expect(mockUpdateBillingAddress).toHaveBeenCalledWith(expectedRequest);
    });
  });
});
