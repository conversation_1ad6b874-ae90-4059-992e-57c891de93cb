import { screen, waitFor, within } from '@testing-library/react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import userEvent from '@testing-library/user-event';
import { CustomerAddress } from '../../../networkRequests/responseEntities/CustomerEntities';
import { act } from 'react';

const views = {
  editButtonWithin: (accountField: HTMLElement) =>
    within(accountField).getByRole('button', {
      name: TranslationKeys.CommonComponents.EDIT_BUTTON
    }),
  cancelButtonWithin: (accountField: HTMLElement) =>
    within(accountField).getByRole('button', {
      name: TranslationKeys.CommonComponents.CANCEL_BUTTON
    }),
  saveButtonWithin: (accountField: HTMLElement) =>
    within(accountField).getByRole('button', {
      name: TranslationKeys.CommonComponents.SAVE_BUTTON
    }),
  continueButtonWithin: (accountField: HTMLElement) =>
    within(accountField).getByRole('button', {
      name: TranslationKeys.CommonComponents.CONTINUE_BUTTON
    }),
  email: {
    baseField: () => screen.getByTestId('editable-email-field'),
    accountField: () => within(views.email.baseField()).getByTestId('account-field'),
    editableAccountField: () =>
      within(views.email.baseField()).getByTestId('editable-account-field'),
    inputField: () =>
      within(views.email.editableAccountField()).getByLabelText(
        TranslationKeys.AccountPage.AccountInfo.Email.LABEL
      ),
    editButton: () => waitFor(() => views.editButtonWithin(views.email.accountField())),
    cancelButton: () => views.cancelButtonWithin(views.email.editableAccountField()),
    saveButton: () => views.saveButtonWithin(views.email.editableAccountField()),
    continueButton: () => views.continueButtonWithin(views.email.editableAccountField())
  },
  outOfSyncEmail: {
    baseField: () => screen.getByTestId('out-of-sync-email-field'),
    accountField: () => within(views.outOfSyncEmail.baseField()).getByTestId('account-field'),
    inputField: () =>
      within(views.outOfSyncEmail.baseField()).getByLabelText(
        TranslationKeys.AccountPage.AccountInfo.Email.LABEL
      ),
    editButton: () => waitFor(() => views.editButtonWithin(views.outOfSyncEmail.accountField())),
    cancelButton: () => views.cancelButtonWithin(views.outOfSyncEmail.baseField()),
    saveButton: () => views.saveButtonWithin(views.outOfSyncEmail.baseField())
  },
  pin: {
    baseField: () => screen.getByTestId('editable-pin-field'),
    editButton: () => views.editButtonWithin(views.pin.baseField()),
    cancelButton: () => views.cancelButtonWithin(views.pin.baseField()),
    saveButton: () => views.saveButtonWithin(views.pin.baseField()),
    inputField: () =>
      within(views.pin.baseField()).getByLabelText(
        TranslationKeys.AccountPage.SupportInfo.Labels.SUPPORT_PIN
      ),
    showHideButton: () =>
      within(views.pin.baseField()).getByRole('button', {
        name: 'Show/Hide Button'
      })
  },
  password: {
    baseView: () => screen.getByTestId('password-view'),
    editView: () => screen.queryByTestId('edit-password-view'),
    editButton: () => views.editButtonWithin(views.password.baseView()),
    cancelButton: () => views.cancelButtonWithin(views.password.baseView()),
    saveButton: () => views.saveButtonWithin(views.password.baseView()),
    currentInputField: () =>
      within(
        within(views.password.baseView()).getByTestId(
          TranslationKeys.AccountPage.AccountInfo.Password.Labels.CURRENT_PASSWORD
        )
      ).getByLabelText(TranslationKeys.AccountPage.AccountInfo.Password.Labels.VIEW_PASSWORD),
    newInputField: () =>
      within(
        within(views.password.baseView()).getByTestId(
          TranslationKeys.AccountPage.AccountInfo.Password.Labels.NEW_PASSWORD
        )
      ).getByLabelText(TranslationKeys.AccountPage.AccountInfo.Password.Labels.VIEW_PASSWORD),

    currentShowHideButton: () =>
      within(
        within(views.password.baseView()).getByTestId(
          TranslationKeys.AccountPage.AccountInfo.Password.Labels.CURRENT_PASSWORD
        )
      ).getByRole('button', {
        name: 'Show/Hide Button'
      }),
    showHideButton: () =>
      within(views.password.baseView()).getByRole('button', {
        name: 'Show/Hide Button'
      })
  },
  primaryPhone: {
    baseField: () => screen.getByTestId('primary-phone-field'),
    accountField: () => within(views.primaryPhone.baseField()).getByTestId('account-field'),
    editableAccountField: () =>
      within(views.primaryPhone.baseField()).getByTestId('editable-account-field'),
    inputField: () =>
      within(views.primaryPhone.editableAccountField()).getByLabelText(
        TranslationKeys.AccountPage.ContactInfo.Labels.PRIMARY_PHONE
      ),
    editButton: () => waitFor(() => views.editButtonWithin(views.primaryPhone.accountField())),
    cancelButton: () => views.cancelButtonWithin(views.primaryPhone.editableAccountField()),
    saveButton: () => views.saveButtonWithin(views.primaryPhone.editableAccountField())
  },
  secondaryPhone: {
    baseField: () => screen.getByTestId('secondary-phone-field'),
    accountField: () => within(views.secondaryPhone.baseField()).getByTestId('account-field'),
    editableAccountField: () =>
      within(views.secondaryPhone.baseField()).getByTestId('editable-account-field'),
    inputField: () =>
      within(views.secondaryPhone.editableAccountField()).getByLabelText(
        TranslationKeys.AccountPage.ContactInfo.Labels.SECONDARY_PHONE
      ),
    editButton: () => waitFor(() => views.editButtonWithin(views.secondaryPhone.accountField())),
    saveButton: () => views.saveButtonWithin(views.secondaryPhone.editableAccountField())
  },
  smsPreferences: {
    switch: () => within(screen.getByTestId('sms-opt-in-switch')).getByRole('checkbox')
  },
  billingAddress: {
    baseField: () => screen.getByTestId('billing-address-field'),
    accountField: () => within(views.billingAddress.baseField()).getByTestId('account-field'),
    editableAccountField: () =>
      within(views.billingAddress.baseField()).getByTestId('editable-account-field'),
    address1Input: () =>
      within(views.billingAddress.editableAccountField()).getByLabelText(
        TranslationKeys.AccountPage.AddressInfo.InputFields.ADDRESS1
      ),
    address2Input: () =>
      within(views.billingAddress.editableAccountField()).getByLabelText(
        TranslationKeys.AccountPage.AddressInfo.InputFields.ADDRESS2
      ),
    postalCodeInput: () =>
      within(views.billingAddress.editableAccountField()).getByLabelText(
        TranslationKeys.AccountPage.AddressInfo.InputFields.POSTAL_CODE
      ),
    cityInput: () =>
      within(views.billingAddress.editableAccountField()).getByLabelText(
        TranslationKeys.AccountPage.AddressInfo.InputFields.CITY
      ),
    stateInput: () =>
      within(views.billingAddress.editableAccountField()).getByLabelText(
        TranslationKeys.AccountPage.AddressInfo.InputFields.STATE
      ),
    editButton: () => views.editButtonWithin(views.billingAddress.accountField()),
    saveButton: () => views.saveButtonWithin(views.billingAddress.editableAccountField())
  },
  shippingAddress: {
    baseField: () => screen.getByTestId('shipping-address-field'),
    accountField: () => within(views.shippingAddress.baseField()).getByTestId('account-field'),
    editableAccountField: () =>
      within(views.shippingAddress.baseField()).getByTestId('editable-account-field'),
    address1Input: () =>
      within(views.shippingAddress.editableAccountField()).getByLabelText(
        TranslationKeys.AccountPage.AddressInfo.InputFields.ADDRESS1
      ),
    editButton: () => views.editButtonWithin(views.shippingAddress.accountField()),
    saveButton: () => views.saveButtonWithin(views.shippingAddress.editableAccountField())
  },
  accountInfo: {
    section: async () => await screen.findByTestId('account-info-section')
  },
  contactInfo: {
    section: async () => await screen.findByTestId('contact-info-section')
  },
  addressInfo: {
    section: async () => await screen.findByTestId('address-info-section')
  },
  supportInfo: {
    section: async () => await screen.findByTestId('support-section')
  }
};

const actions = {
  updateEmail: async (email: string) => {
    expect(views.email.accountField()).toBeInTheDocument();
    await act(async () => {
      await userEvent.click(await views.email.editButton());
    });
    expect(views.email.editableAccountField()).toBeInTheDocument();

    await act(async () => {
      await userEvent.clear(views.email.inputField());
      await userEvent.type(views.email.inputField(), email);
    });
  },
  editOutOfSyncEmail: async () => {
    await act(async () => {
      await userEvent.click(await views.outOfSyncEmail.editButton());
    });
  },
  selectRadioChoice: async (label: string) => {
    await act(async () => {
      await userEvent.click(screen.getByLabelText(label));
    });
  },
  enterEmail: async (email: string) => {
    await act(async () => {
      await userEvent.clear(views.outOfSyncEmail.inputField());
      await userEvent.type(views.outOfSyncEmail.inputField(), email);
    });
  },
  saveOutOfSyncEmail: async () => {
    await act(async () => {
      await userEvent.click(views.outOfSyncEmail.saveButton());
    });
  },
  updatePrimaryPhoneNumber: async (newPhoneNumber: string) => {
    expect(views.primaryPhone.accountField()).toBeInTheDocument();
    await act(async () => {
      await userEvent.click(await views.primaryPhone.editButton());
    });
    expect(views.primaryPhone.editableAccountField()).toBeInTheDocument();

    await act(async () => {
      await userEvent.clear(views.primaryPhone.inputField());
      await userEvent.type(views.primaryPhone.inputField(), newPhoneNumber);
    });
  },
  updateSecondaryPhoneNumber: async (newPhoneNumber: string) => {
    expect(views.secondaryPhone.accountField()).toBeInTheDocument();
    await act(async () => {
      await userEvent.click(await views.secondaryPhone.editButton());
    });

    expect(views.secondaryPhone.editableAccountField()).toBeInTheDocument();

    await act(async () => {
      await userEvent.clear(views.secondaryPhone.inputField());
      await userEvent.type(views.secondaryPhone.inputField(), newPhoneNumber);
    });
  },
  updateBillingAddress: async (address: CustomerAddress) => {
    expect(views.billingAddress.accountField()).toBeInTheDocument();
    await act(async () => userEvent.click(views.billingAddress.editButton()));
    expect(views.billingAddress.editableAccountField()).toBeInTheDocument();

    await act(async () => {
      await userEvent.clear(views.billingAddress.address1Input());
      await userEvent.clear(views.billingAddress.address2Input());
      await userEvent.clear(views.billingAddress.postalCodeInput());
      await userEvent.clear(views.billingAddress.cityInput());
      await userEvent.clear(views.billingAddress.stateInput());
      await userEvent.type(views.billingAddress.address1Input(), address.address1!!);
      if ((address.address2 ?? '') != '')
        await userEvent.type(views.billingAddress.address2Input(), address.address2!!);
      await userEvent.type(views.billingAddress.postalCodeInput(), address.postalCode!!);
      await userEvent.type(views.billingAddress.cityInput(), address.city!!);
      await userEvent.type(views.billingAddress.stateInput(), address.state!!);
    });
  },
  updateShippingAddress: async (newAddress1: string) => {
    expect(views.shippingAddress.accountField()).toBeInTheDocument();
    await act(async () => {
      await userEvent.click(views.shippingAddress.editButton());
    });

    expect(views.shippingAddress.editableAccountField()).toBeInTheDocument();

    await act(async () => {
      await userEvent.clear(views.shippingAddress.address1Input());
      await userEvent.type(views.shippingAddress.address1Input(), newAddress1);
    });
  }
};

export const accountPageViews = views;
export const accountPageActions = actions;
