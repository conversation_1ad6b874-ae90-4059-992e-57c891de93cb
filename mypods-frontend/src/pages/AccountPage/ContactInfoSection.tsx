import React from 'react';
import { Grid } from '@mui/material';
import { PrimaryPhoneNumberField } from './AccountFields/EditableAccountField/PrimaryPhoneNumberField';
import { SecondaryPhoneNumberField } from './AccountFields/EditableAccountField/SecondaryPhoneNumberField';
import { useRefreshSession } from '../../networkRequests/queries/useRefreshSession';

export const ContactInfoSection: React.FC = () => {
  const { sessionClaims } = useRefreshSession();
  return (
    <Grid data-testid="contact-info-section">
      <PrimaryPhoneNumberField />
      {sessionClaims.type !== 'COMMERCIAL' && <SecondaryPhoneNumberField />}
    </Grid>
  );
};
