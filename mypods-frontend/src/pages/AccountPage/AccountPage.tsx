import React from 'react';
import { Grid } from '@mui/material';
import { AccountInfoSection } from './AccountInfoSection';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { ContactInfoSection } from './ContactInfoSection';
import { Section } from './Section';
import { PageLayout } from '../../components/PageLayout';
import { AddressInfoSection } from './AddressInfoSection';
import { CustomerNameSection } from './CustomerNameSection';
import { CommunicationPreferencesSection } from './CommunicationPreferencesSection';
import { Design } from '../../helpers/Design';
import { useRefreshSession } from '../../networkRequests/queries/useRefreshSession';

export const AccountPage = () => {
  const { sessionClaims } = useRefreshSession();

  return (
    <PageLayout columnsLg={6}>
      <CustomerNameSection />
      <Grid container flexDirection="column" rowGap={Design.Primitives.Spacing.lgPlus}>
        <Section translationKey={TranslationKeys.AccountPage.AccountInfo.HEADER}>
          <AccountInfoSection />
        </Section>
        <Section translationKey={TranslationKeys.AccountPage.ContactInfo.HEADER}>
          <ContactInfoSection />
        </Section>
        {sessionClaims.type !== 'COMMERCIAL' && (
          <Section translationKey={TranslationKeys.AccountPage.Communication.HEADER}>
            <CommunicationPreferencesSection />
          </Section>
        )}
        <Section translationKey={TranslationKeys.AccountPage.AddressInfo.HEADER}>
          <AddressInfoSection />
        </Section>
      </Grid>
    </PageLayout>
  );
};
