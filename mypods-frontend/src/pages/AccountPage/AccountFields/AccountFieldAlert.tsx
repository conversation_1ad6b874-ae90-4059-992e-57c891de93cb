import React from 'react';
import { Alert, AlertTitle } from '@mui/material';
import { ExclamationIcon } from '../../../components/icons/ExclamationIcon';
import { Design } from '../../../helpers/Design';

export type AccountFieldAlertProps = {
  title: string;
  message: string;
};

// -- impls --
export const AccountFieldAlert: React.FC<AccountFieldAlertProps> = ({
  title,
  message
}: AccountFieldAlertProps) => {
  const styles = alertStyles();
  return (
    <Alert
      severity="error"
      icon={<ExclamationIcon style={{ color: Design.Alias.Color.errorMain }} />}
      {...styles.alert}>
      <AlertTitle
        sx={{
          ...Design.Alias.Text.BodyUniversal.MdBold,
          color: Design.Alias.Color.errorDark
        }}>
        {title}
      </AlertTitle>
      {message}
    </Alert>
  );
};

// -- styles --
const alertStyles = () => ({
  alert: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: Design.Alias.Color.errorDark,
      backgroundColor: Design.Alias.Color.errorLight,
      iconColor: Design.Alias.Color.errorMain,
      '.MuiAlert-icon': {
        alignItems: 'center'
      }
    }
  }
});
