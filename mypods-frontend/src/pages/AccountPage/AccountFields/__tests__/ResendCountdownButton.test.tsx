import { act, screen } from '@testing-library/react';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import {
  RESEND_COUNTDOWN_SECONDS,
  ResendCountdownButton
} from '../ChallengeEmailField/ResendCountdownButton';
import { vi } from 'vitest';
import React from 'react';
import { mockGetCustomer, mockRefreshSession } from '../../../../../setupTests';
import {
  renderWithPoetProvidersAndState,
  testQueryClient
} from '../../../../testUtils/RenderHelpers';
import {
  createCustomer,
  createEmail,
  createRefreshSessionClaims
} from '../../../../testUtils/MyPodsFactories';
import { QueryClient } from '@tanstack/react-query';
import { Customer } from '../../../../networkRequests/responseEntities/CustomerEntities';
import { RefreshSessionClaims } from '../../../../networkRequests/responseEntities/AuthorizationEntities';

const Tx = TranslationKeys.AccountPage.AccountInfo.Email.OtpVerification.Dialog;

describe('ResendCountdownButton.tsx', () => {
  let queryClient: QueryClient;
  let sessionClaims: RefreshSessionClaims;
  const customerId = '*********';
  const emailAddress = '<EMAIL>';
  let customer: Customer;
  let newUsername = '<EMAIL>';
  let now: Date;

  beforeEach(() => {
    vi.useFakeTimers();
    now = new Date();

    queryClient = testQueryClient();
    sessionClaims = createRefreshSessionClaims({ customerId: customerId, username: emailAddress });
    customer = createCustomer({
      id: customerId,
      email: createEmail({ id: 42, address: emailAddress }),
      username: emailAddress
    });

    mockRefreshSession.mockResolvedValue(sessionClaims);
    mockGetCustomer.mockResolvedValue(customer);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const renderCountdownButton = async (secondsRemaining?: number) => {
    const timeRemaining = secondsRemaining ?? RESEND_COUNTDOWN_SECONDS;

    const result = renderWithPoetProvidersAndState(
      <ResendCountdownButton newUsername={newUsername} resendCountdownSeconds={timeRemaining} />,
      { customQueryClient: queryClient }
    );
    await act(async () => {
      await vi.advanceTimersToNextTimerAsync();
    });

    return result;
  };

  it('should show the default text, along with a max-value countdown on render', async () => {
    await renderCountdownButton();

    expect(screen.getByText(Tx.MISSING_CODE)).toBeVisible();
    expect(screen.getByText(`${Tx.RESEND_CODE_COUNTDOWN}[60]`)).toBeVisible();
  });

  it('should show the default text, and reduce the time by second intervals', async () => {
    await renderCountdownButton();
    await act(async () => {
      await vi.advanceTimersToNextTimerAsync();
      await vi.advanceTimersToNextTimerAsync();
    });

    expect(screen.getByText(`${Tx.RESEND_CODE_COUNTDOWN}[59]`)).toBeVisible();
  });

  // Will re-look this test cases to address the failure
  it('should show a refresh code button, when the countdown has finished', async () => {
    await renderCountdownButton(1);

    await act(async () => {
      await vi.advanceTimersToNextTimerAsync();
      await vi.advanceTimersToNextTimerAsync();
    });

    expect(screen.getByRole('button', { name: Tx.RESEND_CODE_BUTTON })).toBeVisible();
    // The following button behaviors should also occur, but the combination of the timers
    // and button component (even mocked) timed out.
    // Call the EmailChallenge Endpoint, Reset the timer, Disable the button until the timer appears
  });
});
