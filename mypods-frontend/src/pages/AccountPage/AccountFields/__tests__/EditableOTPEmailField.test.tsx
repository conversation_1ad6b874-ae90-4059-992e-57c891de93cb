import { AxiosError, AxiosResponse } from 'axios';
import userEvent from '@testing-library/user-event';
import { screen, waitFor, within } from '@testing-library/react';
import {
  ChallengeEmailErrorStatus,
  ChallengeEmailRequest,
  Customer
} from '../../../../networkRequests/responseEntities/CustomerEntities';
import { mockChallengeEmail, mockGetCustomer, mockRefreshSession } from '../../../../../setupTests';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import {
  createCustomer,
  createEmail,
  createRefreshSessionClaims
} from '../../../../testUtils/MyPodsFactories';
import { renderWithPoetProvidersAndState } from '../../../../testUtils/RenderHelpers';
import { expectNotificationAlertContainsTitle } from '../../../../testUtils/assertions';
import { EditableOTPE<PERSON><PERSON>ield } from '../ChallengeEmailField/EditableOTPEmailField';
import { RefreshSessionClaims } from '../../../../networkRequests/responseEntities/AuthorizationEntities';

const Tx = TranslationKeys.AccountPage.AccountInfo.Email;
const DialogTx = TranslationKeys.AccountPage.AccountInfo.Email.OtpVerification.Dialog;

const views = {
  baseField: () => screen.getByTestId('editable-otp-email-field'),
  accountField: () => within(views.baseField()).getByTestId('account-field'),
  editableAccountField: () => within(views.baseField()).getByTestId('editable-account-field'),
  inputField: () =>
    within(views.editableAccountField()).getByLabelText(
      TranslationKeys.AccountPage.AccountInfo.Email.LABEL
    ),
  editButton: () =>
    within(views.editableAccountField()).getByRole('button', {
      name: TranslationKeys.CommonComponents.EDIT_BUTTON
    }),
  cancelButton: () =>
    within(views.editableAccountField()).getByRole('button', {
      name: TranslationKeys.CommonComponents.CANCEL_BUTTON
    }),
  continueButton: () =>
    within(views.editableAccountField()).getByRole('button', {
      name: TranslationKeys.CommonComponents.CONTINUE_BUTTON
    })
};

const actions = {
  updateEmail: async (email: string) => {
    expect(views.accountField()).toBeInTheDocument();
    await waitFor(async () => {
      await userEvent.click(views.editButton());
    });
    expect(views.editableAccountField()).toBeInTheDocument();

    await waitFor(async () => {
      await userEvent.clear(views.inputField());
      await userEvent.type(views.inputField(), email);
    });
  }
};

describe('OTPEmailField', () => {
  let sessionClaims: RefreshSessionClaims;
  const customerId = '*********';
  const emailAddress = '<EMAIL>';
  let customer: Customer;
  let newEmail = '<EMAIL>';

  beforeEach(() => {
    sessionClaims = createRefreshSessionClaims({ customerId: customerId, username: emailAddress });
    customer = createCustomer({
      id: customerId,
      email: createEmail({ id: 42, address: emailAddress }),
      username: emailAddress
    });

    mockRefreshSession.mockResolvedValue(sessionClaims);
    mockGetCustomer.mockResolvedValue(customer);
  });

  async function renderOTPEmailField() {
    const result = renderWithPoetProvidersAndState(<EditableOTPEmailField />);

    await waitFor(() => views.editButton());
    return result;
  }

  async function renderAndChallengeEmail() {
    const result = await renderOTPEmailField();
    await actions.updateEmail(newEmail);
    await waitFor(async () => {
      await userEvent.click(views.continueButton());
    });
    return result;
  }

  describe('given the user receives a server error for the email, they will see helper text upon save', async () => {
    test.each([
      [ChallengeEmailErrorStatus.EMAIL_ALREADY_IN_USE, Tx.HelperText.EMAIL_ALREADY_IN_USE],
      [ChallengeEmailErrorStatus.INVALID_EMAIL, Tx.HelperText.INVALID_EMAIL],
      [ChallengeEmailErrorStatus.INVALID_EMAIL, Tx.HelperText.INVALID_EMAIL]
    ])('when %s response code should show %s message', async (responseCode, displayText) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 500
      } as AxiosResponse;

      mockChallengeEmail.mockRejectedValue(
        new AxiosError('message', '500', undefined, undefined, response)
      );

      await renderAndChallengeEmail();

      expect(within(views.baseField()).getByText(displayText)).toBeInTheDocument();
    });
  });

  describe('displays alert notifications when the updateEmail call fails with an error', () => {
    test.each([
      [
        ChallengeEmailErrorStatus.ERROR,
        Tx.Notifications.Title.ERROR,
        Tx.Notifications.Message.ERROR
      ]
    ])('when %s occurs display %s and %s', async (responseCode, title, message) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 500
      } as AxiosResponse;

      mockChallengeEmail.mockRejectedValue(
        new AxiosError('message', '500', undefined, undefined, response)
      );

      await renderAndChallengeEmail();

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent(title);
        expect(screen.getByRole('alert')).toHaveTextContent(message);
      });
    });
  });

  it('given an error occurs, displays generic failure', async () => {
    mockChallengeEmail.mockRejectedValue('Something went wrong.');

    await renderAndChallengeEmail();

    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
    );
  });

  describe('managing presentation of the OTP validation dialog', () => {
    // Dialog properties are managed in the component's test
    it('should call challenge email and show dialog when continue is clicked', async () => {
      await renderAndChallengeEmail();

      const dialog = await waitFor(async () => await screen.findByRole('dialog'));
      expect(dialog).toBeInTheDocument();

      const expectedRequest: ChallengeEmailRequest = { email: newEmail };
      expect(views.editableAccountField()).toBeInTheDocument();
      expect(mockChallengeEmail).toHaveBeenCalledWith(expectedRequest);

      expect(dialog).toBeInTheDocument();
      expect(within(dialog).getByText(newEmail)).toBeVisible();
    });

    it('should dismiss banner on X click', async () => {
      await renderAndChallengeEmail();

      const dialog = await waitFor(async () => await screen.findByRole('dialog'));
      expect(dialog).toBeInTheDocument();

      await waitFor(async () => {
        await userEvent.click(screen.getByTestId('otp-dialog-close-button'));
      });

      expect(screen.queryByText(DialogTx.TITLE)).not.toBeInTheDocument();
      expect(screen.queryByText(DialogTx.SUBTITLE)).not.toBeInTheDocument();
      expect(within(dialog).queryByText(newEmail)).not.toBeInTheDocument();
    });

    it('should dismiss banner on background click', async () => {
      await renderAndChallengeEmail();
      const dialog = await waitFor(async () => await screen.findByRole('dialog'));
      expect(dialog).toBeInTheDocument();

      await waitFor(async () => {
        await userEvent.click(dialog.parentElement!);
      });

      expect(screen.queryByText(DialogTx.TITLE)).not.toBeInTheDocument();
      expect(screen.queryByText(DialogTx.SUBTITLE)).not.toBeInTheDocument();
      expect(within(dialog).queryByText(newEmail)).not.toBeInTheDocument();
    });
  });
});
