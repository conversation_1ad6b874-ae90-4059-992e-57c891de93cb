import { screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { VerifyChallengeErrorStatus } from '../../../../networkRequests/responseEntities/CustomerEntities';
import { mockRefreshSession, mockVerifyChallengeCode } from '../../../../../setupTests';
import { renderWithPoetProvidersAndState } from '../../../../testUtils/RenderHelpers';
import { createRefreshSessionClaims } from '../../../../testUtils/MyPodsFactories';
import { EmailChallengeOTPDialog } from '../ChallengeEmailField/EmailChallengeOTPDialog';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { RefreshSessionClaims } from '../../../../networkRequests/responseEntities/AuthorizationEntities';

// -- constants --
const Tx = TranslationKeys.AccountPage.AccountInfo.Email.OtpVerification.Dialog;

describe('EmailChallengeOTPDialog.tsx', () => {
  let sessionClaims: RefreshSessionClaims;
  const customerId = '*********';
  const emailAddress = '<EMAIL>';

  let newEmail = '<EMAIL>';

  beforeEach(() => {
    sessionClaims = createRefreshSessionClaims({ customerId: customerId, username: emailAddress });

    mockRefreshSession.mockResolvedValue(sessionClaims);
  });

  async function renderOTPCodeDialog() {
    const result = renderWithPoetProvidersAndState(
      <EmailChallengeOTPDialog
        isOpen
        newEmail={newEmail}
        onSuccess={vi.fn()}
        onError={vi.fn()}
        onCloseClicked={vi.fn()}
      />
    );

    await waitFor(() => screen.getByText(Tx.TITLE));
    return result;
  }

  it('shows the the title, inputs, and copy of the dialog', async () => {
    await renderOTPCodeDialog();

    expect(screen.getByText(Tx.TITLE)).toBeVisible();
    expect(screen.getByText(Tx.SUBTITLE)).toBeVisible();
    expect(screen.getByText(newEmail)).toBeVisible();
  });

  it('should display helper text and error styles, if challenge code fails', async () => {
    mockVerifyChallengeCode.mockRejectedValue(VerifyChallengeErrorStatus.INVALID_CODE);
    await renderOTPCodeDialog();
    const otpInput = screen.getByTestId('otp-input');
    const inputs = within(otpInput).getAllByRole('textbox');

    await waitFor(async () => {
      await userEvent.type(inputs[0], '0');
      await userEvent.type(inputs[1], '1');
      await userEvent.type(inputs[2], '2');
      await userEvent.type(inputs[3], '3');
    });

    expect(screen.getByText(Tx.SUBTITLE)).toBeVisible();
    expect(screen.getByText(Tx.HELPER_TEXT)).toBeVisible();
  });
});
