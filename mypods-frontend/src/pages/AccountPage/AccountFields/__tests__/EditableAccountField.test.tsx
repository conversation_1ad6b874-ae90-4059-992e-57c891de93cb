import {
  accountPageActions as actions,
  accountPageViews as views
} from '../../__tests__/AccountPageViews';
import { fireEvent, screen, waitFor, within } from '@testing-library/react';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { createCustomer, createPhone } from '../../../../testUtils/MyPodsFactories';
import {
  CustomerContextViewer,
  expectCustomerContextContains,
  getInitialEntries,
  renderWithQueryProvider,
  testQueryClient
} from '../../../../testUtils/RenderHelpers';
import React, { act } from 'react';
import { PrimaryPhoneNumberField } from '../EditableAccountField/PrimaryPhoneNumberField';
import userEvent from '@testing-library/user-event';
import {
  Customer,
  UpdatePrimaryPhoneRequest
} from '../../../../networkRequests/responseEntities/CustomerEntities';
import { NEW_IDENTITY } from '../../../../networkRequests/MyPodsConstants';
import { mockGetCustomer, mockUpdatePrimaryPhone } from '../../../../../setupTests'; // -- mocks ==

describe('Editable Account Field, for the primary phone scenario', () => {
  const customerId = '*********';
  const customer = createCustomer({
    id: customerId,
    primaryPhone: createPhone({ number: '************' })
  });

  async function renderPrimaryPhoneNumber() {
    let result = renderWithQueryProvider(
      <>
        <PrimaryPhoneNumberField />
        <CustomerContextViewer />
      </>,
      testQueryClient(),
      getInitialEntries('editable-account-field', `?customerId=${customerId}`)
    );
    await waitFor(() => views.primaryPhone.baseField());
    await waitFor(() => views.primaryPhone.editButton());
    return result;
  }

  beforeEach(() => {
    mockGetCustomer.mockResolvedValue(customer);
  });

  it('should update primary phone number', async () => {
    let newPhoneNumber = '************';

    await renderPrimaryPhoneNumber();
    await actions.updatePrimaryPhoneNumber(newPhoneNumber);
    await act(async () => {
      await userEvent.click(views.primaryPhone.saveButton());
    });

    const expectedRequest: UpdatePrimaryPhoneRequest = {
      phone: { id: customer.primaryPhone!.id, number: newPhoneNumber }
    };
    expect(views.primaryPhone.accountField()).toBeInTheDocument(); // Restore the display view
    expect(mockUpdatePrimaryPhone).toHaveBeenCalledWith(expectedRequest);
    expect(screen.getByText(newPhoneNumber)).toBeInTheDocument();
    await expectCustomerContextContains(newPhoneNumber);
  });

  it('should update primary phone number, when current phone is null', async () => {
    const newPhoneNumber = '************';
    const customerWithNoPrimaryPhone: Customer = { ...customer, primaryPhone: undefined };
    mockGetCustomer.mockResolvedValue(customerWithNoPrimaryPhone);

    await renderPrimaryPhoneNumber();
    await actions.updatePrimaryPhoneNumber(newPhoneNumber);
    await act(async () => {
      await userEvent.click(views.primaryPhone.saveButton());
    });

    const expectedRequest: UpdatePrimaryPhoneRequest = {
      phone: { id: NEW_IDENTITY, number: newPhoneNumber }
    };
    expect(mockUpdatePrimaryPhone).toHaveBeenCalledWith(expectedRequest);
  });

  it('should notify the user of success', async () => {
    await renderPrimaryPhoneNumber();
    await actions.updatePrimaryPhoneNumber('************');
    await act(async () => {
      await userEvent.click(views.primaryPhone.saveButton());
    });

    expect(
      screen.getByText(
        TranslationKeys.CommonComponents.Notification.PRIMARY_PHONE_NUMBER_SAVE_SUCCEEDED
      )
    ).toBeInTheDocument();
  });

  it('should notify the user when updates fail', async () => {
    let newPhoneNumber = '************';
    mockUpdatePrimaryPhone.mockRejectedValue(new Error());

    await renderPrimaryPhoneNumber();
    await actions.updatePrimaryPhoneNumber(newPhoneNumber);
    await act(async () => {
      await userEvent.click(views.primaryPhone.saveButton());
    });

    expect(
      screen.getByText(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE)
    ).toBeInTheDocument();
  });

  it('should disable save button, customer enters an invalid phone number', async () => {
    let incompletePhoneNumber = '312-555-123';
    await renderPrimaryPhoneNumber();
    await actions.updatePrimaryPhoneNumber(incompletePhoneNumber);
    await act(async () => {
      fireEvent.blur(views.primaryPhone.inputField());
    });

    expect(views.primaryPhone.saveButton()).toBeDisabled();
    expect(
      screen.getByText(TranslationKeys.CommonComponents.Input.Error.INVALID_PHONE)
    ).toBeInTheDocument();
  });

  it('given the phone number has not changed, save button is disabled', async () => {
    await renderPrimaryPhoneNumber();

    await act(async () => {
      await userEvent.click(await views.primaryPhone.editButton());
    });

    await act(async () => {
      await userEvent.clear(views.primaryPhone.inputField());
      await userEvent.type(views.primaryPhone.inputField(), customer.primaryPhone!.number!);
    });
    expect(views.primaryPhone.saveButton()).toBeDisabled();
  });

  it('should restore original value when cancel is clicked after typing', async () => {
    await renderPrimaryPhoneNumber();

    await act(async () => {
      await userEvent.click(await views.primaryPhone.editButton());
    });
    await act(async () => {
      await userEvent.clear(views.primaryPhone.inputField());
      await userEvent.click(views.primaryPhone.cancelButton());
    });

    expect(
      within(views.primaryPhone.accountField()).getByText(customer.primaryPhone!.number!)
    ).toBeInTheDocument();
  });
});
