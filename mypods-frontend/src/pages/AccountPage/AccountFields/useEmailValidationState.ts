import { useTranslation } from 'react-i18next';
import { AxiosError } from 'axios';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { EditableAccountFieldProps } from './EditableAccountField/EditableAccountField';
import { AccountFieldAlertProps } from './AccountFieldAlert';
import { GtmAccountDetailType } from '../../../config/google/GoogleEntities';
import { useGtmEvents } from '../../../config/google/useGtmEvents';
import { useGetCustomer } from '../../../networkRequests/queries/useGetCustomer';
import { useChallengeEmail } from '../../../networkRequests/mutations/useChallengeEmail';
import { useUpdateEmail } from '../../../networkRequests/mutations/useUpdateEmail';
import { ErrorResponse } from '../../../networkRequests/responseEntities/ErrorEntities';
import {
  ChallengeEmailErrorStatus,
  Email,
  UpdateEmailErrorStatus,
  UpdateEmailRequest
} from '../../../networkRequests/responseEntities/CustomerEntities';
import { NEW_IDENTITY } from '../../../networkRequests/MyPodsConstants';
import { isValidEmail } from '../../../helpers/validation/Validators';

const Tx = TranslationKeys.AccountPage.AccountInfo.Email;
const successNotificationKey = TranslationKeys.CommonComponents.Notification.EMAIL_SAVE_SUCCEEDED;
const gtmDetailType: GtmAccountDetailType = 'email';

export interface EmailValidationFieldProps extends EditableAccountFieldProps {
  submitChallenge: (
    email: string,
    onSuccess: () => void,
    onError: (errorHelperText?: string, alertProps?: AccountFieldAlertProps) => void
  ) => void;
}

export const useEmailValidationState = () => {
  const { t: translate } = useTranslation();
  const gtmEvents = useGtmEvents();
  const { customer, updateCustomer } = useGetCustomer();
  const challengeEmail = useChallengeEmail();
  const updateEmail = useUpdateEmail();
  const displayValue = () => customer.email?.address ?? 'N/A';

  // -- Submit Request for 4-digit OTP Code --
  const submitChallenge = (
    email: string,
    onSuccess: () => void,
    onError: (errorTranslationKey?: string, alertKey?: AccountFieldAlertProps) => void
  ) => {
    const challengeRequest = { email };
    gtmEvents.startEditAccountDetail(gtmDetailType);

    challengeEmail.mutate(challengeRequest, {
      onSuccess: () => {
        onSuccess();
      },
      onError: (error: unknown) => {
        if (error instanceof AxiosError && error.response?.data?.status) {
          const errorResponse = error.response.data as ErrorResponse;
          const status = errorResponse.status as ChallengeEmailErrorStatus;
          switch (status) {
            case ChallengeEmailErrorStatus.EMAIL_ALREADY_IN_USE:
            case ChallengeEmailErrorStatus.INVALID_EMAIL: {
              onError(translate(Tx.HelperText[status]), undefined);
              break;
            }
            case ChallengeEmailErrorStatus.ERROR:
              onError(undefined, {
                title: translate(Tx.Notifications.Title[status]),
                message: translate(Tx.Notifications.Message[status])
              });
              break;
            default: {
              onError();
            }
          }
        } else {
          onError();
        }
      }
    });
  };

  // -- Update confirmed email --
  const getUpdateEmailRequest = (updateValue: string): UpdateEmailRequest => {
    const updatedEmail: Email = {
      id: customer.email?.id ?? NEW_IDENTITY,
      address: updateValue
    };
    return { email: updatedEmail };
  };
  const update = (
    value: string,
    onSuccess: () => void,
    onError: (errorTranslationKey?: string, alertKey?: AccountFieldAlertProps) => void
  ) => {
    const updateRequest = getUpdateEmailRequest(value);
    gtmEvents.startEditAccountDetail(gtmDetailType);
    updateEmail.mutate(updateRequest, {
      onSuccess: (_, request) => {
        gtmEvents.successAccountDetail(gtmDetailType);
        onSuccessfulUpdate(request);
        onSuccess();
      },
      onError: (error: unknown) => {
        if (error instanceof AxiosError && error.response?.data?.status) {
          const errorResponse = error.response.data as ErrorResponse;
          const status = errorResponse.status as UpdateEmailErrorStatus;
          switch (status) {
            case UpdateEmailErrorStatus.EMAIL_ALREADY_IN_USE:
            case UpdateEmailErrorStatus.INVALID_EMAIL: {
              onError(translate(Tx.HelperText[status]), undefined);
              break;
            }

            case UpdateEmailErrorStatus.ACCOUNT_UNDER_MAINTENANCE:
            case UpdateEmailErrorStatus.ERROR:
            case UpdateEmailErrorStatus.NO_ACCOUNT_FOUND:
            case UpdateEmailErrorStatus.TOKEN_EXPIRED:
            case UpdateEmailErrorStatus.TOKEN_INVALID: {
              onError(undefined, {
                title: translate(Tx.Notifications.Title[status]),
                message: translate(Tx.Notifications.Message[status])
              });
              break;
            }
            default: {
              // The Page-Level alert is rendered on the EditableAccountField
              onError();
            }
          }
        } else {
          onError();
        }
      }
    });
  };

  const onSuccessfulUpdate = (request: UpdateEmailRequest): void => {
    if (request.email != null)
      updateCustomer({ email: request.email, username: request.email.address });
  };

  return {
    fieldProps: {
      gtmDetailType,
      displayValue: displayValue(),
      isInputValid: isValidEmail,
      submitChallenge,
      challengeEmail,
      update,
      isPendingUpdate: updateEmail.isPending || challengeEmail.isPending,
      labelKey: TranslationKeys.AccountPage.AccountInfo.Email.LABEL,
      validationErrorKey: TranslationKeys.CommonComponents.Input.Error.INVALID_EMAIL,
      successNotificationKey,
      textFieldOverrides: {
        type: 'email'
      }
    }
  };
};
