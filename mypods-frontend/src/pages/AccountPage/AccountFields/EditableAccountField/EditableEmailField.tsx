import React from 'react';
import { Grid } from '@mui/material';
import { EditableAccountField } from './EditableAccountField';
import { useEmailState } from '../useEmailState';

export const EditableEmailField = () => {
  const { editableFieldProps } = useEmailState();

  return (
    <Grid data-testid="editable-email-field">
      <EditableAccountField {...editableFieldProps} />
    </Grid>
  );
};
