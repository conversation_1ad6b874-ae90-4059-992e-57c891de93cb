import React from 'react';
import { Grid } from '@mui/material';
import { Design } from '../../../../helpers/Design';
import { ShowHideButton } from '../../../../components/buttons/ShowHideButton';
import { OccludedSecurityField } from '../PinField/OccludedSecurityField';
import { SecurityFieldLabel } from './SecurityFieldLabel';

export const SecurityQuestionAnswerField = (props: {
  questionLabel: string;
  answerLabel: string;
  questionValue: string;
  answerValue: string;
}) => {
  const [showQuestionAnswer, setShowQuestionAnswer] = React.useState(false);
  const styles = securityQAStyles();

  return (
    <Grid container data-testid="account-field" {...styles.wrapper}>
      <Grid item container flexDirection="row">
        <SecurityFieldLabel label={props.questionLabel} />
        <Grid item flexGrow={0}>
          <ShowHideButton
            onClick={() => setShowQuestionAnswer(!showQuestionAnswer)}
            dataVisible={showQuestionAnswer}
          />
        </Grid>
      </Grid>
      <OccludedSecurityField value={props.questionValue} isVisible={showQuestionAnswer} />

      <SecurityFieldLabel label={props.answerLabel} />
      <OccludedSecurityField value={props.answerValue} isVisible={showQuestionAnswer} />
    </Grid>
  );
};

const securityQAStyles = () => ({
  wrapper: {
    sx: {
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs,
      paddingY: Design.Primitives.Spacing.sm,
      borderBottom: `1px solid ${Design.Alias.Color.neutral300}`
    }
  }
});
