import { useTranslation } from 'react-i18next';
import React from 'react';
import { Grid } from '@mui/material';
import { AccountField } from './AccountField';
import { EMAIL_VERIFICATION_ENABLED, useFeatureFlags } from '../../../helpers/useFeatureFlags';
import { customerEmailOutOfSync } from '../../../networkRequests/responseEntities/CustomerEntities';
import { useGetCustomer } from '../../../networkRequests/queries/useGetCustomer';
import { OutOfSyncEmailField } from './OutOfSyncEmailField/OutOfSyncEmailField';
import { EditableOTPEmailField } from './ChallengeEmailField/EditableOTPEmailField';
import { useEmailValidationState } from './useEmailValidationState';

export const EmailField = () => {
  const { t: translate } = useTranslation();
  const { fieldProps } = useEmailValidationState();
  const { isEmailVerificationEnabled } = useFeatureFlags([EMAIL_VERIFICATION_ENABLED]);
  const { customer } = useGetCustomer();

  if (isEmailVerificationEnabled()) {
    return customerEmailOutOfSync(customer) ? (
      <OutOfSyncEmailField />
    ) : (
      <Grid data-testid="otp-email-field">
        <EditableOTPEmailField />
      </Grid>
    );
  }

  return (
    <Grid data-testid="email-field">
      <AccountField label={translate(fieldProps.labelKey)} value={fieldProps.displayValue} />
    </Grid>
  );
};
