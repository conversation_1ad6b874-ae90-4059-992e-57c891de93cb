import React from 'react';
import { useTranslation } from 'react-i18next';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import {
  DashIcon,
  GreenCheckmarkIcon,
  RedXIcon
} from '../../../../components/icons/GreenCheckmarkIcon';
import { byValidity, Validity } from '../../../../helpers/validation/Validators';
import { Design } from '../../../../helpers/Design';

interface Props {
  i18nKey: string;
  validity: Validity;
}

export const PasswordValidationItem: React.FC<Props> = ({ i18nKey, validity }: Props) => {
  const { t: translate } = useTranslation();
  const styles = passwordValidationStyles(validity);
  return (
    <ListItem disablePadding>
      {' '}
      <ListItemIcon sx={{ minWidth: 'fit-content', marginRight: Design.Primitives.Spacing.xxs }}>
        {byValidity(validity, {
          valid: (
            <GreenCheckmarkIcon
              {...styles.listIconProps}
              color="success"
              data-testid="password-rule-success"
            />
          ),
          invalid: (
            <RedXIcon {...styles.listIconProps} color="error" data-testid="password-rule-error" />
          ),
          default: <DashIcon {...styles.listIconProps} data-testid="password-rule-default" />
        })}
      </ListItemIcon>
      <ListItemText
        {...styles.listItemText}
        primary={translate(i18nKey)}
        primaryTypographyProps={{ ...Design.Alias.Text.BodyUniversal.Xs }}
      />
    </ListItem>
  );
};

const passwordValidationStyles = (validity: Validity) => ({
  listItemText: {
    sx: {
      marginTop: 0,
      '&, & *': {
        color: byValidity(validity, {
          valid: Design.Alias.Color.accent900,
          invalid: Design.Alias.Color.primary300,
          default: Design.Alias.Color.accent900
        })
      }
    }
  },
  listIconProps: {
    sx: { height: '16px', width: '16px' }
  }
});
