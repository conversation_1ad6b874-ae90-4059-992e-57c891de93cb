import React, { ReactNode, useContext, useState } from 'react';
import { Grid } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { AxiosError } from 'axios';
import { Design } from '../../../../helpers/Design';
import { CancelButton } from '../../../../components/buttons/CancelButton';
import { SaveButton } from '../../../../components/buttons/SaveButton';
import { AccountField } from '../AccountField';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { useUpdatePassword } from '../../../../networkRequests/mutations/useUpdatePassword';
import {
  Email,
  UpdatePasswordResponseCode
} from '../../../../networkRequests/responseEntities/CustomerEntities';
import { useValidatePassword } from '../../../../helpers/validation/Validators';
import { PasswordTextField } from './PasswordTextField';
import { NewPasswordHelperText } from './NewPasswordHelperText';
import { NewPasswordTypeaheadValidations } from './NewPasswordTypeaheadValidations';
import { NotificationContext } from '../../../../components/notifications/NotificationContext';
import { AccountFieldAlert, AccountFieldAlertProps } from '../AccountFieldAlert';
import { ErrorResponse } from '../../../../networkRequests/responseEntities/ErrorEntities';
import { useGtmEvents } from '../../../../config/google/useGtmEvents';
import { GtmAccountDetailType } from '../../../../config/google/GoogleEntities';
import {
  createGtmErrorRequest,
  GA_GENERIC_BACKEND_MESSAGE
} from '../../../../config/google/googleAnalyticsUtils';

const Tx = TranslationKeys.AccountPage.AccountInfo.Password;

export type EditablePasswordFieldProps = {
  displayValue: string;
  customerEmail: Email;
};

export const EditablePasswordField: React.FC<EditablePasswordFieldProps> = ({
  displayValue,
  customerEmail
}) => {
  // -- state --
  const { setNotification } = useContext(NotificationContext);
  const gtmEvents = useGtmEvents();
  const detailType: GtmAccountDetailType = 'password';
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [newPassword, setNewPassword] = useState<string>('');
  const [currentPassword, setCurrentPassword] = useState<string>('');
  const [inputGroupAlert, setInputGroupAlert] = useState<AccountFieldAlertProps | undefined>();
  const [newPasswordHelperText, setNewPasswordHelperText] = useState<ReactNode | undefined>();
  const [currentPasswordHelperText, setCurrentPasswordHelperText] = useState<string | undefined>();

  // -- custom hooks --
  const { rules, isValid } = useValidatePassword(newPassword);
  const { t: translate } = useTranslation();
  const updatePassword = useUpdatePassword();

  const styles = editablePasswordFieldStyles();

  const setDefaultErrorNotification = () => {
    setNotification({
      message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
      isError: true
    });
  };

  const setPasswordNotSavedNotification = () => {
    setNotification({
      message: translate(TranslationKeys.CommonComponents.Notification.PASSWORD_NOT_SAVED),
      isError: true
    });
  };

  // -- Event Handlers --
  const handleEditClick = () => {
    gtmEvents.startEditAccountDetail(detailType);
    setIsEditing(true);
  };

  function handleCancel() {
    setIsEditing(false);

    setNewPassword('');
    setNewPasswordHelperText(undefined);
    setCurrentPassword('');
    setCurrentPasswordHelperText(undefined);
    setInputGroupAlert(undefined);
  }

  const handleCurrentPasswordChange = (value: string) => {
    setCurrentPassword(value);
    setCurrentPasswordHelperText(undefined);
    setInputGroupAlert(undefined);
  };

  const handleNewPasswordChange = (value: string) => {
    setNewPassword(value);
    setNewPasswordHelperText(undefined);
    setInputGroupAlert(undefined);
  };

  const handleSaveButton = () => {
    setNewPasswordHelperText(undefined);
    setCurrentPasswordHelperText(undefined);
    setInputGroupAlert(undefined);
    if (!isValid) {
      setNewPasswordHelperText(<NewPasswordHelperText rules={rules} />);
      return;
    }

    // -- API --
    const onMutationSuccess = () => {
      setIsEditing(false);
      setNewPassword('');
      setNotification({
        message: translate(
          TranslationKeys.CommonComponents.Notification.NEW_PASSWORD_SAVE_SUCCEEDED
        ),
        isError: false
      });
    };
    const onMutationError = (error: unknown) => {
      if (error instanceof AxiosError && error.response?.data?.status) {
        const errorResponse = error.response.data as ErrorResponse;
        const status = errorResponse.status as UpdatePasswordResponseCode;
        switch (status) {
          case UpdatePasswordResponseCode.INVALID_CREDENTIALS: {
            setCurrentPasswordHelperText(translate(Tx.HelperText.INVALID_CREDENTIALS));
            setPasswordNotSavedNotification();
            return;
          }
          // Show New Password Helper Text
          case UpdatePasswordResponseCode.NEW_PASSWORD_IS_OLD_PASSWORD:
          case UpdatePasswordResponseCode.NEW_PASSWORD_IS_COMMON:
          case UpdatePasswordResponseCode.NEW_PASSWORD_IS_REUSED:
          case UpdatePasswordResponseCode.PASSWORD_FAILED_REQUIREMENTS:
          case UpdatePasswordResponseCode.NEW_PASSWORD_FAILED_REQUIREMENTS: {
            setNewPasswordHelperText(translate(Tx.HelperText[status]));
            setPasswordNotSavedNotification();
            return;
          }
          // Display alert instead.
          case UpdatePasswordResponseCode.ACCOUNT_NOT_CONVERTED:
          case UpdatePasswordResponseCode.NO_ACCOUNT_FOUND: {
            setInputGroupAlert({
              title: translate(Tx.Notifications.Title[status]),
              message: translate(Tx.Notifications.Message[status])
            });
            return;
          }
          default: {
            setDefaultErrorNotification();
          }
        }
      } else {
        setDefaultErrorNotification();
      }
    };
    const updatePasswordRequest = {
      oldPassword: currentPassword,
      newPassword,
      email: customerEmail
    };

    gtmEvents.submitAccountDetail(detailType);
    updatePassword.mutate(updatePasswordRequest, {
      onSuccess: () => {
        gtmEvents.successAccountDetail(detailType);
        onMutationSuccess();
      },
      onError: (error: unknown) => {
        gtmEvents.errorEvent(
          createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, detailType, 'backend')
        );
        onMutationError(error);
      }
    });
  };

  function isDisabled() {
    return newPassword.length < 1 || currentPassword.length < 1;
  }

  return (
    <Grid data-testid="password-view">
      {!isEditing ? (
        <AccountField
          label={translate(Tx.Labels.VIEW_PASSWORD)}
          value={displayValue}
          onEditClick={handleEditClick}
        />
      ) : (
        <Grid container {...styles.editablePasswordField} data-testid="edit-password-view">
          {inputGroupAlert && (
            <AccountFieldAlert title={inputGroupAlert.title} message={inputGroupAlert.message} />
          )}
          <Grid container {...styles.textFieldContainer}>
            <PasswordTextField
              label={Tx.Labels.CURRENT_PASSWORD}
              hasAutoFocus
              onChange={handleCurrentPasswordChange}
              helperText={currentPasswordHelperText}
            />
            <PasswordTextField
              label={Tx.Labels.NEW_PASSWORD}
              hasAutoFocus={false}
              onChange={handleNewPasswordChange}
              helperText={newPasswordHelperText}
            />
          </Grid>
          <NewPasswordTypeaheadValidations rules={rules} />
          <Grid container {...styles.actionButtonContainer}>
            <CancelButton onClick={() => handleCancel()} disabled={updatePassword.isPending} />
            <SaveButton
              onClick={handleSaveButton}
              isLoading={updatePassword.isPending}
              disabled={isDisabled()}
            />
          </Grid>
        </Grid>
      )}
    </Grid>
  );
};

const editablePasswordFieldStyles = () => ({
  editablePasswordField: {
    sx: {
      paddingY: Design.Primitives.Spacing.sm,
      borderBottom: `1px solid ${Design.Alias.Color.neutral300}`,
      gap: Design.Primitives.Spacing.xxs,
      flexDirection: 'column'
    }
  },
  textFieldContainer: {
    sx: {
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  actionButtonContainer: {
    sx: {
      marginTop: Design.Primitives.Spacing.xxs,
      columnGap: Design.Primitives.Spacing.xxs,
      justifyContent: 'flex-end'
    }
  }
});
