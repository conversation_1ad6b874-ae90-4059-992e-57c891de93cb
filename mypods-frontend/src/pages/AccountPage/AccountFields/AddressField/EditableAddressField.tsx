import React, { useContext, useEffect, useState } from 'react';
import { Grid } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../helpers/Design';
import { CancelButton } from '../../../../components/buttons/CancelButton';
import { SaveButton } from '../../../../components/buttons/SaveButton';
import { AccountField } from '../AccountField';
import {
  areAddressEqual,
  CustomerAddress,
  formatAddress
} from '../../../../networkRequests/responseEntities/CustomerEntities';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { useAddressFieldState } from './useAddressFieldState';
import { NotificationContext } from '../../../../components/notifications/NotificationContext';
import { CustomerAddressForm } from './CustomerAddressForm';
import { GtmAccountDetailType } from '../../../../config/google/GoogleEntities';
import { useGtmEvents } from '../../../../config/google/useGtmEvents';

export interface EditableAddressFieldProps {
  currentAddress?: CustomerAddress;
  // InitializedAddress has some values setup that a user does not modify,
  // but must be present on update
  initializedAddress: CustomerAddress;
  update: (value: CustomerAddress, onSuccess: () => void, onError: () => void) => void;
  isPendingUpdate: boolean;
  labelKey: string;
  gtmDetailType: GtmAccountDetailType;
  successNotificationKey: string;
}

export const EditableAddressField: React.FC<EditableAddressFieldProps> = ({
  currentAddress,
  initializedAddress,
  update,
  isPendingUpdate,
  labelKey,
  gtmDetailType,
  successNotificationKey
}) => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const gtmEvents = useGtmEvents();
  const addressFieldState = useAddressFieldState(initializedAddress);
  const { value, setValue, ...state } = addressFieldState;
  const displayValue = formatAddress(currentAddress);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const label = translate(labelKey);

  useEffect(() => {
    state.clearErrors();
  }, [isEditing]);

  const inputIsInvalidOrUnchanged = () => {
    if (areAddressEqual(value, initializedAddress)) return true;
    return !state.isValid();
  };

  const handleCancel = () => {
    setValue(initializedAddress);
    setIsEditing(false);
  };

  const handleEditClick = () => {
    gtmEvents.startEditAccountDetail(gtmDetailType);
    setIsEditing(true);
  };

  const handleSave = () => {
    state.displayErrorForAllFields();
    if (inputIsInvalidOrUnchanged()) return;

    update(
      value,
      () => {
        setNotification({
          message: translate(successNotificationKey)
        });
        setIsEditing(false);
      },
      () => {
        setNotification({
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
          isError: true
        });
      }
    );
  };

  return (
    <Grid data-testid="editable-account-field">
      {!isEditing ? (
        <AccountField label={label} value={displayValue} onEditClick={handleEditClick} />
      ) : (
        <Grid
          container
          flexDirection="column"
          gap={Design.Primitives.Spacing.xxs}
          sx={{
            paddingY: Design.Primitives.Spacing.sm,
            borderBottom: `1px solid ${Design.Alias.Color.neutral300}`
          }}>
          <CustomerAddressForm label={label} state={addressFieldState} />
          <Grid
            sx={{ marginTop: Design.Primitives.Spacing.xxs }}
            container
            columnGap={Design.Primitives.Spacing.xxs}
            justifyContent="flex-end">
            <CancelButton onClick={handleCancel} disabled={isPendingUpdate} />
            <SaveButton
              onClick={handleSave}
              isLoading={isPendingUpdate}
              disabled={inputIsInvalidOrUnchanged()}
            />
          </Grid>
        </Grid>
      )}
    </Grid>
  );
};
