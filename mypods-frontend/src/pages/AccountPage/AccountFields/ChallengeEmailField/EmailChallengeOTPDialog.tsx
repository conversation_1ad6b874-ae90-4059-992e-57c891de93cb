import React, { useState } from 'react';
import { CircularProgress, Dialog, Grid, IconButton, Typography } from '@mui/material';
import { MuiOtpInput } from 'mui-one-time-password-input';
import CloseIcon from '@mui/icons-material/Close';
import { useTranslation } from 'react-i18next';
import { theme } from '../../../../PodsTheme';
import { Design } from '../../../../helpers/Design';
import { MailIcon } from '../../../../components/icons/MailIcon';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { useVerifyChallenge } from '../../../../networkRequests/mutations/useVerifyChallenge';
import { isTextNumeric } from '../../../../helpers/validation/Validators';
import { VerifyChallengeRequest } from '../../../../networkRequests/responseEntities/CustomerEntities';
import { ResendCountdownButton } from './ResendCountdownButton';
import { Txt } from '../../../../components/Txt';

// -- types --
interface Props {
  isOpen: boolean;
  onCloseClicked: () => void;
  newEmail: string;
  onSuccess: () => void;
  onError: () => void;
}

// -- constants --
const Tx = TranslationKeys.AccountPage.AccountInfo.Email.OtpVerification.Dialog;
// -- impls --
export const EmailChallengeOTPDialog: React.FC<Props> = ({
  isOpen,
  onCloseClicked,
  newEmail,
  onSuccess,
  onError
}: Props) => {
  const verifyChallenge = useVerifyChallenge();
  const { t: translate } = useTranslation();
  const [code, setCode] = useState('');
  const [hasError, setHasError] = useState(false);
  const [displaySpinner, setDisplaySpinner] = useState(false);

  const validateChar = (value: string) => isTextNumeric(value);

  const handleOnChange = (newValue: string) => {
    setHasError(false);
    setCode(newValue);
    if (newValue.length === 4) {
      handleSubmit(newValue);
    }
  };

  const handleSubmit = (value: string) => {
    const request: VerifyChallengeRequest = { oneTimePassword: value };
    setDisplaySpinner(true);
    verifyChallenge.mutate(request, {
      onSuccess: async () => {
        onSuccess();
      },
      onError: () => {
        onError();
        setCode('');
        setHasError(true);
        setDisplaySpinner(false);
      }
    });
  };

  return (
    <Dialog open={isOpen} onClose={onCloseClicked} {...styles.dialog}>
      <Grid container {...styles.contentGrid}>
        <IconButton
          {...styles.closeButton}
          onClick={onCloseClicked}
          data-testid="otp-dialog-close-button">
          <CloseIcon />
        </IconButton>
        <MailIcon />
        <Typography {...styles.title}>{translate(Tx.TITLE)}</Typography>
        <Grid item>
          <Typography {...styles.subtitle}>{translate(Tx.SUBTITLE)}</Typography>
          <Typography {...styles.email}>{newEmail}</Typography>
        </Grid>
        <Grid container {...styles.multiField}>
          {!displaySpinner ? (
            <Grid item {...styles.multiField}>
              <MuiOtpInput
                data-testid="otp-input"
                autoFocus
                length={4}
                value={code}
                onChange={handleOnChange}
                validateChar={validateChar}
                gap="10px"
                TextFieldsProps={{
                  ...styles.inputField,
                  color: 'secondary',
                  error: hasError,
                  // this empty placeholder is needed to style completed fields
                  placeholder: ' ',
                  inputProps: { type: 'tel' }
                }}
              />
              {hasError && (
                <Txt
                  i18nKey={Tx.HELPER_TEXT}
                  variant="caption"
                  color="error"
                  {...styles.helperText}
                />
              )}
            </Grid>
          ) : (
            <Grid container>
              <Grid container {...styles.spinnerContainer} data-testid="loading-spinner">
                <CircularProgress color="secondary" />
              </Grid>
            </Grid>
          )}
        </Grid>
        <ResendCountdownButton newUsername={newEmail} />
      </Grid>
    </Dialog>
  );
};

// -- styles --
const styles = {
  dialog: {
    sx: {
      '.MuiDialog-paper': {
        display: 'flex',
        width: '375px',
        paddingX: Design.Primitives.Spacing.sm,
        paddingY: '20px',
        flexDirection: 'column',
        alignItems: 'flexEnd',
        gap: '1px',
        [theme.breakpoints.down('md')]: {
          marginX: Design.Primitives.Spacing.xs,
          paddingX: '14px'
        }
      }
    }
  },
  contentGrid: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      paddingY: Design.Primitives.Spacing.xs,
      paddingX: Design.Primitives.Spacing.xs,
      gap: '20px'
    }
  },
  closeButton: {
    sx: {
      position: 'absolute',
      top: 0,
      right: 0
    }
  },
  title: {
    sx: {
      ...Design.Alias.Text.Heading.Mobile.Xl,
      color: Design.Alias.Color.accent900
    }
  },
  subtitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: Design.Primitives.Color.NeutralDark.charcoal,
      textAlign: 'center'
    }
  },
  email: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.SmBold,
      color: Design.Primitives.Color.NeutralDark.charcoal,
      textAlign: 'center'
    }
  },
  multiField: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      wrap: 'none'
    }
  },
  helperText: {
    sx: {
      textAlign: 'center'
    }
  },
  spinnerContainer: {
    sx: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      paddingTop: Design.Primitives.Spacing.lg,
      paddingBottom: Design.Primitives.Spacing.lgPlus,
      backgroundColor: Design.Primitives.Color.Bkgd
    }
  },
  inputField: {
    sx: {
      width: '3.75rem',
      height: '3.75rem',
      ...Design.Alias.Text.Heading.Desktop.Xxl,
      textAlign: 'center',
      // default
      '.MuiInputBase-root .MuiOutlinedInput-notchedOutline': {
        borderColor: Design.Alias.Color.neutral700
      },
      // default with value
      '.MuiInputBase-root input:not(:placeholder-shown)~.MuiOutlinedInput-notchedOutline ': {
        borderColor: Design.Alias.Color.secondary500
      },
      // focused
      '.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: Design.Alias.Color.secondary500,
        borderWidth: '2px'
      },
      // on hover
      '.MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: Design.Alias.Color.secondary400
      },
      // with error
      '.Mui-error .MuiOutlinedInput-notchedOutline': {
        borderColor: Design.Primitives.Color.Semantic.error
      },
      // with error + hover
      '.Mui-error.MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: Design.Primitives.Color.Semantic.error
      }
    }
  }
};
