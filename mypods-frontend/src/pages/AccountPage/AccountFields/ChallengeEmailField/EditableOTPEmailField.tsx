import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, TextField } from '@mui/material';
import { EmailChallengeOTPDialog } from './EmailChallengeOTPDialog';
import { AccountField } from '../AccountField';
import { Design } from '../../../../helpers/Design';
import { Txt } from '../../../../components/Txt';
import { AccountFieldAlert, AccountFieldAlertProps } from '../AccountFieldAlert';
import { CancelButton } from '../../../../components/buttons/CancelButton';
import { SaveButton } from '../../../../components/buttons/SaveButton';
import { NotificationContext } from '../../../../components/notifications/NotificationContext';
import { useGtmEvents } from '../../../../config/google/useGtmEvents';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { useRefreshSession } from '../../../../networkRequests/queries/useRefreshSession';
import { useGetCustomer } from '../../../../networkRequests/queries/useGetCustomer';
import { isValidEmail } from '../../../../helpers/validation/Validators';
import { GtmAccountDetailType } from '../../../../config/google/GoogleEntities';
import { useEmailValidationState } from '../useEmailValidationState';

const Tx = TranslationKeys.AccountPage.AccountInfo.Email;
const validationErrorKey = TranslationKeys.CommonComponents.Input.Error.INVALID_EMAIL;
const successNotificationKey = TranslationKeys.CommonComponents.Notification.EMAIL_SAVE_SUCCEEDED;
const submitButtonTranslationKey = TranslationKeys.CommonComponents.CONTINUE_BUTTON;
const gtmDetailType: GtmAccountDetailType = 'email';
const textFieldOverrides = { type: 'email' };

export const EditableOTPEmailField = () => {
  const { fieldProps } = useEmailValidationState();
  const { challengeEmail } = fieldProps;
  const { refetch: refetchSession } = useRefreshSession();
  const { customer, updateCustomer } = useGetCustomer();
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const gtmEvents = useGtmEvents();

  const displayValue = customer.email?.address ?? 'N/A';
  const [inputGroupAlert, setInputGroupAlert] = useState<AccountFieldAlertProps | undefined>();
  const [value, setValue] = useState<string>(displayValue);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [error, setError] = useState<string | undefined>();
  const label = translate(Tx.LABEL);

  useEffect(() => {
    setError(undefined);
  }, [isEditing]);

  // -- Email Validation  --
  const inputIsInvalidOrUnchanged = () => {
    if (value === displayValue) return true;
    return !isValidEmail(value);
  };

  const setErrorIfInvalid = () => {
    if (!isValidEmail(value)) {
      setError(translate(validationErrorKey));
    } else {
      setError(undefined);
    }
  };

  const handleValueChange = (newValue: string) => {
    setValue(newValue);
    setError(undefined);
    setInputGroupAlert(undefined);
  };

  const handleCancel = () => {
    setValue(displayValue);
    setIsEditing(false);
    setError(undefined);
    setInputGroupAlert(undefined);
  };

  const handleEditClick = () => {
    gtmEvents.startEditAccountDetail(gtmDetailType);
    setIsEditing(true);
  };

  const handleServerError = (errorHelperText: string) => {
    setError(errorHelperText);
  };
  const handleServerAlert = (alertKey: AccountFieldAlertProps) => {
    setInputGroupAlert({
      title: alertKey.title,
      message: alertKey.message
    });
  };

  const handleEmailChallengeRequest = () => {
    setErrorIfInvalid();
    if (inputIsInvalidOrUnchanged()) return;

    fieldProps.submitChallenge(value, onEmailChallengeSuccess, onEmailChallengeError);
  };
  const onEmailChallengeSuccess = () => {
    setIsModalOpen(true);
  };

  const onEmailChallengeError = (errorHelperText?: string, alertProps?: AccountFieldAlertProps) => {
    if (errorHelperText) handleServerError(errorHelperText);
    if (alertProps) handleServerAlert(alertProps);
    if (!alertProps && !errorHelperText) {
      setNotification({
        message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
        isError: true
      });
    }
  };

  // -- OTP Code Verification --
  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleEmailVerificationSuccess = async () => {
    setIsModalOpen(false);
    setIsEditing(false);
    setNotification({
      message: translate(successNotificationKey)
    });
    if (value != null)
      updateCustomer({
        email: { id: 0, address: value },
        username: value
      });
    gtmEvents.successAccountDetail(gtmDetailType);
    await refetchSession();
  };

  const handleEmailVerificationError = () => {
    setNotification({
      message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
      isError: true
    });
  };

  return (
    <>
      <Grid data-testid="editable-otp-email-field">
        <Grid data-testid="editable-account-field">
          {!isEditing ? (
            <AccountField label={label} value={displayValue} onEditClick={handleEditClick} />
          ) : (
            <Grid
              container
              flexDirection="column"
              gap={Design.Primitives.Spacing.xxs}
              sx={{
                paddingY: Design.Primitives.Spacing.sm,
                borderBottom: `1px solid ${Design.Alias.Color.neutral300}`
              }}>
              <Grid container flexDirection="column" gap={Design.Primitives.Spacing.sm}>
                <Grid container flexDirection="row" justifyContent="space-between">
                  <Txt
                    style={Design.Alias.Text.BodyUniversal.Md}
                    sx={{ color: Design.Alias.Color.neutral700 }}>
                    {label}
                  </Txt>
                </Grid>
                {inputGroupAlert && (
                  <AccountFieldAlert
                    title={inputGroupAlert.title}
                    message={inputGroupAlert.message}
                  />
                )}
                <TextField
                  autoFocus
                  fullWidth
                  color="secondary"
                  inputRef={(input) => input && input.focus()}
                  label={label}
                  value={value}
                  onBlur={setErrorIfInvalid}
                  helperText={error}
                  error={!!error}
                  onChange={(event) => handleValueChange(event.target.value)}
                  onKeyDown={(event) => event.key === 'Enter' && handleEmailChallengeRequest()}
                  {...textFieldOverrides}
                />
              </Grid>

              <Grid
                sx={{ marginTop: Design.Primitives.Spacing.xxs }}
                container
                columnGap={Design.Primitives.Spacing.xxs}
                justifyContent="flex-end">
                <CancelButton onClick={handleCancel} disabled={challengeEmail.isPending} />
                <SaveButton
                  onClick={handleEmailChallengeRequest}
                  isLoading={challengeEmail.isPending}
                  label={submitButtonTranslationKey}
                  disabled={inputIsInvalidOrUnchanged()}
                />
              </Grid>
            </Grid>
          )}
        </Grid>
      </Grid>
      {isModalOpen && (
        <EmailChallengeOTPDialog
          isOpen={isModalOpen}
          onCloseClicked={handleModalClose}
          newEmail={value}
          onSuccess={handleEmailVerificationSuccess}
          onError={handleEmailVerificationError}
          data-testid="otp-challenge-dialog"
        />
      )}
    </>
  );
};
