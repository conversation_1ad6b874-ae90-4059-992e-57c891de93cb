import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Grid, Typography } from '@mui/material';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { useChallengeEmail } from '../../../../networkRequests/mutations/useChallengeEmail';

// -- types --
type Props = {
  newUsername: string;
  resendCountdownSeconds?: number;
};

export const RESEND_COUNTDOWN_SECONDS = 60;
const Tx = TranslationKeys.AccountPage.AccountInfo.Email.OtpVerification.Dialog;

// -- impls --
export const ResendCountdownButton: React.FC<Props> = ({
  newUsername,
  resendCountdownSeconds
}: Props) => {
  const { t: translate } = useTranslation();
  const challengeEmail = useChallengeEmail();
  const [timeRemaining, setTimeRemaining] = useState<number>(
    resendCountdownSeconds ?? RESEND_COUNTDOWN_SECONDS
  );

  useEffect(() => {
    const timerId = setInterval(() => setTimeRemaining(timeRemaining - 1), 1000);
    return () => clearInterval(timerId);
  });

  const requestEmailChallenge = () => {
    const challengeRequest = { email: newUsername };

    challengeEmail.mutate(challengeRequest, {
      onSuccess: () => {
        setTimeRemaining(RESEND_COUNTDOWN_SECONDS);
      },
      onError: (responseError: unknown) => {
        console.log(responseError);
      }
    });
  };

  return (
    <Grid container item {...styles.countdown}>
      <Typography {...styles.countdownText}>{translate(Tx.MISSING_CODE)}</Typography>
      {timeRemaining > 0 ? (
        <Typography {...styles.countdownText}>
          {translate(Tx.RESEND_CODE_COUNTDOWN, { amount: timeRemaining })}
        </Typography>
      ) : (
        <Button
          variant="text"
          color="secondary"
          {...styles.countdownButton}
          disabled={challengeEmail.isPending}
          onClick={requestEmailChallenge}>
          {translate(Tx.RESEND_CODE_BUTTON)}
        </Button>
      )}
    </Grid>
  );
};

// -- styles --
const styles = {
  countdown: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      gap: Design.Primitives.Spacing.xxxs
    }
  },
  countdownText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Alias.Color.neutral600
    }
  },
  countdownButton: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.XsBold,
      paddingTop: 'inherit',
      paddingLeft: 'inherit',
      '.MuiTouchRipple-root': {
        paddingTop: 'inherit',
        paddingLeft: 'inherit'
      }
    }
  }
};
