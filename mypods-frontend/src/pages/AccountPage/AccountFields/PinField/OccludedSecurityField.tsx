import React from 'react';
import { Grid } from '@mui/material';
import { Design } from '../../../../helpers/Design';
import { Txt } from '../../../../components/Txt';

export const OccludedSecurityField = (props: { value: string; isVisible: boolean }) => {
  const SECURITY_PLACEHOLDER: string = '************************';

  return (
    <Grid item>
      <Txt
        style={Design.Alias.Text.BodyUniversal.Lg}
        sx={{ color: Design.Alias.Color.accent900, overflowWrap: 'anywhere' }}>
        {props.isVisible ? props.value : SECURITY_PLACEHOLDER}
      </Txt>
    </Grid>
  );
};
