import React from 'react';
import { AxiosError, AxiosResponse } from 'axios';
import { QueryClient } from '@tanstack/react-query';
import { screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import {
  createCustomer,
  createEmail,
  createRefreshSessionClaims
} from '../../../../../testUtils/MyPodsFactories';
import { renderWithQueryProvider, testQueryClient } from '../../../../../testUtils/RenderHelpers';
import {
  mockChallengeEmail,
  mockGetCustomer,
  mockRefreshSession,
  mockUpdateEmail
} from '../../../../../../setupTests';
import {
  accountPageActions as actions,
  accountPageViews as views
} from '../../../__tests__/AccountPageViews';
import {
  ChallengeEmailRequest,
  UpdateEmailErrorStatus,
  UpdateEmailRequest
} from '../../../../../networkRequests/responseEntities/CustomerEntities';
import { expectNotificationAlertContainsTitle } from '../../../../../testUtils/assertions';
import { OutOfSyncEmailField } from '../OutOfSyncEmailField';

const Tx = TranslationKeys.AccountPage.AccountInfo.Email;
const DialogTx = TranslationKeys.AccountPage.AccountInfo.Email.OtpVerification.Dialog;

describe('OutOfSyncEmailField', () => {
  let queryClient: QueryClient;
  const customerId = '*********';
  const customer = createCustomer({
    id: customerId,
    email: createEmail({ id: 42, address: '<EMAIL>' }),
    username: '<EMAIL>'
  });
  let newEmail = '<EMAIL>';

  beforeEach(() => {
    queryClient = testQueryClient();
    mockGetCustomer.mockResolvedValue(customer);
    mockRefreshSession.mockResolvedValue(
      createRefreshSessionClaims({ customerId: customerId, username: customer.email?.address! })
    );
  });

  async function renderEmailField() {
    let result = renderWithQueryProvider(<OutOfSyncEmailField />, queryClient);
    await waitFor(() => views.outOfSyncEmail.editButton());
    return result;
  }

  async function renderAndUpdateEmail() {
    await renderEmailField();
    await actions.editOutOfSyncEmail();
    await actions.saveOutOfSyncEmail();
  }

  it('should show radio buttons with the okta username selected by default', async () => {
    await renderEmailField();
    await actions.editOutOfSyncEmail();

    expect(screen.getByText(Tx.OutOfSync.LABEL));
    expect(screen.getByLabelText(Tx.OutOfSync.OTHER));
    expect(screen.getByLabelText(customer.username!!));
    expect(views.outOfSyncEmail.saveButton()).toBeEnabled();
  });

  //   SF email changed to match Okta Email
  it('should save the okta username', async () => {
    await renderEmailField();

    //save the default selected radio button
    await actions.editOutOfSyncEmail();
    await actions.saveOutOfSyncEmail();

    const expectedRequest: UpdateEmailRequest = {
      email: { id: customer.email!.id, address: customer.username }
    };
    expect(views.outOfSyncEmail.accountField()).toBeInTheDocument(); // Restore the display view
    expect(mockUpdateEmail).toHaveBeenCalledWith(expectedRequest);
    expect(screen.getByText(customer.username!)).toBeInTheDocument();
    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.EMAIL_SAVE_SUCCEEDED
    );
  });

  it('should allow the user to select other and change the email to a new email', async () => {
    await renderEmailField();
    await actions.editOutOfSyncEmail();
    await actions.selectRadioChoice(Tx.OutOfSync.OTHER);
    await actions.enterEmail(newEmail);
    await actions.saveOutOfSyncEmail();

    const expectedRequest: UpdateEmailRequest = {
      email: { id: customer.email!.id, address: newEmail }
    };
    expect(mockUpdateEmail).toHaveBeenCalledWith(expectedRequest);
    expect(screen.getByText(newEmail!)).toBeInTheDocument();
  });
  describe('verifies email when changing to existing salesforce email', () => {
    // Dialog properties are managed in the component's test'
    let salesforceEmail: string;

    const renderFieldAndSubmitDisplayEmail = async () => {
      salesforceEmail = customer.email?.address!;
      await renderEmailField();
      await actions.editOutOfSyncEmail();
      await actions.selectRadioChoice(Tx.OutOfSync.OTHER);

      // the input is autofilled with the existing salesforce email
      const input = views.outOfSyncEmail.inputField() as HTMLInputElement;
      expect(input.value).toEqual(salesforceEmail);
      await actions.saveOutOfSyncEmail();
    };

    it('should initiate the verification process, when the user selects their salesforce email', async () => {
      await renderFieldAndSubmitDisplayEmail();

      const expectedRequest: ChallengeEmailRequest = { email: salesforceEmail };
      expect(mockChallengeEmail).toHaveBeenCalledWith(expectedRequest);

      const dialog = await waitFor(async () => await screen.findByRole('dialog'));
      expect(dialog).toBeInTheDocument();
      expect(within(dialog).getByText(salesforceEmail)).toBeVisible();
    });

    it('should dismiss banner on X click', async () => {
      await renderFieldAndSubmitDisplayEmail();

      const dialog = await waitFor(async () => await screen.findByRole('dialog'));
      expect(dialog).toBeInTheDocument();

      await waitFor(async () => {
        await userEvent.click(screen.getByTestId('otp-dialog-close-button'));
      });

      expect(screen.queryByText(DialogTx.TITLE)).not.toBeInTheDocument();
      expect(screen.queryByText(DialogTx.SUBTITLE)).not.toBeInTheDocument();
      expect(within(dialog).queryByText(salesforceEmail)).not.toBeInTheDocument();
    });

    it('should dismiss banner on background click', async () => {
      await renderFieldAndSubmitDisplayEmail();
      const dialog = await waitFor(async () => await screen.findByRole('dialog'));
      expect(dialog).toBeInTheDocument();

      await waitFor(async () => {
        await userEvent.click(dialog.parentElement!);
      });

      expect(screen.queryByText(DialogTx.TITLE)).not.toBeInTheDocument();
      expect(screen.queryByText(DialogTx.SUBTITLE)).not.toBeInTheDocument();
      expect(within(dialog).queryByText(salesforceEmail)).not.toBeInTheDocument();
    });
  });

  describe('given the user receives a server error for the email, they will see helper text upon save', async () => {
    test.each([
      [UpdateEmailErrorStatus.EMAIL_ALREADY_IN_USE, Tx.HelperText.EMAIL_ALREADY_IN_USE],
      [UpdateEmailErrorStatus.INVALID_EMAIL, Tx.HelperText.INVALID_EMAIL]
    ])('when %s response code should show %s message', async (responseCode, displayText) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 500
      } as AxiosResponse;

      mockUpdateEmail.mockRejectedValue(
        new AxiosError('message', '500', undefined, undefined, response)
      );

      await renderAndUpdateEmail();

      expect(within(views.outOfSyncEmail.baseField()).getByText(displayText)).toBeInTheDocument();
    });
  });

  describe('displays alert notifications when the updateEmail call fails with an error', () => {
    test.each([
      [
        UpdateEmailErrorStatus.ACCOUNT_UNDER_MAINTENANCE,
        Tx.Notifications.Title.ACCOUNT_UNDER_MAINTENANCE,
        Tx.Notifications.Message.ACCOUNT_UNDER_MAINTENANCE
      ],
      [UpdateEmailErrorStatus.ERROR, Tx.Notifications.Title.ERROR, Tx.Notifications.Message.ERROR],
      [
        UpdateEmailErrorStatus.NO_ACCOUNT_FOUND,
        Tx.Notifications.Title.NO_ACCOUNT_FOUND,
        Tx.Notifications.Message.NO_ACCOUNT_FOUND
      ],
      [
        UpdateEmailErrorStatus.TOKEN_EXPIRED,
        Tx.Notifications.Title.TOKEN_EXPIRED,
        Tx.Notifications.Message.TOKEN_EXPIRED
      ],
      [
        UpdateEmailErrorStatus.TOKEN_INVALID,
        Tx.Notifications.Title.TOKEN_INVALID,
        Tx.Notifications.Message.TOKEN_INVALID
      ]
    ])('when %s occurs display %s and %s', async (responseCode, title, message) => {
      const response: AxiosResponse = {
        data: { status: responseCode },
        status: 500
      } as AxiosResponse;

      mockUpdateEmail.mockRejectedValue(
        new AxiosError('message', '500', undefined, undefined, response)
      );

      await renderAndUpdateEmail();

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent(title);
        expect(screen.getByRole('alert')).toHaveTextContent(message);
      });
    });
  });

  it('given an error occurs, displays generic failure', async () => {
    mockUpdateEmail.mockRejectedValue('Something went wrong.');

    await renderAndUpdateEmail();

    expectNotificationAlertContainsTitle(
      TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
    );
  });
});
