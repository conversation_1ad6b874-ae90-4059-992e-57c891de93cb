import React from 'react';
import { Grid } from '@mui/material';
import { EditablePasswordField } from './AccountFields/PasswordField/EditablePasswordField';
import { useGetCustomer } from '../../networkRequests/queries/useGetCustomer';
import { EmailField } from './AccountFields/EmailField';

export const AccountInfoSection: React.FC = () => {
  const { customer } = useGetCustomer();

  return (
    <Grid data-testid="account-info-section">
      <EmailField />
      {customer.isConverted && customer.email && (
        <EditablePasswordField displayValue="********" customerEmail={customer.email} />
      )}
    </Grid>
  );
};
