import React from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, Typography } from '@mui/material';
import { CardIcon, PaymentTypeIcon } from '../../../components/icons/PaymentTypeIcon';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { PaymentMethod } from '../../../networkRequests/responseEntities/PaymentEntities';
import { Design } from '../../../helpers/Design';

type CardProps = { paymentMethod: PaymentMethod };

export const MinimalPaymentCard: React.FC<CardProps> = ({ paymentMethod }: CardProps) => {
  const { t: translate } = useTranslation();
  const styles = style();
  const isPayPal = paymentMethod.cardType === CardIcon.PAYPAL;

  const label = translate(TranslationKeys.MakePaymentsPage.SELECT_OPTION_LABEL, {
    lastFourDigits: paymentMethod.cardNumberLastFourDigits
  });
  return (
    <Grid container {...styles.container}>
      <PaymentTypeIcon cardType={paymentMethod.cardType as CardIcon} />
      <Typography {...styles.text}>{isPayPal ? paymentMethod.accountId : label}</Typography>
    </Grid>
  );
};

// -- styles --
const style = () => ({
  container: {
    sx: {
      flexDirection: 'row',
      alignContent: 'center',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  text: {
    sx: {
      overflow: 'hidden',
      textOverflow: 'ellipsis'
    }
  }
});
