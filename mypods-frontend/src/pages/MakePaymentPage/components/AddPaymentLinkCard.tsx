import React from 'react';
import { Grid } from '@mui/material';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { PlusCircularIcon } from '../../../components/icons/PlusCircularIcon';
import { ROUTES } from '../../../Routes';
import { TranslationKeys } from '../../../locales/TranslationKeys';

export const AddPaymentLinkCard: React.FC = () => {
  const styles = style();
  const { t: translate } = useTranslation();

  return (
    <Grid container {...styles.addPayment}>
      <PlusCircularIcon {...styles.addPaymentIcon} />
      <Link
        {...styles.addPaymentText}
        to={ROUTES.MANAGE_PAYMENT_METHOD}
        state={{
          originPath: ROUTES.MAKE_PAYMENT
        }}>
        {translate(TranslationKeys.MakePaymentsPage.ADD_PAYMENT_METHOD_LINK)}
      </Link>
    </Grid>
  );
};

// -- styles --
const style = () => ({
  addPayment: {
    sx: {
      flexDirection: 'row',
      alignContent: 'center',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  addPaymentIcon: {
    style: {
      color: '#0069E5'
    }
  },
  addPaymentText: {
    style: {
      ...Design.Alias.Text.BodyUniversal.Md,
      textDecoration: 'none',
      color: '#0069E5'
    }
  }
});
