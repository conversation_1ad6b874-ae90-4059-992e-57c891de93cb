import { Grid, Typography } from '@mui/material';
import Divider from '@mui/material/Divider';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  BillingInvoice,
  formatByCurrency
} from '../../../networkRequests/responseEntities/BillingEntities';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';

const Tx = TranslationKeys.MakePaymentsPage;

export const ReviewInvoicesSection = ({
  invoices,
  totalPaymentAmount
}: {
  invoices: BillingInvoice[];
  totalPaymentAmount: number;
}) => {
  const { t: translate } = useTranslation();
  const styles = reviewStyles();
  // TODO: Show this section only for commercial customers

  return (
    <Grid {...styles.reviewPaymentContainer}>
      {invoices.map(
        (invoice) =>
          invoice.balanceDue > 0 && (
            <Grid {...styles.unpaidInvoice} key={invoice.invoiceNumber}>
              <Typography>
                {translate(Tx.INVOICE_PREFIX)}
                {invoice.invoiceNumber}
              </Typography>
              <Typography {...styles.balanceAmount}>
                {formatByCurrency(invoice.balanceDue, invoice.currencyType)}
              </Typography>
            </Grid>
          )
      )}
      {invoices.length > 1 && (
        <Grid {...styles.totalDue}>
          <Typography>{translate(Tx.TOTAL_LABEL)}</Typography>
          <Typography {...styles.balanceAmount}>{formatByCurrency(totalPaymentAmount)}</Typography>
        </Grid>
      )}
      <Divider {...styles.paymentsDivider} />
    </Grid>
  );
};

const reviewStyles = () => ({
  reviewPaymentContainer: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      gap: Design.Primitives.Spacing.xxs
    }
  },
  unpaidInvoice: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingBottom: Design.Primitives.Spacing.xxs
    }
  },
  totalDue: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingTop: Design.Primitives.Spacing.xxs
    }
  },
  paymentsDivider: {
    sx: {
      paddingTop: Design.Primitives.Spacing.xxs
    }
  },
  balanceAmount: {
    sx: {
      font: Design.Alias.Text.BodyUniversal.MdBold.fontSize,
      fontWeight: Design.Alias.Text.BodyUniversal.MdBold.fontWeight
    }
  }
});
