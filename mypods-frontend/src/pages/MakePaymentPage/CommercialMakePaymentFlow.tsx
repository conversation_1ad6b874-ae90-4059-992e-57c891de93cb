import React, { useState } from 'react';
import { BillingInvoice } from '../../networkRequests/responseEntities/BillingEntities';
import { CommercialMakePaymentPage } from './CommercialMakePaymentPage';
import { CommercialSelectInvoicesPage } from './CommercialSelectInvoicesPage';
import { useGetBillingInformation } from '../../networkRequests/queries/useGetBillingInformation';

export const CommercialMakePaymentFlow = () => {
  const { billingInformation } = useGetBillingInformation();
  const unpaidInvoices = billingInformation.invoices.filter(
    (invoice) => invoice.balanceDue > 0 && !invoice.isPaid
  );
  const [hasCompletedSelection, setHasCompletedSelection] = useState<boolean>(false);
  const [selectedInvoices, setSelectedInvoices] = useState<BillingInvoice[] | null>(null);

  const completeSelection = (invoices: BillingInvoice[]) => {
    setSelectedInvoices(invoices);
    setHasCompletedSelection(true);
  };

  if (selectedInvoices != null && hasCompletedSelection) {
    return (
      <CommercialMakePaymentPage
        invoicesToPay={selectedInvoices}
        goToSelectPage={() => setHasCompletedSelection(false)}
      />
    );
  }

  return (
    <CommercialSelectInvoicesPage
      unpaidInvoices={unpaidInvoices}
      selectedInvoices={selectedInvoices}
      makePayment={completeSelection}
    />
  );
};
