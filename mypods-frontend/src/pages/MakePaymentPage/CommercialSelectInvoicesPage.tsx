import { Grid, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useImmer } from 'use-immer';
import { BillingInvoice } from '../../networkRequests/responseEntities/BillingEntities';
import { PageLayout } from '../../components/PageLayout';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { BackLink } from '../../components/buttons/BackLink';
import { ROUTES } from '../../Routes';
import { StretchableLoadingButton } from '../../components/buttons/StretchableLoadingButton';
import { Design } from '../../helpers/Design';
import { theme } from '../../PodsTheme';
import { InvoiceCheckbox, SelectAllInvoiceCheckbox } from './components/InvoiceCheckbox';
import { useGetBillingInformation } from '../../networkRequests/queries/useGetBillingInformation';

const Tx = TranslationKeys.MakePaymentsPage;

type InvoiceWithStatus = {
  invoiceNumber: string;
  balanceDue: number;
  dueDate: string;
  checked: boolean;
};

export const CommercialSelectInvoicesPage = ({
  unpaidInvoices,
  selectedInvoices,
  makePayment
}: {
  unpaidInvoices: BillingInvoice[];
  selectedInvoices: BillingInvoice[] | null;
  makePayment: (invoices: BillingInvoice[]) => void;
}) => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = style(isMobile);
  const {
    billingInformation: { totalBalance }
  } = useGetBillingInformation();
  const selectedInvoiceNumbers = new Set(selectedInvoices?.map((inv) => inv.invoiceNumber));

  const getCheckedStatus = (it: BillingInvoice) =>
    selectedInvoices === null || selectedInvoices.length === 0
      ? true
      : selectedInvoiceNumbers.has(it.invoiceNumber);

  const [invoicesWithStatus, setInvoicesWithStatus] = useImmer<InvoiceWithStatus[]>(
    unpaidInvoices.map((it) => ({
      invoiceNumber: it.invoiceNumber!,
      balanceDue: it.balanceDue,
      dueDate: it.dueDate ?? '',
      checked: getCheckedStatus(it)
    }))
  );
  const allSelected = invoicesWithStatus.every((invoice) => invoice.checked);
  const isInvoiceSelected: boolean = invoicesWithStatus.some((invoice) => invoice.checked);

  const handleSelectAllChange = (checkedStatus: boolean) => {
    const newCheckedState = checkedStatus;
    setInvoicesWithStatus(
      invoicesWithStatus.map((invoice) => ({
        ...invoice,
        checked: newCheckedState
      }))
    );
  };

  const handleCheckboxChange = (index: number) => {
    setInvoicesWithStatus((prevState) => {
      const checkboxToUpdate = prevState[index];
      checkboxToUpdate.checked = !checkboxToUpdate.checked;
    });
  };

  const handleNext = () => {
    const checkedInvoiceNumbers = new Set(
      invoicesWithStatus
        .filter((invoice) => invoice.checked)
        .map((invoice) => invoice.invoiceNumber)
    );

    const invoicesToPay: BillingInvoice[] = unpaidInvoices.filter((invoice) =>
      checkedInvoiceNumbers.has(invoice.invoiceNumber!)
    );
    makePayment(invoicesToPay);
  };

  return (
    <PageLayout columnsLg={6}>
      <Grid {...styles.container}>
        <Grid {...styles.headerSection}>
          <Grid container flexDirection="column" rowGap={Design.Primitives.Spacing.md}>
            <Grid item>
              <BackLink route={ROUTES.BILLING} />
            </Grid>
            <Grid item>
              <Typography {...styles.pagination}>
                {translate(TranslationKeys.CommonComponents.PROGRESS_COUNTER, {
                  current: 1,
                  total: 2
                })}
              </Typography>
            </Grid>
          </Grid>
          <Typography variant="h1">{translate(Tx.HEADER)}</Typography>
          <Typography variant="body2">{translate(Tx.SelectInvoice.SUBTITLE)}</Typography>
        </Grid>
        <Grid container flexDirection="column" rowGap={1}>
          <Grid>
            <Typography variant="h4">{translate(Tx.SelectInvoice.QUESTION)}</Typography>
            <Typography variant="body2">{translate(Tx.SelectInvoice.QUESTION_SUBTEXT)}</Typography>
          </Grid>
          <SelectAllInvoiceCheckbox
            checked={allSelected}
            totalBalance={totalBalance}
            onCheckboxChange={handleSelectAllChange}
          />
          {invoicesWithStatus.map((invoice, index) => (
            <InvoiceCheckbox
              key={index}
              invoiceNumber={invoice.invoiceNumber ?? ''}
              dueDate={invoice.dueDate}
              balanceDue={invoice.balanceDue}
              checked={invoice.checked}
              onCheckboxChange={() => handleCheckboxChange(index)}
            />
          ))}
        </Grid>
        <StretchableLoadingButton
          disabled={!isInvoiceSelected}
          isMobile={isMobile}
          label={translate(TranslationKeys.CommonComponents.NEXT_BUTTON)}
          onClick={handleNext}
        />
      </Grid>
    </PageLayout>
  );
};

// -- styles --
const style = (isMobile: boolean) => ({
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.lgPlus
    }
  },
  headerSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  pagination: { sx: { ...Design.Alias.Text.BodyUniversal.LgBold } },
  commercialPaymentAmount: {
    sx: {
      display: 'flex',
      alignItems: 'flex-start',
      justifyContent: 'space-between'
    }
  },
  paymentAmountDisclaimer: {
    sx: {
      ...(isMobile && { width: '168px' })
    }
  }
});
