import React, { useState } from 'react';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { PaymentMethodSelect } from './components/PaymentMethodSelect';
import { PaymentAmountButtonGroup } from './components/PaymentAmountButtonGroup';
import { PageLayout } from '../../components/PageLayout';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { StretchableLoadingButton } from '../../components/buttons/StretchableLoadingButton';
import { theme } from '../../PodsTheme';
import { ReviewInvoicesSection } from './components/ReviewInvoicesSection';
import {
  BillingInvoice,
  formatByCurrency
} from '../../networkRequests/responseEntities/BillingEntities';
import { useMakePaymentState } from './useMakePaymentState';
import { BackLink } from '../../components/buttons/BackLink';

const Tx = TranslationKeys.MakePaymentsPage;

function getTotalPaymentAmount(selectedInvoices: BillingInvoice[]) {
  return selectedInvoices.reduce((total, it) => total + it.balanceDue, 0);
}

// -- impls --
export const CommercialMakePaymentPage = ({
  invoicesToPay,
  goToSelectPage
}: {
  invoicesToPay: BillingInvoice[];
  goToSelectPage: () => void;
}) => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = style(isMobile);

  const totalPaymentAmount = getTotalPaymentAmount(invoicesToPay);
  const state = useMakePaymentState(invoicesToPay);
  const [isFormValid, setIsFormValid] = useState<boolean>(true);
  const renderReadOnlyPaymentAmount = () => invoicesToPay.length > 1;

  return (
    <PageLayout columnsLg={6}>
      <Grid {...styles.container}>
        <Grid {...styles.headerSection}>
          <Grid container flexDirection="column" rowGap={Design.Primitives.Spacing.md}>
            <Grid item>
              <BackLink handleClick={goToSelectPage} />
            </Grid>
            <Grid item>
              <Typography {...styles.pagination}>
                {translate(TranslationKeys.CommonComponents.PROGRESS_COUNTER, {
                  current: 2,
                  total: 2
                })}
              </Typography>
            </Grid>
          </Grid>
          <Grid>
            <Typography variant="h1">{translate(Tx.HEADER)}</Typography>
          </Grid>
          <Grid>
            <Typography variant="body2">{translate(Tx.SUBTITLE)}</Typography>
          </Grid>
        </Grid>
        <ReviewInvoicesSection
          invoices={invoicesToPay}
          totalPaymentAmount={totalPaymentAmount || 0}
        />
        {renderReadOnlyPaymentAmount() ? (
          <Grid {...styles.commercialPaymentAmount}>
            <Grid>
              <Typography variant="body1">{translate(Tx.INPUT_LABEL)}</Typography>
              <Typography variant="subtitle2" {...styles.paymentAmountDisclaimer}>
                {translate(Tx.PAYMENT_AMOUNT_DISCLAIMER)}
              </Typography>
            </Grid>
            <Typography variant="h2">{formatByCurrency(totalPaymentAmount || 0)}</Typography>
          </Grid>
        ) : (
          <PaymentAmountButtonGroup
            totalAmount={totalPaymentAmount}
            setCustomPaymentAmount={state.setPaymentAmount}
            setIsFormValid={setIsFormValid}
          />
        )}
        <PaymentMethodSelect onChange={state.handlePaymentMethodChange} />
        <StretchableLoadingButton
          isMobile={isMobile}
          label={translate(state.submitButtonTx)}
          onClick={state.handleSubmitPayment}
          isLoading={state.isPaymentPending}
          disabled={!isFormValid || state.isPaymentPending || !state.paymentMethod}
        />
      </Grid>
    </PageLayout>
  );
};
// -- styles --
const style = (isMobile: boolean) => ({
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  headerSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  pagination: { sx: { ...Design.Alias.Text.BodyUniversal.LgBold } },
  commercialPaymentAmount: {
    sx: {
      display: 'flex',
      alignItems: 'flex-start',
      justifyContent: 'space-between'
    }
  },
  paymentAmountDisclaimer: {
    sx: {
      ...(isMobile && { width: '168px' })
    }
  }
});
