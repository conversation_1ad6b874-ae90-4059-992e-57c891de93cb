import React from 'react';
import { useGetCustomer } from '../../networkRequests/queries/useGetCustomer';
import { CustomerType } from '../../networkRequests/responseEntities/CustomerEntities';
import { ResidentialMakePaymentPage } from './ResidentialMakePaymentPage';
import { CommercialMakePaymentFlow } from './CommercialMakePaymentFlow';

export const MakePaymentPage = () => {
  const { customer } = useGetCustomer();

  if (customer.customerType === CustomerType.COMMERCIAL) {
    return <CommercialMakePaymentFlow />;
  }
  return <ResidentialMakePaymentPage />;
};
