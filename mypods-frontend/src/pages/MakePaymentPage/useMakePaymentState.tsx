import { Dispatch, SetStateAction, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { AxiosError } from 'axios';
import { datadogRum } from '@datadog/browser-rum';
import {
  PayInvoicesRequest,
  PaymentDeclinedErrors,
  PaymentMethod
} from '../../networkRequests/responseEntities/PaymentEntities';
import { ROUTES } from '../../Routes';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { BillingInvoice } from '../../networkRequests/responseEntities/BillingEntities';
import { useMakePayment } from '../../networkRequests/mutations/useMakePayment';
import useNotificationContext from '../../components/notifications/NotificationContext';
import { useGtmEvents } from '../../config/google/useGtmEvents';
import { ErrorResponse } from '../../networkRequests/responseEntities/ErrorEntities';
import { PodsAlertIcon, PodsAlertProps, PodsAlertType } from '../../components/alert/PodsAlert';
import { BillingPageLocationState } from '../BillingPage/BillingPage';
import { useGetBillingInformation } from '../../networkRequests/queries/useGetBillingInformation';
import { useGetPaymentMethods } from '../../networkRequests/queries/useGetPaymentMethods';
import { useMyPodsService } from '../../networkRequests/MyPodsService';

const Tx = TranslationKeys.MakePaymentsPage;

interface IMakePaymentState {
  handleSubmitPayment: () => void;
  isPaymentPending: boolean;
  setPaymentAmount: Dispatch<SetStateAction<number>>;
  paymentAmount: number | undefined;
  paymentMethod: PaymentMethod | undefined;
  handlePaymentMethodChange: (value: string) => void;
  submitButtonTx: string;
}

const MAX_TIMEOUT_MS = 30_000;
export const BALANCE_UPDATE_TIME_KEY = 'time_for_balance_to_update_after_payment';

export const useMakePaymentState = (invoicesToPay: BillingInvoice[]) => {
  const { setNotification } = useNotificationContext();
  const { t: translate } = useTranslation();
  const navigate = useNavigate();
  const makePayment = useMakePayment();
  const { getBillingInformation: getBillingInformationNoCache } = useMyPodsService();
  const { billingInformation, refetch: refetchBillingInfo } = useGetBillingInformation();
  const { paymentMethods } = useGetPaymentMethods();
  const [paymentAmount, setPaymentAmount] = useState<number | undefined>(
    billingInformation.totalBalance
  );
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | undefined>(
    paymentMethods.find((method) => method.isPrimary)
  );
  const [isPaymentPending, setIsPaymentPending] = useState<boolean>(false);
  const gtmEvents = useGtmEvents();
  const [submitButtonTx, setSubmitButtonTx] = useState<string>(Tx.SUBMIT_BUTTON);
  const navigateToBillingSuccess = () => {
    datadogRum.stopDurationVital(BALANCE_UPDATE_TIME_KEY);
    setNotification({
      message: translate(Tx.Notifications.SUCCESS),
      isError: false
    });
    refetchBillingInfo();
    navigate(ROUTES.BILLING);
  };

  const handleSubmitPayment = () => {
    if (paymentAmount && paymentMethod && billingInformation.invoices) {
      const request: PayInvoicesRequest = {
        paymentMethodId: paymentMethod.paymentMethodId!,
        totalPaymentAmount: paymentAmount,
        invoices: invoicesToPay.map((invoice) => ({
          invoiceNumber: invoice.invoiceNumber,
          paymentAmount: invoice.balanceDue,
          dueDate: invoice.dueDate
        }))
      };
      datadogRum.startDurationVital(BALANCE_UPDATE_TIME_KEY);
      setIsPaymentPending(true);

      const gtmPayload = {
        accountBalance: billingInformation.totalBalance,
        paymentMethod,
        paymentAmount
      };
      gtmEvents.submitPayment(gtmPayload);

      const startTime = Date.now();
      const sevenSecondTimeoutId = setTimeout(() => {
        setSubmitButtonTx(Tx.SUBMIT_BUTTON_SEVEN);
      }, 7000);
      const fourteenSecondTimeoutId = setTimeout(() => {
        setSubmitButtonTx(Tx.SUBMIT_BUTTON_FOURTEEN);
      }, 14000);

      const monitorTimedOut = () => Date.now() - startTime >= MAX_TIMEOUT_MS;

      const monitorUntilUpdated = async (currentPaymentAmount: number): Promise<void> => {
        const targetBalance = billingInformation.totalBalance - currentPaymentAmount;
        const {
          billingInformation: { totalBalance: updatedBalance }
        } = await getBillingInformationNoCache();

        if (updatedBalance <= targetBalance) {
          navigateToBillingSuccess();
        } else if (monitorTimedOut()) {
          navigateToBillingSuccess();
        } else {
          setTimeout(monitorUntilUpdated.bind(this, currentPaymentAmount), 2000);
        }
      };

      makePayment.mutate(request, {
        onSuccess: async () => {
          gtmEvents.successPayment(gtmPayload);
          monitorUntilUpdated(request.totalPaymentAmount);
        },

        onError: (error: unknown) => {
          clearTimeout(sevenSecondTimeoutId);
          clearTimeout(fourteenSecondTimeoutId);
          if (error instanceof AxiosError && error.response?.data?.status) {
            const errorResponse = error.response.data as ErrorResponse;
            const status = errorResponse.status as PaymentDeclinedErrors;
            let messageKey = TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE;
            switch (status) {
              case 'NO_BALANCE_REMAINING':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.NO_BALANCE_REMAINING;
                break;
              case 'FRAUD_SUSPECTED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.FRAUD_SUSPECTED;
                break;
              case 'CARD_CLOSED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.CARD_CLOSED;
                break;
              case 'INACTIVE_CARD':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.INACTIVE_CARD;
                break;
              case 'INVALID_ACCOUNT':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.INVALID_ACCOUNT;
                break;
              case 'INSUFFICIENT_FUNDS':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.INSUFFICIENT_FUNDS;
                break;
              case 'PROCESSOR_DECLINED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.PROCESSOR_DECLINED;
                break;
              case 'CARD_ISSUER_DECLINED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.CARD_ISSUER_DECLINED;
                break;
              case 'BANK_PAYMENT_UNAUTHORISED':
                messageKey =
                  TranslationKeys.MakePaymentsPage.Error.Declined.BANK_PAYMENT_UNAUTHORISED;
                break;
              case 'PAYPAL_ACCOUNT_ISSUE':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.PAYPAL_ACCOUNT_ISSUE;
                break;
              case 'LIMIT_EXCEEDED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.LIMIT_EXCEEDED;
                break;
              case 'SOME_PAYMENTS_FAILED': {
                const alertProps: PodsAlertProps = {
                  title: translate(TranslationKeys.MakePaymentsPage.Error.Warning.TITLE),
                  description: translate(TranslationKeys.MakePaymentsPage.Error.Warning.BODY),
                  alertType: PodsAlertType.ERROR,
                  icon: PodsAlertIcon.ERROR
                };
                const locationState: BillingPageLocationState = { alertProps };
                refetchBillingInfo();
                navigate(ROUTES.BILLING, { state: locationState });
                return;
              }
              case 'DECLINED':
                messageKey = TranslationKeys.MakePaymentsPage.Error.Declined.DECLINED;
                break;
              default:
                break;
            }
            setNotification({
              message: translate(messageKey),
              isError: true
            });
          } else {
            setNotification({
              message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
              isError: true
            });
          }
          setIsPaymentPending(false);
          setSubmitButtonTx(Tx.SUBMIT_BUTTON);
        }
      });
    }
  };

  const handlePaymentMethodChange = (value: string) => {
    if (value === 'NA') {
      setPaymentMethod(undefined);
    } else {
      setPaymentMethod(paymentMethods.find((method) => method.paymentMethodId === value));
    }
  };

  return {
    handleSubmitPayment,
    isPaymentPending,
    setPaymentAmount,
    paymentAmount,
    paymentMethod,
    handlePaymentMethodChange,
    submitButtonTx
  } as IMakePaymentState;
};
