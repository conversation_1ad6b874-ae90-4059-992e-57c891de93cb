import React, { useState } from 'react';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { BackLink } from '../../components/buttons/BackLink';
import { ROUTES } from '../../Routes';
import { PaymentMethodSelect } from './components/PaymentMethodSelect';
import { PaymentAmountButtonGroup } from './components/PaymentAmountButtonGroup';
import { PageLayout } from '../../components/PageLayout';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { StretchableLoadingButton } from '../../components/buttons/StretchableLoadingButton';
import { theme } from '../../PodsTheme';
import { useMakePaymentState } from './useMakePaymentState';
import { useGetBillingInformation } from '../../networkRequests/queries/useGetBillingInformation';

// -- types --
interface Props {}

const Tx = TranslationKeys.MakePaymentsPage;

// -- impls --
export const ResidentialMakePaymentPage: React.FC<Props> = () => {
  const { t: translate } = useTranslation();
  const { billingInformation } = useGetBillingInformation();
  const unpaidInvoices = billingInformation.invoices.filter(
    (invoice) => invoice.balanceDue > 0 && !invoice.isPaid
  );
  const state = useMakePaymentState(unpaidInvoices);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = makePaymentStyles();
  const [isFormValid, setIsFormValid] = useState<boolean>(true);

  return (
    <PageLayout columnsLg={6}>
      <Grid {...styles.container}>
        <Grid {...styles.headerSection}>
          <BackLink route={ROUTES.BILLING} />
          <Grid>
            <Typography variant="h1">{translate(Tx.HEADER)}</Typography>
          </Grid>
        </Grid>
        <PaymentAmountButtonGroup
          totalAmount={billingInformation.totalBalance}
          setCustomPaymentAmount={state.setPaymentAmount}
          setIsFormValid={setIsFormValid}
        />
        <PaymentMethodSelect onChange={state.handlePaymentMethodChange} />
        <StretchableLoadingButton
          isMobile={isMobile}
          label={translate(state.submitButtonTx)}
          onClick={state.handleSubmitPayment}
          isLoading={state.isPaymentPending}
          disabled={!isFormValid || state.isPaymentPending || !state.paymentMethod}
        />
      </Grid>
    </PageLayout>
  );
};

// -- styles --
const makePaymentStyles = () => ({
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  headerSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  }
});
