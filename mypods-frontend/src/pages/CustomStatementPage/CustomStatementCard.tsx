import React from 'react';
import { ButtonBase, Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Design } from '../../helpers/Design';
import { DocumentIcon } from '../../components/icons/DocumentIcon';
import { DownloadIcon } from '../../components/icons/DownloadIcon';
import { CustomStatement } from '../../networkRequests/responseEntities/BillingEntities';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { useGtmEvents } from '../../config/google/useGtmEvents';

// -- types --
interface Props {
  customStatement: CustomStatement;
}

// -- impls --
export const CustomStatementCard: React.FC<Props> = ({ customStatement }: Props) => {
  const { t: translate } = useTranslation();
  const { content, dateRange } = customStatement;
  const styles = documentCardStyles;
  const gtmEvents = useGtmEvents();

  const onCardClick = () => {
    gtmEvents.fileDownload('custom-statement');
    window.open(content, '_blank');
  };

  return (
    <ButtonBase {...styles.button} onClick={onCardClick}>
      <Grid container {...styles.card}>
        <Grid item style={{ alignContent: 'center' }}>
          <DocumentIcon data-testid="document-icon" />
        </Grid>
        <Grid item {...styles.textContainer}>
          <Typography {...styles.cardTitle}>
            {translate(TranslationKeys.BillingPage.Statements.Statement.TITLE)}
          </Typography>
          <Typography {...styles.dateRange}>{dateRange}</Typography>
        </Grid>
        <Grid item style={{ alignContent: 'center' }}>
          <DownloadIcon {...styles.downloadIcon} />
        </Grid>
      </Grid>
    </ButtonBase>
  );
};

// -- styles --
const documentCardStyles = {
  button: {
    sx: {
      display: 'flex',
      width: '100%',
      textAlign: 'unset'
    }
  },
  card: {
    sx: {
      gap: Design.Primitives.Spacing.sm,
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'nowrap',
      padding: Design.Primitives.Spacing.sm,
      justifyContent: 'space-between',
      borderRadius: '8px',
      border: '1px solid var(--color-neutral-neutral300, #CBCBCB)',
      background: 'var(--color-defaults-neutral100-D, #FFF)',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)'
    }
  },
  textContainer: {
    sx: {
      display: 'flex',
      flex: 1,
      flexDirection: 'row',
      alignContent: 'center',
      justifyContent: 'space-between',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  cardTitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.MdBold,
      color: Design.Alias.Color.accent900
    }
  },
  dateRange: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md
    }
  },
  downloadIcon: {
    sx: {
      width: '24px',
      height: '24px'
    }
  }
};
