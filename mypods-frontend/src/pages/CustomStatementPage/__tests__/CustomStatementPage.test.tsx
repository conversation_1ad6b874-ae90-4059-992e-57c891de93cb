import { fireEvent, render, RenderResult, screen, waitFor, within } from '@testing-library/react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { CustomStatementPage } from '../CustomStatementPage';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { QueryClientProvider } from '@tanstack/react-query';
import { testQueryClient } from '../../../testUtils/RenderHelpers';
import {
  mockCreateCustomStatement,
  mockGetPaymentMethods,
  mockNavigate
} from '../../../../setupTests';
import { NotificationProvider } from '../../../components/notifications/NotificationContext';
import { ROUTES } from '../../../Routes';
import { MemoryRouter } from 'react-router-dom';

const Tx = TranslationKeys.CustomStatementPage;
let result: RenderResult;

const views = {
  header: () => result.getByText(Tx.HEADER),
  subheader: () => result.getByText(Tx.SUBTITLE),
  startDateInput: () => result.getByLabelText('customStatementPage.datePickers.startDate'),
  endDateInput: () => result.getByLabelText('customStatementPage.datePickers.endDate'),
  createStatementButton: () =>
    result.getByRole('button', { name: 'customStatementPage.button[default]' })
};

describe('Custom Statement Page', () => {
  let user: UserEvent;

  beforeEach(() => {
    user = userEvent.setup();
  });

  const enterStartAndEndDate = async (start: string, end: string) => {
    fireEvent.change(views.startDateInput()!, { target: { value: start } });
    fireEvent.change(views.endDateInput()!, { target: { value: end } });
  };

  const renderCustomStatementPage = async (): Promise<RenderResult> => {
    return render(
      <MemoryRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <QueryClientProvider client={testQueryClient()}>
          <NotificationProvider>
            <CustomStatementPage />
          </NotificationProvider>
        </QueryClientProvider>
      </MemoryRouter>
    );
    // await waitForElementToBeRemoved(() => result.queryByText(/Loading/));
  };

  it('should show the title & subtitle', async () => {
    result = await renderCustomStatementPage();

    expect(views.header()).toBeInTheDocument();
    expect(views.subheader()).toBeInTheDocument();
  });

  describe('date pickers', () => {
    it('should show fields for picking a for start date & end date, and a disabled Create button on load', async () => {
      result = await renderCustomStatementPage();

      expect(views.startDateInput()).toBeInTheDocument();
      expect(views.endDateInput()).toBeInTheDocument();

      expect(result.getByRole('button', { name: /customStatementPage.button/ })).toBeDisabled();
    });

    it('should show an enabled Create Statement button when both dates are selected', async () => {
      result = await renderCustomStatementPage();

      await enterStartAndEndDate('04/01/2024', '06/01/2024');

      expect(views.createStatementButton()).toBeEnabled();
    });
  });

  describe('generating a custom statement', () => {
    beforeEach(() => {
      mockCreateCustomStatement.mockResolvedValue({
        dateRange: '04/24 - 06/24',
        content: 'someBlob'
      });
    });

    it('shows a statement card in the Generated Statements section, when successful', async () => {
      result = await renderCustomStatementPage();

      await enterStartAndEndDate('04/01/2024', '06/01/2024');

      await waitFor(() => user.click(views.createStatementButton()));

      expect(
        result.getByRole('button', {
          name: 'billingPage.statements.statement.title 04/24 - 06/24'
        })
      ).toBeInTheDocument();
    });

    it('shows an alert when no statement is returned', async () => {
      mockCreateCustomStatement.mockRejectedValue('oops');
      result = await renderCustomStatementPage();

      await enterStartAndEndDate('04/01/2024', '06/01/2024');

      await waitFor(() => user.click(views.createStatementButton()));

      expect(result.getByRole('alert')).toHaveTextContent(
        TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
      );
    });

    it('should display alert when start date is after end date', async () => {
      result = await renderCustomStatementPage();
      await enterStartAndEndDate('06/01/2024', '04/01/2024');

      await waitFor(() => user.click(views.createStatementButton()));

      let alert = within(screen.getByTestId('custom-statement-alert'));
      expect(alert.getByText(Tx.ErrorMessages.INVALID_DATES_DESCRIPTION)).toBeInTheDocument();
      expect(alert.getByText(Tx.ErrorMessages.INVALID_DATES_TITLE)).toBeInTheDocument();

      expect(mockCreateCustomStatement).not.toHaveBeenCalled();
    });
  });

  it('should navigate back to the billing page', async () => {
    mockGetPaymentMethods.mockResolvedValue([]);
    result = await renderCustomStatementPage();

    const link = screen.getByTestId('back-link');
    await userEvent.click(link);

    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.BILLING);
  });
});
