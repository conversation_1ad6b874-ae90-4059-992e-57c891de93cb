import React from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { css } from 'pods-component-library/styled-system/css';

import { Button } from 'pods-component-library';
import { PageLayout } from '../../components/PageLayout';
import { Txt } from '../../components/Txt';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { Design } from '../../helpers/Design';
import { theme } from '../../PodsTheme';
import { ROUTES } from '../../Routes';

const Tx = TranslationKeys.NotFoundPage;

export const NotFoundPage: React.FC = () => {
  const { t: translate } = useTranslation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = notFoundStyles(isMobile);
  return (
    <PageLayout columnsLg={6}>
      <Grid container {...styles.container}>
        <Grid container item {...styles.textContainer}>
          <Txt i18nKey={Tx.TITLE} {...styles.title} />
          <Txt i18nKey={Tx.BODY} {...styles.body} />
        </Grid>
        <Grid>
          <Button
            variant="filled"
            buttonSize="large"
            color="secondary"
            onPress={() => navigate(ROUTES.HOME, { replace: true })}
            css={css.raw({ padding: '8px 36px' })}>
            {translate(Tx.BUTTON)}
          </Button>
        </Grid>
      </Grid>
    </PageLayout>
  );
};

// -- styles --
const notFoundStyles = (isMobile: boolean) => ({
  container: {
    sx: {
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
      height: '100%',
      flex: 1,
      gap: Design.Primitives.Spacing.md
    }
  },
  textContainer: {
    sx: {
      justifyContent: 'center',
      flexDirection: 'column',
      alignItems: 'center',
      gap: isMobile ? Design.Primitives.Spacing.xxs : Design.Primitives.Spacing.sm
    }
  },
  title: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Lg : Design.Alias.Text.Heading.Desktop.Lg),
      color: Design.Alias.Color.accent900
    }
  },
  body: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Alias.Color.neutral700,
      width: '328px'
    }
  }
});
