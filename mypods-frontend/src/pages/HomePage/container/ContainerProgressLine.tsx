import { Box, Grid } from '@mui/material';
import React, { ReactNode } from 'react';
import { Design } from '../../../helpers/Design';
import { TimelineCheckmarkIcon } from '../../../components/icons/TimelineCheckmarkIcon';
import { TimelineEmptyCircleIcon } from '../../../components/icons/TimelineEmptyCircleIcon';
import { TimelineFilledCircleIcon } from '../../../components/icons/TimelineFilledCircleIcon';
import { TimelineFadedEmptyCircleIcon } from '../../../components/icons/TimelineFadedEmptyCircleIcon';

export type ProgressLineVariant = 'FADED_SOLID' | 'DASHED' | 'FADED_DASHED';

interface Props {
  variant: ProgressLineVariant;
  isUpNext: Boolean;
  dataTestId: string;
  isFinal: boolean;
}

export const ContainerProgressLine: React.FC<Props> = ({
  variant,
  isUpNext,
  isFinal,
  dataTestId
}) => {
  const styles = getLineStyles(variant, isFinal);

  const renderIconForStatus = (): ReactNode => {
    switch (variant) {
      case 'FADED_SOLID':
        return <TimelineCheckmarkIcon data-testid="timeline-icon-checkmark" {...styles.icon} />;
      case 'DASHED':
        if (isUpNext)
          return <TimelineFilledCircleIcon data-testid="timeline-icon-filled" {...styles.icon} />;
        return <TimelineEmptyCircleIcon data-testid="timeline-icon-empty" {...styles.icon} />;
      case 'FADED_DASHED':
        return <TimelineFadedEmptyCircleIcon data-testid="timeline-icon-faded" {...styles.icon} />;
    }
  };

  return (
    <Grid item {...styles.containerProgressLine} data-testid={dataTestId}>
      {renderIconForStatus()}
      <Box data-testid={`trailing-line-${variant}`} {...styles.trailingLineBottom} />
    </Grid>
  );
};

const getLineStyles = (lineVariant: ProgressLineVariant, isFinal: boolean) => {
  const lineStyle = () => {
    if (isFinal) return `0px`;
    switch (lineVariant) {
      case 'FADED_SOLID':
        return `1px solid ${Design.Alias.Color.neutral600}`;
      case 'DASHED':
        return `1px dotted ${Design.Alias.Color.accent900}`;
      case 'FADED_DASHED':
        return `1px dotted ${Design.Alias.Color.neutral300}`;
    }
  };
  return {
    containerProgressLine: {
      sx: {
        display: 'flex',
        width: '17px',
        flexDirection: 'column',
        flexAlign: 'start',
        height: '100%'
      }
    },
    icon: {
      sx: {
        width: '17px',
        height: '17px'
      }
    },
    trailingLineTop: {
      sx: {
        height: '24px',
        width: '50%',
        borderRight: `1px solid ${Design.Alias.Color.neutral600}`
      }
    },
    trailingLineBottom: {
      sx: {
        height: '100%',
        width: '50%',
        borderRight: lineStyle()
      }
    }
  };
};
