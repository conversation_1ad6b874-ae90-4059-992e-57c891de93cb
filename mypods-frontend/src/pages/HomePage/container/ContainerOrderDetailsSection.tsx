import { Grid, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import Divider from '@mui/material/Divider';
import { useTranslation } from 'react-i18next';
import { theme } from '../../../PodsTheme';
import { Design } from '../../../helpers/Design';
import { DateComponent } from './moveleg/DateComponent';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ContainerProgressLine } from './ContainerProgressLine';

const Tx = TranslationKeys.HomePage.ContainerTile;

export const ContainerOrderDetailsSection = (props: { orderId: string; date: Date }) => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = sectionStyles(isMobile);

  return (
    <Grid container {...styles.containerOrderDetailsSection}>
      <Grid item>
        <ContainerProgressLine
          variant="FADED_SOLID"
          isUpNext={false}
          dataTestId="ordered-move-leg"
          isFinal={false}
        />
      </Grid>
      <Grid item xs {...styles.mainBody}>
        <Typography color="inherit" variant="h4" {...styles.titleText}>
          {translate(Tx.OrderDetails.STATUS)}
        </Typography>
        <Grid container {...styles.titleContainer}>
          <DateComponent firstDateValue={props.date} firstDateLabel="Date" />
          <Grid flexDirection="column" alignSelf="stretch" flex="1 0 0">
            <Typography color="inherit" variant="subtitle2" {...styles.orderNumberText}>
              {translate(Tx.OrderDetails.ORDER_NUMBER)}
            </Typography>
            <Typography color="inherit" variant="subtitle1">
              {props.orderId}
            </Typography>
          </Grid>
        </Grid>
        <Divider {...styles.divider} />
      </Grid>
    </Grid>
  );
};

const sectionStyles = (isMobile: boolean) => ({
  containerOrderDetailsSection: {
    sx: {
      flexDirection: 'row',
      columnGap: Design.Primitives.Spacing.xxs,
      color: Design.Alias.Color.neutral600
    }
  },
  mainBody: { sx: { flexGrow: 1, marginTop: '-5px' } },
  titleText: { sx: { paddingBottom: Design.Primitives.Spacing.xxs } },
  titleContainer: {
    sx: {
      columnGap: Design.Primitives.Spacing.md,
      rowGap: Design.Primitives.Spacing.xxs,
      flexDirection: isMobile ? 'column' : 'row'
    }
  },
  orderNumberText: {
    sx: {
      fontWeight: Design.Alias.Text.BodyUniversal.XsBold
    }
  },
  divider: {
    sx: {
      marginY: '36px'
    }
  }
});
