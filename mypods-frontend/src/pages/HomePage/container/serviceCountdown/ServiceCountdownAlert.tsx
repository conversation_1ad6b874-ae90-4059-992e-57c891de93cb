import React from 'react';
import { useServiceCountdownState } from './useServiceCountdownState';
import { MoveLeg } from '../../../../domain/OrderEntities';
import { PodsAlert, PodsAlertIcon, PodsAlertType } from '../../../../components/alert/PodsAlert';

interface ServiceCountdownAlertProps {
  moveLeg: MoveLeg;
}

export const ServiceCountdownAlert = ({ moveLeg }: ServiceCountdownAlertProps) => {
  const { serviceCountdownTextEta, helperText, daysUntil } = useServiceCountdownState(moveLeg);

  if (!serviceCountdownTextEta || !moveLeg.isUpNext) return null;

  const getAlertType = () => {
    if (daysUntil == null) return PodsAlertType.INFO;
    return daysUntil >= 2 ? PodsAlertType.GRAY : PodsAlertType.INFO;
  };

  return (
    <PodsAlert
      title={serviceCountdownTextEta}
      description={helperText}
      icon={PodsAlertIcon.TRUCK}
      alertType={getAlertType()}
    />
  );
};
