import { useTranslation } from 'react-i18next';
import { getDaysUntil } from '../../../../helpers/dateHelpers';
import {
  getServiceCountdownContext,
  getServiceCountdownHelperContext
} from '../../../../locales/TranslationConstants';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { MoveLeg } from '../../../../domain/OrderEntities';

const Tx = TranslationKeys.HomePage.ContainerTile;

export const useServiceCountdownState = (upNextMoveLeg?: MoveLeg) => {
  const { t: translate } = useTranslation();

  const daysUntil = getDaysUntil(upNextMoveLeg?.scheduledDate);

  const getServiceCountdownText = () => {
    if (daysUntil == null) return null;
    return translate(Tx.SERVICE_COUNTDOWN, {
      count: daysUntil,
      context: getServiceCountdownContext(upNextMoveLeg!!)
    });
  };

  const getServiceCountdownTextWithEta = () => {
    const serviceCountdownText = getServiceCountdownText();
    if (serviceCountdownText == null) return null;
    if (upNextMoveLeg?.eta) {
      const etaBetween = translate(Tx.SERVICE_COUNTDOWN_ETA_BETWEEN, {
        eta: upNextMoveLeg.eta
      });
      return `${serviceCountdownText} ${etaBetween}`;
    }
    return serviceCountdownText;
  };

  const getHelperText = () => {
    if (upNextMoveLeg == null) return '';
    if (daysUntil != null && daysUntil >= 2)
      return translate(Tx.SERVICE_COUNTDOWN_HELPER24_HOUR, {
        context: getServiceCountdownHelperContext(upNextMoveLeg)
      });
    if (
      upNextMoveLeg.serviceCountdownType === 'SELFDELIVERY' ||
      upNextMoveLeg.serviceCountdownType === 'SELFPICKUP'
    ) {
      if (daysUntil != null && daysUntil === 1)
        return translate(Tx.SERVICE_COUNTDOWN_HELPER_STORAGE_TOMORROW);
      if (daysUntil != null && daysUntil === 0)
        return translate(Tx.SERVICE_COUNTDOWN_HELPER_STORAGE_TODAY);
    }
    if (upNextMoveLeg.eta != null) return translate(Tx.SERVICE_COUNTDOWN_HELPER_WITH_ETA);
    return translate(Tx.SERVICE_COUNTDOWN_HELPER_WITHOUT_ETA, {
      context: getServiceCountdownHelperContext(upNextMoveLeg)
    });
  };
  return {
    serviceCountdownText: getServiceCountdownText(),
    serviceCountdownTextEta: getServiceCountdownTextWithEta(),
    helperText: getHelperText(),
    daysUntil
  };
};
