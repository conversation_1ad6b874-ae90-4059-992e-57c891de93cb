import { Grid, useMediaQuery } from '@mui/material';
import React, { useContext, useState } from 'react';
import Divider from '@mui/material/Divider';
import { Design } from '../../../helpers/Design';
import { ContainerTileHeader } from './ContainerTileHeader';
import { theme } from '../../../PodsTheme';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ContainerOrderDetailsSection } from './ContainerOrderDetailsSection';
import { MoveLegSection } from './moveleg/current/MoveLegSection';
import { ContainerToggleButton } from './ContainerToggleButton';
import { MoveLeg, MoveLegTypeEnum } from '../../../domain/OrderEntities';
import { ProgressLineStub } from './moveleg/ProgressLineStub';
import { ContainerContext } from '../../../context/ContainerContext';
import { UpcomingMoveLegSection } from './moveleg/upcoming/UpcomingMoveLegSection';
import { HistoryMoveLegSection } from './moveleg/history/HistoryMoveLegSection';
import { useContainerTileMoveLegs } from './useContainerTileMoveLegs';
import { MoveLegProvider } from './moveleg/MoveLegContext';

export const ContainerTile = () => {
  const { container, order } = useContext(ContainerContext);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [showHistoryMoveLegs, setShowHistoryMoveLegs] = useState(false);
  const [showUpcomingMoveLegs, setShowUpcomingMoveLegs] = useState(false);
  const { completedMoveLegs, currentMoveLegs, upcomingMoveLegs, lastMoveLegId } =
    useContainerTileMoveLegs(container.moveLegs);

  const styles = containerTileStyle(isMobile, showHistoryMoveLegs);
  const Tx = TranslationKeys.HomePage.ContainerTile;

  function toggleMoveLegHistory() {
    setShowHistoryMoveLegs((prev) => !prev);
  }

  function toggleMoveLegUpcoming() {
    setShowUpcomingMoveLegs((prev) => !prev);
  }

  const getLastDisplayedMoveLeg = () => {
    const lastCurrent = currentMoveLegs.slice(-1);
    const lastUpcoming = upcomingMoveLegs.slice(-1);
    if (showUpcomingMoveLegs && lastUpcoming.length > 0) {
      return lastUpcoming[0];
    }
    return lastCurrent[0];
  };

  const lastDisplayedMoveLeg = getLastDisplayedMoveLeg();
  const shouldRenderBottomDivider = (moveLegId: string): boolean =>
    moveLegId !== lastDisplayedMoveLeg.moveLegId;

  return (
    <Grid data-testid={container.containerId} {...styles.containerTile}>
      <ContainerTileHeader />
      <ContainerToggleButton
        onClick={() => toggleMoveLegHistory()}
        isExpanded={showHistoryMoveLegs}
        hideTranslationKey={Tx.HIDE_HISTORY}
        showTranslationKey={Tx.SHOW_HISTORY}
      />
      <Divider />
      <Grid {...styles.accordionContent}>
        {showHistoryMoveLegs && (
          <Grid data-testid="accordion-content-history">
            <ProgressLineStub />
            <ContainerOrderDetailsSection date={order.orderDate!} orderId={order.orderId} />
            {completedMoveLegs.map((moveLeg: MoveLeg) => (
              <MoveLegProvider
                key={
                  moveLeg.moveLegType === MoveLegTypeEnum.VISIT_CONTAINER
                    ? `${moveLeg.moveLegId}_${moveLeg.moveLegType}`
                    : moveLeg.moveLegId
                }
                moveLeg={moveLeg}
                lastMoveLegId={lastMoveLegId}
                isLastRenderedMoveLeg>
                <HistoryMoveLegSection key={moveLeg.moveLegId} />
              </MoveLegProvider>
            ))}
          </Grid>
        )}
        {currentMoveLegs && (
          <Grid data-testid="accordion-content-current">
            {!showHistoryMoveLegs && <ProgressLineStub />}
            {currentMoveLegs.map((moveLeg: MoveLeg) => (
              <MoveLegProvider
                key={
                  moveLeg.moveLegType === MoveLegTypeEnum.VISIT_CONTAINER
                    ? `${moveLeg.moveLegId}_${moveLeg.moveLegType}`
                    : moveLeg.moveLegId
                }
                moveLeg={moveLeg}
                lastMoveLegId={lastMoveLegId}
                isLastRenderedMoveLeg={shouldRenderBottomDivider(moveLeg.moveLegId)}>
                <MoveLegSection key={moveLeg.moveLegId} />
              </MoveLegProvider>
            ))}
          </Grid>
        )}
        {showUpcomingMoveLegs && (
          <Grid data-testid="accordion-content-upcoming">
            {upcomingMoveLegs.map((moveLeg: MoveLeg) => (
              <MoveLegProvider
                key={
                  moveLeg.moveLegType === MoveLegTypeEnum.VISIT_CONTAINER
                    ? `${moveLeg.moveLegId}_${moveLeg.moveLegType}`
                    : moveLeg.moveLegId
                }
                moveLeg={moveLeg}
                lastMoveLegId={lastMoveLegId}
                isLastRenderedMoveLeg={shouldRenderBottomDivider(moveLeg.moveLegId)}>
                <UpcomingMoveLegSection key={moveLeg.moveLegId} />
              </MoveLegProvider>
            ))}
          </Grid>
        )}
      </Grid>
      <Divider />
      {upcomingMoveLegs.length > 0 && (
        <ContainerToggleButton
          onClick={() => toggleMoveLegUpcoming()}
          isExpanded={showUpcomingMoveLegs}
          hideTranslationKey={Tx.HIDE_UPCOMING_ACTIVITY}
          showTranslationKey={Tx.SHOW_UPCOMING_ACTIVITY}
        />
      )}
    </Grid>
  );
};

const containerTileStyle = (isMobile: boolean, showHistory: boolean) => ({
  containerTile: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      borderRadius: Design.Primitives.Spacing.xxs,
      backgroundColor: Design.Alias.Color.neutral100,
      overflow: 'hidden',
      border: '1px solid var(--color-neutral-neutral300, #CBCBCB)',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)'
    }
  },
  showHistorySummary: {
    sx: {
      flexDirection: 'row',
      paddingLeft: isMobile ? '17px' : '38px',
      paddingRight: Design.Primitives.Spacing.lgPlus,
      paddingY: '11px',
      gap: '8px'
    }
  },
  icon: {
    sx: {
      width: Design.Primitives.Spacing.sm,
      height: Design.Primitives.Spacing.sm,
      transform: showHistory ? 'rotate(-90deg)' : 'rotate(0deg)'
    }
  },
  showHistoryText: {
    sx: { marginLeft: Design.Primitives.Spacing.xxs }
  },
  accordionContent: {
    sx: {
      paddingY: '0px',
      paddingX: isMobile ? Design.Primitives.Spacing.xxs : Design.Primitives.Spacing.lgPlus
    }
  },
  lineFragment: {
    sx: {
      flexDirection: 'row',
      columnGap: Design.Primitives.Spacing.xxs,
      color: Design.Alias.Color.neutral600
    }
  },
  trailingLineTop: {
    sx: {
      height: Design.Primitives.Spacing.md,
      width: '50%',
      borderRight: `1px solid ${Design.Alias.Color.neutral600}`
    }
  }
});
