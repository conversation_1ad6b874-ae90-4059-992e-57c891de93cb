import { useTranslation } from 'react-i18next';
import React, { useContext } from 'react';
import { Chip, Grid, Typography, useMediaQuery } from '@mui/material';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { Design } from '../../../helpers/Design';
import { useServiceCountdownState } from './serviceCountdown/useServiceCountdownState';
import { ContainerIcon } from './ContainerIcon';
import { ContainerContext } from '../../../context/ContainerContext';
import { getUpNextMoveLeg } from '../../../domain/OrderEntities';
import { theme } from '../../../PodsTheme';

export const ContainerTileHeader = () => {
  const { container } = useContext(ContainerContext);
  const Tx = TranslationKeys.HomePage.ContainerTile;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = containerTileHeaderStyles();
  const { t: translate } = useTranslation();
  const { serviceCountdownText } = useServiceCountdownState(getUpNextMoveLeg(container));
  const { containerSize } = container;

  function getContainerId(): string {
    return `${translate(Tx.ContainerHeader.CONTAINER_ID)}${container.containerId ? container.containerId : translate(Tx.ContainerHeader.NO_CONTAINER_ID)}`;
  }

  return (
    <Grid container data-testid="container-tile-header" {...styles.header}>
      <Grid item {...styles.iconContainer}>
        <ContainerIcon size={containerSize} />
      </Grid>
      <Grid item {...styles.titleContainer}>
        <Typography variant="h2Accent">
          {translate(Tx.ContainerHeader.CONTAINER_TYPE, { size: containerSize })}
        </Typography>
        <Typography variant="body1Accent">{getContainerId()}</Typography>
        {serviceCountdownText && isMobile && (
          <Grid item>
            <Chip label={serviceCountdownText} color="primary" size="medium" variant="filled" />
          </Grid>
        )}
      </Grid>

      {serviceCountdownText && !isMobile && (
        <Grid item>
          <Chip label={serviceCountdownText} color="primary" size="medium" variant="filled" />
        </Grid>
      )}
    </Grid>
  );
};

const containerTileHeaderStyles = () => ({
  header: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      padding: Design.Primitives.Spacing.sm,
      gap: Design.Primitives.Spacing.sm,
      backgroundColor: Design.Alias.Color.accent900,
      color: Design.Alias.Color.neutral100
    }
  },
  titleContainer: {
    sx: {
      display: 'flex',
      flexGrow: 1,
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  iconContainer: {
    sx: {
      display: 'flex',
      height: '72px',
      width: '72px',
      borderRadius: '36px',
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
      backgroundColor: Design.Alias.Color.secondary100
    }
  }
});
