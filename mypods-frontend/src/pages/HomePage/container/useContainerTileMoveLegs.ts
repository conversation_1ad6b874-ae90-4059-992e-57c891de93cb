import compact from 'lodash/compact';
import groupBy from 'lodash/groupBy';
import { MoveLeg } from '../../../domain/OrderEntities';

export const useContainerTileMoveLegs = (moveLegs: MoveLeg[]) => {
  const groupedMoveLegs = groupBy(moveLegs, (it) => it.scheduledStatus);
  const completedMoveLegs = groupedMoveLegs.PAST ?? [];
  const scheduledMoveLegs = groupedMoveLegs.FUTURE ?? [];
  const [firstUnscheduled, ...upcomingMoveLegs] = groupedMoveLegs.UNSCHEDULED ?? [];
  let currentMoveLegs = compact([...scheduledMoveLegs, firstUnscheduled]);

  // Move the unscheduled visit container leg from completed to current if the next real move leg is not in the past
  if (completedMoveLegs.length > 0) {
    const lastCompletedMoveLeg = completedMoveLegs[completedMoveLegs.length - 1];
    if (
      lastCompletedMoveLeg.moveLegType === 'VISIT_CONTAINER' &&
      lastCompletedMoveLeg.containerVisitDate == null
    ) {
      if (currentMoveLegs.length > 0) {
        const firstCurrentMoveLeg = currentMoveLegs[0];
        if (firstCurrentMoveLeg.scheduledStatus !== 'PAST') {
          completedMoveLegs.pop();
          currentMoveLegs = compact([lastCompletedMoveLeg, ...currentMoveLegs]);
        }
      }
    }
  }

  const lastMoveLegId = moveLegs.length > 0 ? moveLegs[moveLegs.length - 1].moveLegId : '';

  return {
    completedMoveLegs,
    currentMoveLegs,
    upcomingMoveLegs,
    lastMoveLegId
  };
};
