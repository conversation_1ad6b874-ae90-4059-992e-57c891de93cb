import React from 'react';
import { EightFootContainerIcon } from '../../../components/icons/EightFootContainerIcon';
import { SixteenFootContainerIcon } from '../../../components/icons/SixteenFootContainerIcon';
import { TwelveFootContainerIcon } from '../../../components/icons/TwelveFootContainerIcon';

export const ContainerIcon = ({ size }: { size: string }) => {
  if (size === '8') return <EightFootContainerIcon sx={{ height: '36px', width: '40px' }} />;
  if (size === '12') return <TwelveFootContainerIcon sx={{ height: '52px', width: '48px' }} />;
  return <SixteenFootContainerIcon sx={{ height: '53px', width: '49px' }} />;
};
