import React, { FC, useEffect } from 'react';
import { ContainerProvider } from '../../../context/ContainerContext';
import { ContainerTile } from './ContainerTile';
import { SingleOrderProvider } from '../../../context/SingleOrderContext';
import { useGetCustomerOrders } from '../../../networkRequests/queries/useGetCustomerOrders';
import { ContainerSkeleton } from '../../../components/Loading/ContainerSkeleton';

export const ContainerTileWrapper: FC<{
  topOfPageRef?: React.RefObject<HTMLDivElement>;
}> = ({ topOfPageRef }) => {
  const { customerOrders: orders, isError, refetch } = useGetCustomerOrders();

  useEffect(() => {
    if (isError) {
      refetch();
    }
  }, [isError]);

  if (isError) {
    return <ContainerSkeleton isError topOfPageRef={topOfPageRef} />;
  }

  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {orders?.map((order) => (
        <SingleOrderProvider state={{ order }} key={order.orderId}>
          {order.containers?.map((container, index) => (
            <ContainerProvider
              key={container.containerId ?? `container-context${index}}`}
              state={{ container, order }}>
              <ContainerTile />
            </ContainerProvider>
          ))}
        </SingleOrderProvider>
      ))}
    </>
  );
};
