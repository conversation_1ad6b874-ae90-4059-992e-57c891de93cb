import { Grid, TextField } from '@mui/material';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMapsLibrary } from '@vis.gl/react-google-maps';
import { Design } from '../../../../helpers/Design';
import { Txt } from '../../../../components/Txt';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { IMoveLegAddressState } from './useMoveLegAddressState';
import { ServiceAddress } from '../../../../domain/OrderEntities';
import { getAddressFromPlace } from '../../../../helpers/googlePlace/getAddressFromPlace';

export interface MoveLegAddressFormProps {
  state: IMoveLegAddressState;
  checkServicability: (address: ServiceAddress) => void;
}

const Tx = TranslationKeys.HomePage.MoveLegs.Scheduling;

export const MoveLegAddressForm = ({ state, checkServicability }: MoveLegAddressFormProps) => {
  const [placeAutocomplete, setPlaceAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const places = useMapsLibrary('places');
  const { t: translate } = useTranslation();
  const { value: addressValue, error, ...moveLegAddressState } = state;

  useEffect(() => {
    if (!places || !inputRef.current) return;

    const options = {
      componentRestrictions: { country: ['us', 'ca'] },
      fields: ['address_components'],
      types: ['address']
    };

    setPlaceAutocomplete(new places.Autocomplete(inputRef.current, options));
  }, [places]);

  const handleGooglePlaceChanged = () => {
    if (!placeAutocomplete) return;
    const placeAddress = getAddressFromPlace(placeAutocomplete.getPlace());
    const addressToSet = state.getValueFromPlaceAddress(placeAddress);

    state.setValue(addressToSet);
    checkServicability(addressToSet);
  };

  useEffect(() => {
    if (!placeAutocomplete) return;
    placeAutocomplete.addListener('place_changed', handleGooglePlaceChanged);
  }, [placeAutocomplete]);

  return (
    <Grid
      container
      flexDirection="column"
      gap={Design.Primitives.Spacing.sm}
      paddingBottom="34px"
      data-testid="move-leg-address-form">
      <Grid container item flexDirection="row" justifyContent="space-between">
        <Txt
          style={Design.Alias.Text.BodyUniversal.Md}
          sx={{ color: Design.Alias.Color.neutral700 }}>
          {translate(Tx.AddressLabels.LABEL_DEFAULT)}
        </Txt>
      </Grid>
      <Grid item>
        <TextField
          autoFocus
          fullWidth
          color="secondary"
          label={translate(TranslationKeys.AccountPage.AddressInfo.InputFields.ADDRESS1)}
          value={addressValue?.address1}
          error={!!error?.streetAddress}
          helperText={error?.streetAddress}
          onBlur={moveLegAddressState.displayStreetAddressError}
          onChange={(event) => moveLegAddressState.setAddress1(event.target.value)}
          placeholder=""
          inputRef={inputRef}
        />
      </Grid>
      <Grid item>
        <TextField
          fullWidth
          color="secondary"
          label={translate(TranslationKeys.AccountPage.AddressInfo.InputFields.ADDRESS2)}
          value={addressValue?.address2}
          onChange={(event) => moveLegAddressState.setAddress2(event.target.value)}
        />
      </Grid>
      <Grid item>
        <TextField
          fullWidth
          color="secondary"
          label={translate(TranslationKeys.AccountPage.AddressInfo.InputFields.POSTAL_CODE)}
          value={addressValue?.postalCode}
          error={!!error?.postalCode}
          helperText={error?.postalCode}
          onBlur={() => {
            checkServicability(state.value);
            moveLegAddressState.displayPostalCodeError();
          }}
          onChange={(event) => moveLegAddressState.setPostalCode(event.target.value)}
        />
      </Grid>
      <Grid container item spacing={1}>
        <Grid item xs={6}>
          <TextField
            fullWidth
            color="secondary"
            label={translate(TranslationKeys.AccountPage.AddressInfo.InputFields.CITY)}
            value={addressValue?.city}
            error={!!error?.city}
            helperText={error?.city}
            onBlur={moveLegAddressState.displayCityError}
            onChange={(event) => moveLegAddressState.setCity(event.target.value)}
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            fullWidth
            color="secondary"
            label={translate(TranslationKeys.AccountPage.AddressInfo.InputFields.STATE)}
            value={addressValue?.state}
            error={!!error?.state}
            helperText={error?.state}
            onBlur={moveLegAddressState.displayStateError}
            onChange={(event) => moveLegAddressState.setState(event.target.value)}
          />
        </Grid>
      </Grid>
    </Grid>
  );
};
