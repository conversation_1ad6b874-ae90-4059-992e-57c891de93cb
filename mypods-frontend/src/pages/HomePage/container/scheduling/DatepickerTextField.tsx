import { TextField, TextFieldProps } from '@mui/material';
import React from 'react';

interface DatePickerTextFieldProps {
  onClick: () => void;
  params: JSX.IntrinsicAttributes & TextFieldProps;
}

export const DatepickerTextField = ({ onClick, params }: DatePickerTextFieldProps) => (
  <TextField
    {...params}
    onClick={onClick}
    name="date"
    color="secondary"
    onKeyDown={(e: { preventDefault: () => any }) => e.preventDefault()}
    fullWidth
    inputProps={{
      ...params.inputProps,
      'data-testid': `date-input`,
      readOnly: true
    }}
    {...textFieldStyles.dateTextInput}
  />
);

const textFieldStyles = {
  dateTextInput: {
    sx: {
      '&  .MuiFormHelperText-root.Mui-error': {
        backgroundColor: 'white'
      }
    }
  }
};
