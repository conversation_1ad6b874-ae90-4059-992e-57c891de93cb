import React, { ReactNode } from 'react';
import { Grid } from '@mui/material';
import { Design } from '../../../../../helpers/Design';

type Props = {
  children: ReactNode;
};

export const ContainerPlacementContent = ({ children }: Props) => (
  <Grid container {...styles.contentContainer}>
    {children}
  </Grid>
);

const styles = {
  contentContainer: {
    sx: {
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  }
};
