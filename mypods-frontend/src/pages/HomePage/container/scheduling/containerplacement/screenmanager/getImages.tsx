import { FinalContainerPlacement } from '../../../../../../domain/OrderEntities';
import { ENV_VARS } from '../../../../../../environment';
import { SiteTypeChildScreens } from './ContainerPlacementModalTypes';

export const getImageForScreen = (screen: SiteTypeChildScreens) => {
  const getPath = () => {
    switch (screen) {
      case 'DRIVEWAY_STRAIGHT':
        return '/driveway/driveway-straight.jpg';
      case 'DRIVEWAY_STRAIGHT_CLOSE':
        return '/driveway/driveway-straight-close.jpg';
      case 'DRIVEWAY_STRAIGHT_MIDDLE':
        return '/driveway/driveway-straight-middle.jpg';
      case 'DRIVEWAY_STRAIGHT_FAR':
        return '/driveway/driveway-straight-far.jpg';
      case 'DRIVEWAY_CIRCULAR':
        return '/driveway/driveway-circular.jpg';
      case 'DRIVEWAY_L_SHAPED':
        return '/driveway/driveway-l_shaped.jpg';
      case 'DRIVE_CIRCULAR_CLOSE':
        return '/driveway/driveway-circular-close.jpg';
      case 'DRIVE_CIRCULAR_FAR':
        return '/driveway/driveway-circular-far.jpg';
      case 'DRIVE_L_SHAPED_CLOSE':
        return '/driveway/driveway-l_shaped-close.jpg';
      case 'DRIVE_L_SHAPED_MIDDLE':
        return '/driveway/driveway-l_shaped-middle.jpg';
      case 'DRIVE_L_SHAPED_FAR':
        return '/driveway/driveway-l_shaped-far.jpg';
      case 'PARKING_LOT_RIGHT':
        return '/parking-lot/parking-lot-right.jpg';
      case 'PARKING_LOT_FRONT':
        return '/parking-lot/parking-lot-front.jpg';
      case 'PARKING_LOT_BACK':
        return '/parking-lot/parking-lot-back.jpg';
      case 'PARKING_LOT_LEFT':
        return '/parking-lot/parking-lot-left.jpg';
    }
  };
  return `${ENV_VARS.ASSETS_BASE_URL}/container-placement${getPath()}`;
};

export const getImageForFinalPlacement = (placement: FinalContainerPlacement) => {
  const getPath = () => {
    switch (placement) {
      case 'DRIVEWAY_STRAIGHT_CLOSE_REAR':
        return '/driveway/driveway-straight-close-rear.jpg';
      case 'DRIVEWAY_STRAIGHT_CLOSE_CAB':
        return '/driveway/driveway-straight-close-cab.jpg';
      case 'DRIVEWAY_STRAIGHT_MIDDLE_REAR':
        return '/driveway/driveway-straight-middle-rear.jpg';
      case 'DRIVEWAY_STRAIGHT_MIDDLE_CAB':
        return '/driveway/driveway-straight-middle-cab.jpg';
      case 'DRIVEWAY_STRAIGHT_FAR_REAR':
        return '/driveway/driveway-straight-far-rear.jpg';
      case 'DRIVEWAY_STRAIGHT_FAR_CAB':
        return '/driveway/driveway-straight-far-cab.jpg';
      case 'DRIVEWAY_CIRCULAR_CLOSE_REAR':
        return '/driveway/driveway-circular-close-rear.jpg';
      case 'DRIVEWAY_CIRCULAR_CLOSE_CAB':
        return '/driveway/driveway-circular-close-cab.jpg';
      case 'DRIVEWAY_CIRCULAR_FAR_REAR':
        return '/driveway/driveway-circular-far-rear.jpg';
      case 'DRIVEWAY_CIRCULAR_FAR_CAB':
        return '/driveway/driveway-circular-far-cab.jpg';
      case 'DRIVEWAY_L_SHAPED_CLOSE_REAR':
        return '/driveway/driveway-l_shaped-close-rear.jpg';
      case 'DRIVEWAY_L_SHAPED_CLOSE_CAB':
        return '/driveway/driveway-l_shaped-close-cab.jpg';
      case 'DRIVEWAY_L_SHAPED_MIDDLE_REAR':
        return '/driveway/driveway-l_shaped-middle-rear.jpg';
      case 'DRIVEWAY_L_SHAPED_MIDDLE_CAB':
        return '/driveway/driveway-l_shaped-middle-cab.jpg';
      case 'DRIVEWAY_L_SHAPED_FAR_REAR':
        return '/driveway/driveway-l_shaped-far-rear.jpg';
      case 'DRIVEWAY_L_SHAPED_FAR_CAB':
        return '/driveway/driveway-l_shaped-far-cab.jpg';
      case 'STREET_LEFT_CAB':
        return '/street/street-left-cab.jpg';
      case 'STREET_FRONT_CAB':
        return '/street/street-front-cab.jpg';
      case 'STREET_RIGHT_CAB':
        return '/street/street-right-cab.jpg';
      case 'STREET_BACK_CAB':
        return '/street/street-back-cab.jpg';
      case 'PARKING_LOT_RIGHT_01':
        return '/parking-lot/parking-lot-right-01-cab.jpg';
      case 'PARKING_LOT_RIGHT_02':
        return '/parking-lot/parking-lot-right-02-rear.jpg';
      case 'PARKING_LOT_RIGHT_03':
        return '/parking-lot/parking-lot-right-03-cab.jpg';
      case 'PARKING_LOT_RIGHT_04':
        return '/parking-lot/parking-lot-right-04-cab.jpg';
      case 'PARKING_LOT_FRONT_01':
        return '/parking-lot/parking-lot-front-01-cab.jpg';
      case 'PARKING_LOT_FRONT_02':
        return '/parking-lot/parking-lot-front-02-cab.jpg';
      case 'PARKING_LOT_FRONT_03':
        return '/parking-lot/parking-lot-front-03-rear.jpg';
      case 'PARKING_LOT_FRONT_04':
        return '/parking-lot/parking-lot-front-04-cab.jpg';
      case 'PARKING_LOT_BACK_01':
        return '/parking-lot/parking-lot-back-01-cab.jpg';
      case 'PARKING_LOT_BACK_02':
        return '/parking-lot/parking-lot-back-02-cab.jpg';
      case 'PARKING_LOT_BACK_03':
        return '/parking-lot/parking-lot-back-03-cab.jpg';
      case 'PARKING_LOT_BACK_04':
        return '/parking-lot/parking-lot-back-04-cab.jpg';
      case 'PARKING_LOT_LEFT_01':
        return '/parking-lot/parking-lot-left-01-rear.jpg';
      case 'PARKING_LOT_LEFT_02':
        return '/parking-lot/parking-lot-left-02-cab.jpg';
      case 'PARKING_LOT_LEFT_03':
        return '/parking-lot/parking-lot-left-03-cab.jpg';
      case 'PARKING_LOT_LEFT_04':
        return '/parking-lot/parking-lot-left-04-cab.jpg';
    }
  };

  return `${ENV_VARS.ASSETS_BASE_URL}/container-placement${getPath()}`;
};
