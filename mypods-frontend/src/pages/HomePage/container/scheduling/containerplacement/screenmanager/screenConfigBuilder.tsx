import { ReactNode } from 'react';
import { ContainerPlacementNode } from './useContainerPlacementScreenManager';
import { ContainerPlacementScreenName } from './ContainerPlacementModalTypes';

export interface IContainerPlacementNodeBuilder {
  addChild: (childNodeBuilder: IContainerPlacementNodeBuilder) => IContainerPlacementNodeBuilder;
  setParent: (node: ContainerPlacementNode) => IContainerPlacementNodeBuilder;
  build: () => ContainerPlacementNode;
}

export const nodeBuilder = (
  name: ContainerPlacementScreenName,
  component: ReactNode
): IContainerPlacementNodeBuilder => {
  const buildingNode: ContainerPlacementNode = {
    name,
    component,
    children: []
  };

  const builder: IContainerPlacementNodeBuilder = {
    addChild(childNodeBuilder: IContainerPlacementNodeBuilder): IContainerPlacementNodeBuilder {
      const childNode = childNodeBuilder.setParent(buildingNode).build();
      buildingNode.children.push(childNode);
      return builder;
    },
    setParent(parentNode: ContainerPlacementNode): IContainerPlacementNodeBuilder {
      buildingNode.parent = parentNode;
      return builder;
    },
    build(): ContainerPlacementNode {
      return buildingNode;
    }
  };

  return builder;
};

export interface IScreenConfigBuilder {
  nextNode: (node: IContainerPlacementNodeBuilder) => IScreenConfigBuilder;
  // build returns the first screen's node
  build: () => ContainerPlacementNode;
}

export const screenConfigBuilder = (firstNodeBuilder: IContainerPlacementNodeBuilder) => {
  const firstNode = firstNodeBuilder.build();
  let currentRootScreen = firstNode;

  // Wrap the result in a builder const so we can chain method calls when using it
  const builder: IScreenConfigBuilder = {
    nextNode: (nextBuilder: IContainerPlacementNodeBuilder): IScreenConfigBuilder => {
      const newNode = nextBuilder.build();
      currentRootScreen.next = newNode;
      newNode.previous = currentRootScreen;
      currentRootScreen = newNode;

      return builder;
    },
    build: () => firstNode
  };

  return builder;
};
