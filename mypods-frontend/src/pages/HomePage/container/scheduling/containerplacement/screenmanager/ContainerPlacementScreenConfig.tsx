import React from 'react';
import { nodeBuilder, screenConfigBuilder } from './screenConfigBuilder';
import { PavedSurfaceScreen } from '../screens/PavedSurfaceScreen';
import { ReviewScreen } from '../screens/ReviewScreen';
import { SiteTypeScreen } from '../screens/SiteTypeScreen';
import {
  SiteTypeFinal,
  SiteTypeNonFinal,
  SiteTypeSelectionFinalProps,
  SiteTypeSelectionNonFinalProps
} from '../screens/SiteTypeSelectionScreens';
import { TranslationKeys } from '../../../../../../locales/TranslationKeys';
import { DrivewayNotesScreen } from '../screens/DriverNotesScreen';
import { PlacementTipsScreen } from '../screens/PlacementTips';

const Tx = TranslationKeys.HomePage.ContainerPlacement.SiteTypeScreen.Prompts;
// prettier-ignore
export const containerPlacementFirstScreen = () =>
  screenConfigBuilder(nodeBuilder('PAVED_SURFACE', <PavedSurfaceScreen />))
      .nextNode(nodeBuilder('PLACEMENT_TIPS',<PlacementTipsScreen/>))
      .nextNode(nodeBuilder('SITE_TYPE', <SiteTypeScreen />)
        .addChild(
          nodeBuilder('DRIVEWAY_TYPE', <SiteTypeNonFinal {...drivewayProps} />)
            .addChild(nodeBuilder('DRIVEWAY_STRAIGHT', <SiteTypeNonFinal  {...drivewayStraightProps} />)
              .addChild(nodeBuilder('DRIVEWAY_STRAIGHT_CLOSE',
                <SiteTypeFinal {...drivewayStraightCloseProps} />))
              .addChild(nodeBuilder('DRIVEWAY_STRAIGHT_MIDDLE',
                <SiteTypeFinal {...drivewayStraightMiddleProps} />))
              .addChild(nodeBuilder('DRIVEWAY_STRAIGHT_FAR',
                <SiteTypeFinal {...drivewayStraightFarProps} />)))
            .addChild(nodeBuilder('DRIVEWAY_CIRCULAR', <SiteTypeNonFinal {...drivewayCircularProps} />)
              .addChild(nodeBuilder('DRIVE_CIRCULAR_CLOSE',
                <SiteTypeFinal {...drivewayCircularCloseProps} />))
              .addChild(nodeBuilder('DRIVE_CIRCULAR_FAR',
                <SiteTypeFinal {...drivewayCircularFarProps} />)))
            .addChild(nodeBuilder('DRIVEWAY_L_SHAPED', <SiteTypeNonFinal {...drivewayLShapedProps} />)
              .addChild(nodeBuilder('DRIVE_L_SHAPED_CLOSE',
                <SiteTypeFinal {...drivewayLShapedCloseProps} />))
              .addChild(nodeBuilder('DRIVE_L_SHAPED_MIDDLE',
                <SiteTypeFinal {...drivewayLShapedMiddleProps} />))
              .addChild(nodeBuilder('DRIVE_L_SHAPED_FAR',
                <SiteTypeFinal {...drivewayLShapedFarProps} />))))
        .addChild(nodeBuilder('STREET_TYPE', <SiteTypeFinal {...streetAlleywayProps} />))
        .addChild(nodeBuilder('PARKING_LOT_TYPE', <SiteTypeNonFinal {...parkingLotProps} />)
          .addChild(nodeBuilder('PARKING_LOT_FRONT',
            <SiteTypeFinal {...parkingLotFrontProps} />))
          .addChild(nodeBuilder('PARKING_LOT_RIGHT',
            <SiteTypeFinal {...parkingLotRightProps} />))
          .addChild(nodeBuilder('PARKING_LOT_LEFT',
            <SiteTypeFinal {...parkingLotLeftProps} />))
          .addChild(nodeBuilder('PARKING_LOT_BACK',
            <SiteTypeFinal {...parkingLotBackProps} />)))
    )
    .nextNode(nodeBuilder('DRIVER_NOTES', <DrivewayNotesScreen />))
    .nextNode(nodeBuilder('REVIEW', <ReviewScreen />))
    .build();

// region Driveway
const drivewayProps: SiteTypeSelectionNonFinalProps = {
  siteTypes: ['DRIVEWAY_STRAIGHT', 'DRIVEWAY_CIRCULAR', 'DRIVEWAY_L_SHAPED'],
  prompt: Tx.Driveway.SITE_TYPE
};

// region Driveway Straight
const drivewayStraightProps: SiteTypeSelectionNonFinalProps = {
  siteTypes: ['DRIVEWAY_STRAIGHT_CLOSE', 'DRIVEWAY_STRAIGHT_MIDDLE', 'DRIVEWAY_STRAIGHT_FAR'],
  prompt: Tx.Driveway.CONTAINER_LOCATION
};
const drivewayStraightCloseProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_STRAIGHT_CLOSE_CAB', 'DRIVEWAY_STRAIGHT_CLOSE_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
const drivewayStraightMiddleProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_STRAIGHT_MIDDLE_CAB', 'DRIVEWAY_STRAIGHT_MIDDLE_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
const drivewayStraightFarProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_STRAIGHT_FAR_CAB', 'DRIVEWAY_STRAIGHT_FAR_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
// endregion

// region Driveway Circular
const drivewayCircularProps: SiteTypeSelectionNonFinalProps = {
  siteTypes: ['DRIVE_CIRCULAR_CLOSE', 'DRIVE_CIRCULAR_FAR'],
  prompt: Tx.Driveway.CONTAINER_LOCATION
};
const drivewayCircularCloseProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_CIRCULAR_CLOSE_CAB', 'DRIVEWAY_CIRCULAR_CLOSE_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
const drivewayCircularFarProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_CIRCULAR_FAR_CAB', 'DRIVEWAY_CIRCULAR_FAR_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
// endregion

// region Driveway L Shaped
const drivewayLShapedProps: SiteTypeSelectionNonFinalProps = {
  siteTypes: ['DRIVE_L_SHAPED_CLOSE', 'DRIVE_L_SHAPED_MIDDLE', 'DRIVE_L_SHAPED_FAR'],
  prompt: Tx.Driveway.CONTAINER_LOCATION
};
const drivewayLShapedCloseProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_L_SHAPED_CLOSE_CAB', 'DRIVEWAY_L_SHAPED_CLOSE_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
const drivewayLShapedMiddleProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_L_SHAPED_MIDDLE_CAB', 'DRIVEWAY_L_SHAPED_MIDDLE_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
const drivewayLShapedFarProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_L_SHAPED_FAR_CAB', 'DRIVEWAY_L_SHAPED_FAR_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
// endregion
// endregion Driveway

// region Street Alleyway
const streetAlleywayProps: SiteTypeSelectionFinalProps = {
  selections: ['STREET_LEFT_CAB', 'STREET_FRONT_CAB', 'STREET_RIGHT_CAB', 'STREET_BACK_CAB'],
  prompt: Tx.StreetAlleyway.SITE_TYPE
};
// endregion

// region Parking Lot
const parkingLotProps: SiteTypeSelectionNonFinalProps = {
  siteTypes: ['PARKING_LOT_RIGHT', 'PARKING_LOT_FRONT', 'PARKING_LOT_LEFT', 'PARKING_LOT_BACK'],
  prompt: Tx.ParkingLot.SITE_TYPE
};

const parkingLotFrontProps: SiteTypeSelectionFinalProps = {
  selections: [
    'PARKING_LOT_FRONT_01',
    'PARKING_LOT_FRONT_02',
    'PARKING_LOT_FRONT_03',
    'PARKING_LOT_FRONT_04'
  ],
  prompt: Tx.Driveway.CAB_DIRECTION
};

const parkingLotBackProps: SiteTypeSelectionFinalProps = {
  selections: [
    'PARKING_LOT_BACK_01',
    'PARKING_LOT_BACK_02',
    'PARKING_LOT_BACK_03',
    'PARKING_LOT_BACK_04'
  ],
  prompt: Tx.Driveway.CAB_DIRECTION
};

const parkingLotLeftProps: SiteTypeSelectionFinalProps = {
  selections: [
    'PARKING_LOT_LEFT_01',
    'PARKING_LOT_LEFT_02',
    'PARKING_LOT_LEFT_03',
    'PARKING_LOT_LEFT_04'
  ],
  prompt: Tx.Driveway.CAB_DIRECTION
};

const parkingLotRightProps: SiteTypeSelectionFinalProps = {
  selections: [
    'PARKING_LOT_RIGHT_01',
    'PARKING_LOT_RIGHT_02',
    'PARKING_LOT_RIGHT_03',
    'PARKING_LOT_RIGHT_04'
  ],
  prompt: Tx.Driveway.CAB_DIRECTION
};
// endregion
