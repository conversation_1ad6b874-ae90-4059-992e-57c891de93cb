import React, { act } from 'react';
import {
  ContainerPlacementNode,
  IContainerPlacementScreenManager,
  useContainerPlacementScreenManager
} from '../useContainerPlacementScreenManager';
import { renderHook } from '@testing-library/react';
import {
  IContainerPlacementNodeBuilder,
  nodeBuilder,
  screenConfigBuilder
} from '../screenConfigBuilder';
import { ContainerPlacementScreenName } from '../ContainerPlacementModalTypes';
import { PavedSurfaceScreen } from '../../screens/PavedSurfaceScreen';
import { SiteTypeScreen } from '../../screens/SiteTypeScreen';
import { ReviewScreen } from '../../screens/ReviewScreen';

const actions = {
  selectChoice: async (manager: { current: IContainerPlacementScreenManager }, choice: any) => {
    await act(async () => manager.current.selectChoice(choice));
  },
  selectChoiceWithChild: async (
    manager: { current: IContainerPlacementScreenManager },
    choice: any,
    childScreen: ContainerPlacementScreenName
  ) => {
    await act(async () => manager.current.selectChoiceWithChild(choice, childScreen));
  },
  nextStep: async (manager: { current: IContainerPlacementScreenManager }) => {
    await act(async () => manager.current.nextStep());
  },
  previousStep: async (manager: { current: IContainerPlacementScreenManager }) => {
    await act(async () => manager.current.previousStep());
  },
  goto: async (
    manager: { current: IContainerPlacementScreenManager },
    screen: ContainerPlacementScreenName
  ) => {
    await act(async () => manager.current.goto(screen));
  }
};

const expectCurrentScreenNameEquals = (
  manager: { current: IContainerPlacementScreenManager },
  screenName: ContainerPlacementScreenName
) => {
  expect(manager.current.currentScreen.name).toEqual(screenName);
};

describe('ContainerPlacementScreenManager', () => {
  let pavedSurfaceNode: IContainerPlacementNodeBuilder;
  let reviewNode: IContainerPlacementNodeBuilder;

  beforeEach(() => {
    pavedSurfaceNode = nodeBuilder('PAVED_SURFACE', <PavedSurfaceScreen />);
    reviewNode = nodeBuilder('REVIEW', <ReviewScreen />);
  });

  function renderScreenManager(firstScreen: ContainerPlacementNode) {
    const { result } = renderHook(() => useContainerPlacementScreenManager(firstScreen));
    return result;
  }

  it('after initializing, first screen is the current screen', async () => {
    const manager = renderScreenManager(screenConfigBuilder(pavedSurfaceNode).build());

    expectCurrentScreenNameEquals(manager, 'PAVED_SURFACE');
  });

  it('after next step, second screen is the current screen', async () => {
    const firstNode = screenConfigBuilder(pavedSurfaceNode).nextNode(reviewNode).build();

    const manager = renderScreenManager(firstNode);
    await actions.selectChoice(manager, true);

    expectCurrentScreenNameEquals(manager, 'REVIEW');
  });

  it('after next step, then previous step, first screen is the current screen', async () => {
    const firstNode = screenConfigBuilder(pavedSurfaceNode).nextNode(reviewNode).build();

    const manager = renderScreenManager(firstNode);
    await actions.selectChoice(manager, false);
    await actions.previousStep(manager);

    expectCurrentScreenNameEquals(manager, 'PAVED_SURFACE');
  });

  it('should not proceed if a choice has not been selected', async () => {
    const firstNode = screenConfigBuilder(pavedSurfaceNode).nextNode(reviewNode).build();

    const manager = renderScreenManager(firstNode);
    await actions.nextStep(manager);

    expectCurrentScreenNameEquals(manager, 'PAVED_SURFACE');
  });

  describe('Given nodes with branches', () => {
    let firstNode: ContainerPlacementNode;

    beforeEach(() => {
      firstNode = screenConfigBuilder(pavedSurfaceNode)
        .nextNode(
          nodeBuilder('SITE_TYPE', <SiteTypeScreen />)
            .addChild(nodeBuilder('DRIVEWAY_TYPE', <div>Driveway Type</div>))
            .addChild(nodeBuilder('STREET_TYPE', <div>Street Type</div>))
            .addChild(nodeBuilder('PARKING_LOT_TYPE', <div>Parking Lot Type</div>))
        )
        .nextNode(reviewNode)
        .build();
    });

    it('should proceed to driveway type branch when choice is selected', async () => {
      const manager = renderScreenManager(firstNode);
      expectCurrentScreenNameEquals(manager, 'PAVED_SURFACE');

      await actions.selectChoice(manager, false);
      expectCurrentScreenNameEquals(manager, 'SITE_TYPE');

      await actions.selectChoiceWithChild(manager, 'DRIVEWAY', 'DRIVEWAY_TYPE');

      expectCurrentScreenNameEquals(manager, 'DRIVEWAY_TYPE');
    });

    it('should proceed backward from final screen', async () => {
      const manager = renderScreenManager(firstNode);

      expectCurrentScreenNameEquals(manager, 'PAVED_SURFACE');
      await actions.selectChoice(manager, false);
      expectCurrentScreenNameEquals(manager, 'SITE_TYPE');
      await actions.selectChoiceWithChild(manager, 'DRIVEWAY', 'DRIVEWAY_TYPE');
      expectCurrentScreenNameEquals(manager, 'DRIVEWAY_TYPE');
      await actions.selectChoice(manager, 'anything');

      expectCurrentScreenNameEquals(manager, 'REVIEW');

      await actions.previousStep(manager);
      expectCurrentScreenNameEquals(manager, 'DRIVEWAY_TYPE');

      await actions.previousStep(manager);
      expectCurrentScreenNameEquals(manager, 'SITE_TYPE');

      await actions.nextStep(manager);
      expectCurrentScreenNameEquals(manager, 'DRIVEWAY_TYPE');

      await actions.previousStep(manager);
      await actions.previousStep(manager);
      expectCurrentScreenNameEquals(manager, 'PAVED_SURFACE');
    });
  });
});
