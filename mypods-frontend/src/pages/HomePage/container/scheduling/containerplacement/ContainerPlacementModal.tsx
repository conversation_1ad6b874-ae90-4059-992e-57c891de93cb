import React, { useEffect } from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { PodsModal } from '../../../../../components/Modals/PodsModal';
import { ContainerPlacementNavigationHeader } from './ContainerPlacementNavigationHeader';
import { PodsModalContent } from '../../../../../components/Modals/PodsModalBody';
import { useContainerPlacementContext } from './context/ContainerPlacementContext';
import { useGtmEvents } from '../../../../../config/google/useGtmEvents';
import useContainerContext from '../../../../../context/ContainerContext';
import useMoveLegContext from '../../moveleg/MoveLegContext';
import { theme } from '../../../../../PodsTheme';

export type ContainerPlacementModalProps = {
  open: boolean;
  handleOnClose: () => void;
};

export const ContainerPlacementModal = ({ open, handleOnClose }: ContainerPlacementModalProps) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { manager } = useContainerPlacementContext();
  const { order, container } = useContainerContext();
  const { moveLeg } = useMoveLegContext();
  const gtmEvents = useGtmEvents();

  useEffect(() => {
    if (open) {
      const args = {
        orderId: order.orderId,
        containerId: container.containerId,
        moveLegId: moveLeg.moveLegId,
        moveLegType: moveLeg.moveLegType,
        stepName: manager.currentScreen.name
      };
      gtmEvents.viewPlacementStep(args);
    }
  }, [manager.currentScreen.name, open]);

  const renderCurrentScreen = () => manager.currentScreen.component;

  return (
    <PodsModal open={open}>
      <Grid sx={{ ...(!isMobile && { minHeight: '704px' }) }}>
        <ContainerPlacementNavigationHeader handleOnClosed={handleOnClose} />
        <PodsModalContent>{renderCurrentScreen()}</PodsModalContent>
      </Grid>
    </PodsModal>
  );
};
