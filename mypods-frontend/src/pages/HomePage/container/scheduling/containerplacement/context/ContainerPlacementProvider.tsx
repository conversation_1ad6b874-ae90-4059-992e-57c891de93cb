import React, { FC, ReactNode, useMemo, useState } from 'react';
import { ContainerPlacement } from '../../../../../../domain/OrderEntities';
import {
  ContainerPlacementContext,
  ContainerPlacementContextState,
  initialContainerPlacement
} from './ContainerPlacementContext';
import { useContainerPlacementScreenManager } from '../screenmanager/useContainerPlacementScreenManager';
import { containerPlacementFirstScreen } from '../screenmanager/ContainerPlacementScreenConfig';

export type ContainerPlacementProviderProps = {
  existingPlacement?: ContainerPlacement;
  handleFinish: (containerPlacement: ContainerPlacement) => void;
  children: ReactNode;
};

export const ContainerPlacementProvider: FC<ContainerPlacementProviderProps> = ({
  existingPlacement,
  children,
  handleFinish
}) => {
  const manager = useContainerPlacementScreenManager(
    containerPlacementFirstScreen(),
    existingPlacement
  );
  const [containerPlacement, setContainerPlacement] = useState<ContainerPlacement>(
    existingPlacement ?? initialContainerPlacement
  );

  const value: ContainerPlacementContextState = useMemo(
    () => ({
      manager,
      containerPlacement,
      setContainerPlacement,
      handleFinish
    }),
    [manager, containerPlacement, setContainerPlacement, handleFinish]
  );

  return (
    <ContainerPlacementContext.Provider value={value}>
      {children}
    </ContainerPlacementContext.Provider>
  );
};
