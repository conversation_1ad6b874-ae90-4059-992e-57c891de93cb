import { createContext, Dispatch, SetStateAction, useContext } from 'react';
import { ContainerPlacement } from '../../../../../../domain/OrderEntities';
import { IContainerPlacementScreenManager } from '../screenmanager/useContainerPlacementScreenManager';
import { ContainerPlacementScreenName } from '../screenmanager/ContainerPlacementModalTypes';

export type ContainerPlacementContextState = {
  manager: IContainerPlacementScreenManager;
  containerPlacement: ContainerPlacement;
  setContainerPlacement: Dispatch<SetStateAction<ContainerPlacement>>;
  handleFinish: (containerPlacement: ContainerPlacement) => void;
};

export const initialContainerPlacement: ContainerPlacement = {
  isPavedSurface: false,
  siteType: 'DRIVEWAY',
  placement: 'DRIVEWAY_STRAIGHT_CLOSE_REAR',
  driverNotes: ''
};

const initialState: ContainerPlacementContextState = {
  manager: {
    currentScreen: {
      name: 'PAVED_SURFACE',
      component: undefined,
      children: []
    },
    selectChoice(_choice: any): void {
      throw new Error('Function not implemented.');
    },
    selectChoiceWithChild(_choice: any, _childScreen: ContainerPlacementScreenName): void {
      throw new Error('Function not implemented.');
    },
    hasNextStep(): boolean {
      throw new Error('Function not implemented.');
    },
    nextStep(): void {
      throw new Error('Function not implemented.');
    },
    hasPreviousStep(): boolean {
      throw new Error('Function not implemented.');
    },
    previousStep(): void {
      throw new Error('Function not implemented.');
    },
    goto(_: ContainerPlacementScreenName): void {
      throw new Error('Function not implemented.');
    },
    gotoSiteTypeSelectedChild(): void {
      throw new Error('Function not implemented.');
    },
    completedPercentage(): number {
      throw new Error('Function not implemented.');
    }
  },
  containerPlacement: initialContainerPlacement,
  setContainerPlacement: (_) => {},
  handleFinish(_: ContainerPlacement): void {}
};

export const ContainerPlacementContext =
  createContext<ContainerPlacementContextState>(initialState);

export function useContainerPlacementContext() {
  return useContext(ContainerPlacementContext);
}
