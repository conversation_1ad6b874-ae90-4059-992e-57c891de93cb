import { Grid, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import React from 'react';
import { theme } from '../../../../../PodsTheme';
import { Design } from '../../../../../helpers/Design';

interface ContainerPlacementHeaderProps {
  titleKey: string;
  subTitleKey?: string;
}

export const ContainerPlacementHeader = ({
  titleKey,
  subTitleKey
}: ContainerPlacementHeaderProps) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = headerStyles(isMobile);
  const { t: translate } = useTranslation();

  return (
    <Grid container item {...styles.header}>
      <Grid {...styles.titleContainer}>
        <Typography {...styles.title}>{translate(titleKey)}</Typography>
      </Grid>
      {subTitleKey != null && (
        <Grid {...styles.subtitleContainer}>
          <Typography {...styles.subtitle}>{translate(subTitleKey)}</Typography>
        </Grid>
      )}
    </Grid>
  );
};

const headerStyles = (isMobile: boolean) => ({
  header: {
    sx: {
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs,
      color: Design.Alias.Color.accent900
    }
  },
  titleContainer: {},
  title: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Xl : Design.Alias.Text.Heading.Desktop.Lg),
      color: 'inherit'
    }
  },
  subtitleContainer: {
    sx: { ...Design.Alias.Text.BodyUniversal.Sm }
  },
  subtitle: {
    sx: {
      color: 'inherit'
    }
  }
});
