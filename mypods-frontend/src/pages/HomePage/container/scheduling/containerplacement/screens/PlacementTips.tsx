import React from 'react';
import { Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../../../../locales/TranslationKeys';
import { useContainerPlacementContext } from '../context/ContainerPlacementContext';
import { ContainerPlacementHeader } from '../ContainerPlacementHeader';
import { ContainerPlacementActionButton } from '../ContainerPlacementActionButton';
import { ContainerPlacementContent } from '../ContainerPlacementContent';

const Tx = TranslationKeys.HomePage.ContainerPlacement;

export const PlacementTipsScreen = () => {
  const styles = placementTipsScreenStyle();
  const { t: translate } = useTranslation();
  const { manager } = useContainerPlacementContext();

  const handleNextClick = () => {
    manager.selectChoice('anything');
  };

  return (
    <ContainerPlacementContent>
      <ContainerPlacementHeader titleKey={Tx.PlacementTips.TITLE} />
      <Grid item container {...styles.listContainer}>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM1)}</Typography>
        </Grid>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM2)}</Typography>
        </Grid>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM3)}</Typography>
        </Grid>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM4)}</Typography>
        </Grid>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM5)}</Typography>
        </Grid>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM6)}</Typography>
        </Grid>
      </Grid>
      <ContainerPlacementActionButton
        label={translate(TranslationKeys.CommonComponents.NEXT_BUTTON)}
        onClick={handleNextClick}
      />
    </ContainerPlacementContent>
  );
};

const placementTipsScreenStyle = () => ({
  listContainer: {
    sx: {
      gap: '.5rem',
      paddingLeft: '.25rem'
    }
  }
});
