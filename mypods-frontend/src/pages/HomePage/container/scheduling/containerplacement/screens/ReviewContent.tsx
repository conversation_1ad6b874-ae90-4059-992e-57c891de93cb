import { Grid, Typography } from '@mui/material';
import Divider from '@mui/material/Divider';
import React, { ReactNode } from 'react';
import { Design } from '../../../../../../helpers/Design';
import { EditButton } from '../../../../../../components/buttons/EditButton';

type ReviewContentTextProps = {
  text: string;
};

export const ReviewContentText = ({ text }: ReviewContentTextProps) => (
  <Typography {...styles.reviewBody}>{text}</Typography>
);

type ReviewContentImageProps = {
  src: string;
  altText: string;
};

export const ReviewContentImage = ({ src, altText }: ReviewContentImageProps) => (
  <Grid {...styles.pseudoCard}>
    <img src={src} alt={altText} height="88px"></img>
  </Grid>
);

type ReviewContentProps = {
  label: string;
  testId: string;
  reviewBody: ReactNode;
  handleEditClick?: () => void;
};

export const ReviewContent = ({
  label,
  testId,
  reviewBody,
  handleEditClick
}: ReviewContentProps) => (
  <>
    <Grid container item {...styles.reviewContent} data-testid={testId}>
      <Grid container item>
        <Grid item flexGrow={1}>
          <Typography {...styles.reviewLabel}>{label}</Typography>
        </Grid>
        {handleEditClick && (
          <Grid item flexGrow={0}>
            <EditButton onClick={handleEditClick} dataTestId={testId} />
          </Grid>
        )}
      </Grid>
      <Grid item>{reviewBody}</Grid>
    </Grid>
    <Divider />
  </>
);

const styles = {
  reviewContent: {
    sx: {
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs,
      paddingY: Design.Primitives.Spacing.sm
    }
  },
  reviewLabel: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      color: Design.Alias.Color.neutral700
    }
  },
  reviewBody: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Lg,
      color: Design.Alias.Color.accent900
    }
  },
  pseudoCard: {
    sx: {
      display: 'flex',
      padding: Design.Primitives.Spacing.sm,
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      width: 'fit-content',
      border: `1px solid ${Design.Alias.Color.neutral300}`,
      borderRadius: Design.Primitives.Spacing.xxxs,
      overflow: 'hidden',
      boxShadow: '0px 4px 8px 0px rgba(0, 0, 0, 0.08)'
    }
  }
};
