import Button from '@mui/material/Button';
import React from 'react';
import { useContainerPlacementContext } from '../context/ContainerPlacementContext';
import { SiteTypeChildScreens } from '../screenmanager/ContainerPlacementModalTypes';
import { getImageForFinalPlacement, getImageForScreen } from '../screenmanager/getImages';
import { FinalContainerPlacement } from '../../../../../../domain/OrderEntities';
import { SiteTypeSelectionWrapper } from './SiteTypeSelectionWrapper';

export type SiteTypeSelectionNonFinalProps = {
  siteTypes: SiteTypeChildScreens[];
  prompt: string;
};

export type SiteTypeSelectionFinalProps = {
  selections: FinalContainerPlacement[];
  prompt: string;
};

export const SiteTypeNonFinal = ({ siteTypes, prompt }: SiteTypeSelectionNonFinalProps) => {
  const { manager } = useContainerPlacementContext();
  const handleOnClick = (screen: SiteTypeChildScreens) => {
    manager.selectChoiceWithChild('anything', screen);
  };

  return (
    <SiteTypeSelectionWrapper prompt={prompt}>
      {siteTypes.map((type) => (
        <Button key={type} onClick={() => handleOnClick(type)}>
          <img alt={type} src={getImageForScreen(type)} />
        </Button>
      ))}
    </SiteTypeSelectionWrapper>
  );
};

export const SiteTypeFinal = ({ selections, prompt }: SiteTypeSelectionFinalProps) => {
  const { setContainerPlacement, manager } = useContainerPlacementContext();
  const handleOnClick = (selection: FinalContainerPlacement) => {
    setContainerPlacement((prev) => ({
      ...prev,
      placement: selection
    }));
    manager.selectChoice(selection);
  };

  return (
    <SiteTypeSelectionWrapper prompt={prompt}>
      {selections.map((type) => (
        <Button key={type} onClick={() => handleOnClick(type)}>
          <img alt={type} src={getImageForFinalPlacement(type)} />
        </Button>
      ))}
    </SiteTypeSelectionWrapper>
  );
};
