import React, { ReactNode } from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { theme } from '../../../../../../PodsTheme';
import { Design } from '../../../../../../helpers/Design';
import { ContainerPlacementHeader } from '../ContainerPlacementHeader';

export const SiteTypeSelectionWrapper = ({
  children,
  prompt
}: {
  children: ReactNode;
  prompt: string;
}) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const style = styles(isMobile);
  return (
    <Grid container gap={isMobile ? 2 : 3}>
      <ContainerPlacementHeader titleKey={prompt} />
      <Grid container gap={2} {...style.images}>
        {children}
      </Grid>
    </Grid>
  );
};
export const styles = (isMobile: boolean) => ({
  question: {
    sx: {
      fontSize: isMobile
        ? Design.Alias.Text.Heading.Mobile.Xl
        : Design.Alias.Text.Heading.Desktop.Lg
    }
  },
  images: {
    sx: {
      justifyContent: 'center',
      alignJustify: 'center',
      width: '100%',
      gap: '16px',
      rowGap: isMobile ? '16px' : '24px',
      '& > .MuiButtonBase-root': {
        padding: 0
      }
    }
  }
});
