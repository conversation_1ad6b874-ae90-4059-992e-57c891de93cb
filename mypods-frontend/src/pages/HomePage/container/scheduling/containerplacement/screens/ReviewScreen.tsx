import { Grid } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ReviewContainerPlacement } from './ReviewContainerPlacement';
import { TranslationKeys } from '../../../../../../locales/TranslationKeys';
import { useContainerPlacementContext } from '../context/ContainerPlacementContext';
import { ContainerPlacementHeader } from '../ContainerPlacementHeader';
import { ContainerPlacementActionButton } from '../ContainerPlacementActionButton';
import { ContainerPlacementContent } from '../ContainerPlacementContent';

const Tx = TranslationKeys.HomePage.ContainerPlacement;

export const ReviewScreen = () => {
  const styles = reviewScreenStyles();
  const { t: translate } = useTranslation();
  const { containerPlacement, handleFinish } = useContainerPlacementContext();

  return (
    <ContainerPlacementContent>
      <ContainerPlacementHeader
        titleKey={Tx.ReviewScreen.TITLE}
        subTitleKey={Tx.ReviewScreen.SUBTITLE}
      />
      <Grid container item {...styles.reviewContainer}>
        <ReviewContainerPlacement />
      </Grid>
      <ContainerPlacementActionButton
        label={translate(Tx.ReviewScreen.FINISH_BUTTON)}
        onClick={() => handleFinish(containerPlacement)}
      />
    </ContainerPlacementContent>
  );
};

const reviewScreenStyles = () => ({
  reviewContainer: {
    sx: {
      flexDirection: 'column'
    }
  }
});
