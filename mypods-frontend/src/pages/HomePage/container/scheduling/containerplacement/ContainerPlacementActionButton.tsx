import { Grid, useMediaQuery } from '@mui/material';
import React from 'react';
import { StretchableLoadingButton } from '../../../../../components/buttons/StretchableLoadingButton';
import { theme } from '../../../../../PodsTheme';

type Props = {
  label: string;
  onClick: () => void;
};

export const ContainerPlacementActionButton = ({ label, onClick }: Props) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Grid container item {...styles.buttonContainer}>
      <StretchableLoadingButton
        isMobile={isMobile}
        label={label}
        customStyles={styles.button}
        onClick={onClick}
      />
    </Grid>
  );
};

const styles = {
  buttonContainer: {
    sx: {
      alignSelf: 'center',
      maxWidth: '400px'
    }
  },
  button: { sx: { height: '56px' } }
};
