import { Grid, Typography } from '@mui/material';
import React from 'react';
import { PickersDayProps } from '@mui/x-date-pickers';
import { Design } from '../../../../helpers/Design';

export type MoveLegDayState = 'hidden' | 'unavailable' | 'available' | 'selected';

interface Props {
  date: Date;
  selectedDay: Date;
  pickersDayProps: PickersDayProps<Date>;
  isAvailable: (date: Date) => boolean;
  onClick: (date: Date) => void;
}

export const DatePickerDay = ({
  date,
  selectedDay,
  pickersDayProps,
  isAvailable,
  onClick
}: Props) => {
  const styles = dayStyles;

  const getState = (): MoveLegDayState => {
    if (pickersDayProps.outsideCurrentMonth) return 'hidden';
    if (date && date.getTime() === selectedDay?.getTime()) return 'selected';
    if (isAvailable(date)) return 'available';
    return 'unavailable';
  };

  const state = getState();
  const handleOnClick = () => {
    if (state !== 'available' && state !== 'selected') return;
    onClick(date);
  };

  const isLabelVisible = () => state !== 'hidden';

  const getStyle = () => {
    switch (state) {
      case 'available':
        return styles.available;
      case 'selected':
        return styles.selected;
      default:
        return null;
    }
  };

  const getLabelStyle = () => {
    switch (state) {
      case 'unavailable':
        return styles.unavailableLabel;
      case 'selected':
        return styles.selectedLabel;
      default:
        return null;
    }
  };

  return (
    <Grid {...styles.day} {...getStyle()} onClick={handleOnClick}>
      {isLabelVisible() && <Typography {...getLabelStyle()}>{date.getDate()}</Typography>}
    </Grid>
  );
};

const dayStyles = {
  day: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '34px',
    height: '34px',
    margin: '3px',
    padding: '3px',
    bgcolor: Design.Alias.Color.neutral100,
    sx: {
      cursor: 'default'
    }
  },
  available: {
    sx: {
      cursor: 'pointer'
    }
  },
  selected: {
    bgcolor: Design.Alias.Color.secondary500,
    borderRadius: '17px',
    sx: {
      cursor: 'pointer'
    }
  },
  label: {
    ...Design.Alias.Text.BodyUniversal.Sm,
    textAlign: 'center'
  },
  unavailableLabel: {
    color: Design.Alias.Color.neutral200
  },
  selectedLabel: {
    color: Design.Alias.Color.neutral100
  }
};
