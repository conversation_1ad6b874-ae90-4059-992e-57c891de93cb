import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  isNullOrEmpty,
  isValidCountryCode,
  isValidState
} from '../../../../helpers/validation/Validators';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { MoveLegAddress, ServiceAddress } from '../../../../domain/OrderEntities';
import { PlaceAddress } from '../../../../helpers/googlePlace/getAddressFromPlace';

export interface IMoveLegAddressState {
  getValueFromPlaceAddress: (place: PlaceAddress) => ServiceAddress;
  setAddress1: (updatedValue: string) => void;
  setAddress2: (updatedValue: string) => void;
  setPostalCode: (updatedValue: string) => void;
  setCity: (updatedValue: string) => void;
  setState: (updatedValue: string) => void;
  displayStreetAddressError: () => void;
  displayPostalCodeError: () => void;
  displayCityError: () => void;
  displayStateError: () => void;
  displayErrorForAllFields: () => void;
  clearErrors: () => void;
  value: ServiceAddress;
  setValue: (updateValue: ServiceAddress) => void;
  isValid: () => boolean;
  postalCodesInServiceArea: Set<string>;
  addInServiceArea: (postalCode: string) => void;
  removeInServiceArea: (postalCode: string) => void;
  isInSameServiceArea: (postalCode: string | undefined) => boolean;
  error: {
    streetAddress: string | undefined;
    city: string | undefined;
    postalCode: string | undefined;
    state: string | undefined;
  };
}

export const toServiceAddress = (address: MoveLegAddress): ServiceAddress => ({
  address1: address.address1 ?? '',
  address2: address.address2 ?? '',
  city: address.city ?? '',
  country: address.country ?? '',
  postalCode: address.postalCode ?? '',
  state: address.state ?? ''
});

export const useMoveLegAddressState = (initialValue: MoveLegAddress) => {
  const { t: translate } = useTranslation();
  const [value, setValue] = useState<ServiceAddress>(toServiceAddress(initialValue));
  const [postalCodesInServiceArea, setPostalCodesInServiceArea] = useState<Set<string>>(
    new Set([initialValue.postalCode!!])
  );

  const getValueFromPlaceAddress = (placeAddress: PlaceAddress) => {
    const result: ServiceAddress = { ...value };
    if (placeAddress?.streetAddress != null) {
      result.address1 = placeAddress.streetAddress;
    }
    if (placeAddress?.city != null) {
      result.city = placeAddress.city;
    }
    if (placeAddress?.state != null) {
      result.state = placeAddress.state;
    }
    if (placeAddress?.postalCode != null) {
      result.postalCode = placeAddress.postalCode;
    }
    return result;
  };

  const setAddress1 = (updatedValue: string) => {
    setValue({ ...value, address1: updatedValue });
  };
  const setAddress2 = (updatedValue: string) => {
    setValue({ ...value, address2: updatedValue });
  };
  const setPostalCode = (updatedValue: string) => {
    setValue({ ...value, postalCode: updatedValue });
  };
  const setCity = (updatedValue: string) => {
    setValue({ ...value, city: updatedValue });
  };
  const setState = (updatedValue: string) => {
    setValue({ ...value, state: updatedValue });
  };

  // region Validation Errors

  const [streetAddressError, setStreetAddressError] = useState<string | undefined>();
  const [postalCodeError, setPostalCodeError] = useState<string | undefined>();
  const [cityError, setCityError] = useState<string | undefined>();
  const [stateError, setStateError] = useState<string | undefined>();

  const displayStreetAddressError = () => {
    if (isNullOrEmpty(value.address1))
      setStreetAddressError(translate(TranslationKeys.CommonComponents.Input.Error.REQUIRED));
    else {
      setStreetAddressError(undefined);
    }
  };

  const displayPostalCodeError = () => {
    if (isNullOrEmpty(value.postalCode))
      setPostalCodeError(translate(TranslationKeys.CommonComponents.Input.Error.REQUIRED));
    else {
      setPostalCodeError(undefined);
    }
  };

  const displayCityError = () => {
    if (isNullOrEmpty(value.city))
      setCityError(translate(TranslationKeys.CommonComponents.Input.Error.REQUIRED));
    else {
      setCityError(undefined);
    }
  };

  const displayStateError = () => {
    if (isNullOrEmpty(value.state))
      setStateError(translate(TranslationKeys.CommonComponents.Input.Error.REQUIRED));
    else if (!isValidState(value.state)) {
      setStateError(translate(TranslationKeys.CommonComponents.Input.Error.STATE_LENGTH));
    } else {
      setStateError(undefined);
    }
  };

  const displayErrorForAllFields = () => {
    displayStreetAddressError();
    displayPostalCodeError();
    displayCityError();
    displayStateError();
  };

  const clearErrors = () => {
    setStreetAddressError(undefined);
    setPostalCodeError(undefined);
    setCityError(undefined);
    setStateError(undefined);
  };

  // endregion Validation Errors

  const isValid = () => {
    if (isNullOrEmpty(value.address1)) return false;
    if (isNullOrEmpty(value.postalCode)) return false;
    if (isNullOrEmpty(value.city)) return false;
    if (!isValidState(value.state)) return false;
    if (!isValidCountryCode(value.country)) return false;
    return isInSameServiceArea(value.postalCode);
  };

  const addInServiceArea = (postalCode: string) => {
    const updated = new Set(postalCodesInServiceArea);
    updated.add(postalCode);
    setPostalCodesInServiceArea(updated);
  };

  const removeInServiceArea = (postalCode: string) => {
    const updated = new Set(postalCodesInServiceArea);
    updated.delete(postalCode);
    setPostalCodesInServiceArea(updated);
  };

  const isInSameServiceArea = (postalCode: string | undefined) => {
    if (!postalCode) return false;
    return postalCodesInServiceArea.has(postalCode);
  };

  return <IMoveLegAddressState>{
    getValueFromPlaceAddress,
    setAddress1,
    setAddress2,
    setPostalCode,
    setCity,
    setState,
    displayStreetAddressError,
    displayPostalCodeError,
    displayCityError,
    displayStateError,
    displayErrorForAllFields,
    clearErrors,
    value,
    setValue,
    isValid,
    postalCodesInServiceArea,
    addInServiceArea,
    removeInServiceArea,
    isInSameServiceArea,
    error: {
      streetAddress: streetAddressError,
      postalCode: postalCodeError,
      city: cityError,
      state: stateError
    }
  };
};
