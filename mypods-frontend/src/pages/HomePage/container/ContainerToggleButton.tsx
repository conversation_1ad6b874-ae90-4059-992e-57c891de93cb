import { useTranslation } from 'react-i18next';
import { ButtonBase, Grid, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { RightChevronIcon } from '../../../components/icons/RightChevronIcon';
import { Design } from '../../../helpers/Design';
import { theme } from '../../../PodsTheme';

interface Props {
  onClick: () => void;
  isExpanded: boolean;
  hideTranslationKey: string;
  showTranslationKey: string;
}

export const ContainerToggleButton: React.FC<Props> = ({
  onClick,
  isExpanded,
  hideTranslationKey,
  showTranslationKey
}: Props) => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = containerToggleStyle(isMobile, isExpanded);

  return (
    <ButtonBase disableRipple onClick={onClick}>
      <Grid container {...styles.showHistorySummary}>
        <RightChevronIcon {...styles.icon} />
        <Typography variant="accordionSummary" {...styles.showHistoryText}>
          {isExpanded ? translate(hideTranslationKey) : translate(showTranslationKey)}
        </Typography>
      </Grid>
    </ButtonBase>
  );
};

const containerToggleStyle = (isMobile: boolean, showHistory: boolean) => ({
  showHistorySummary: {
    sx: {
      flexDirection: 'row',
      paddingLeft: isMobile ? '17px' : '38px',
      paddingRight: Design.Primitives.Spacing.lgPlus,
      paddingY: '11px',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  icon: {
    sx: {
      width: Design.Primitives.Spacing.sm,
      height: Design.Primitives.Spacing.sm,
      transform: showHistory ? 'rotate(-90deg)' : 'rotate(0deg)'
    }
  },
  showHistoryText: {
    sx: { marginLeft: Design.Primitives.Spacing.xxs }
  }
});
