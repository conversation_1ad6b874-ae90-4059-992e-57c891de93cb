import { QueryClientProvider } from '@tanstack/react-query';
import { renderHook } from '@testing-library/react';
import { Suspense } from 'react';
import { runPendingPromises, testQueryClient } from '../../../../testUtils/RenderHelpers';
import { useMoveLegDateState } from '../scheduling/useMoveLegDateState';
import { MoveLeg } from '../../../../domain/OrderEntities';
import { createMoveLeg } from '../../../../testUtils/MyPodsFactories';
import { it } from 'vitest';
import addYears from 'date-fns/addYears';
import { addDays, addWeeks, format, startOfDay } from 'date-fns';
import { mockGetContainerAvailability } from '../../../../../setupTests';

describe('useMoveLegDateState', () => {
  var moveLeg: MoveLeg;

  const renderUseMoveLegDateState = async () => {
    moveLeg = moveLeg ?? createMoveLeg();
    // @ts-ignore
    const wrapper = ({ children }) => (
      <QueryClientProvider client={testQueryClient()}>
        <Suspense fallback={'SUSPENSE_FALLBACK'}>{children}</Suspense>
      </QueryClientProvider>
    );
    const { result } = renderHook(() => useMoveLegDateState(moveLeg), { wrapper });
    await runPendingPromises();
    return result.current;
  };

  it('should return the correct initial state', async () => {
    const state = await renderUseMoveLegDateState();

    expect(state.selectedDate).toEqual(moveLeg.scheduledDate);
    expect(state.containerAvailabilityPending).toBe(false);
    expect(state.selectedDateIsValid()).toBe(true);
    expect(state.selectedDateIsDifferent()).toBe(false);
  });

  describe('getCalendarStartDate', () => {
    it('should return the firstAvailableDate, when it is after the defaultEarliestAvailability', async () => {
      moveLeg = createMoveLeg({
        firstAvailableDate: addYears(new Date(), 1)
      });
      const state = await renderUseMoveLegDateState();
      expect(state.getCalendarStartDate()).toEqual(moveLeg.firstAvailableDate);
    });

    it('should return the defaultEarliestAvailability (the next business day), when it is after the firstAvailableDate and earlier than 5', async () => {
      vi.setSystemTime(new Date(2025, 0, 1, 1));

      moveLeg = createMoveLeg({
        firstAvailableDate: addDays(startOfDay(new Date()), -1)
      });

      const state = await renderUseMoveLegDateState();

      expect(state.getCalendarStartDate()).toEqual(addDays(startOfDay(new Date()), 1));
    });

    it('should return the defaultEarliestAvailability (the next business day), when it is after the firstAvailableDate and later than 5', async () => {
      vi.setSystemTime(new Date(2025, 0, 1, 23, 1));
      moveLeg = createMoveLeg({
        firstAvailableDate: addDays(startOfDay(new Date()), -1)
      });

      const state = await renderUseMoveLegDateState();

      expect(state.getCalendarStartDate()).toEqual(addDays(startOfDay(new Date()), 2));
    });
  });

  describe('getCalendarStartDateWithAvailability', () => {
    let today: Date;
    let todayFormatted: string;

    beforeEach(() => {
      vi.setSystemTime(new Date(2025, 0, 1, 1));
      today = startOfDay(new Date());
      todayFormatted = format(today, 'yyyy-MM-dd');
    });

    it('should return the result of getCalendarStartDate, when there is no availability', async () => {
      const mockRequest = {
        date: todayFormatted,
        postalCode: '12345',
        orderType: 'LOCAL',
        moveLegType: 'INITIAL_DELIVERY' as const,
        siteIdentity: 'test'
      };

      moveLeg = createMoveLeg({
        firstAvailableDate: addWeeks(new Date(), 1)
      });
      const state = await renderUseMoveLegDateState();
      expect(state.getCalendarStartDateWithAvailability()).toEqual(moveLeg.firstAvailableDate);
    });

    it.skip('should return the first available date, when there is availability', async () => {
      // in order to test this, we'd need to mock out the useGetContainerAvailability hook,
      // which is kind of a heavy lift.  So I'm leaving the test here for documentation.
      const tomorrowFormatted = format(addDays(today, 1), 'yyyy-MM-dd');
      const twoDaysFromNowFormatted = format(addDays(today, 2), 'yyyy-MM-dd');
      const threeDaysFromNowFormatted = format(addDays(today, 3), 'yyyy-MM-dd');

      moveLeg = createMoveLeg({
        firstAvailableDate: today
      });

      mockGetContainerAvailability.mockResolvedValue({
        eightFootAvailability: [
          { date: todayFormatted, isAvailable: false },
          { date: tomorrowFormatted, isAvailable: false },
          { date: twoDaysFromNowFormatted, isAvailable: false },
          { date: threeDaysFromNowFormatted, isAvailable: true }
        ],
        twelveFootAvailability: [],
        sixteenFootAvailability: []
      });

      moveLeg = createMoveLeg({
        firstAvailableDate: addDays(today, 1)
      });

      const state = await renderUseMoveLegDateState();

      state.addContainerAvailabilitiesFor3Months(today);

      expect(state.getCalendarStartDateWithAvailability()).toEqual(addDays(today, 3));
    });
  });
});
