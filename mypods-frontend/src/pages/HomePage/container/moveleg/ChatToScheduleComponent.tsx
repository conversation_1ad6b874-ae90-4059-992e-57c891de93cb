import React from 'react';
import { Grid } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../helpers/Design';
import { launchLiveChat } from '../../../../chat/LiveChat';
import { SMSButton } from '../../../../components/buttons/SmsButton';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { ChatButton } from '../../../../components/buttons/ChatButton';
import { useRefreshSession } from '../../../../networkRequests/queries/useRefreshSession';

export const ChatToScheduleComponent = () => {
  const { t: translate } = useTranslation();
  const styles = componentStyles();
  const SMS_NUMBER = '697637';
  const SMS_BODY = 'I need to make an update to my order.';
  const { sessionClaims } = useRefreshSession();

  const handleChatButtonClick = () => {
    launchLiveChat(sessionClaims);
  };

  return (
    <Grid container {...styles.container}>
      <Grid container item gap={Design.Primitives.Spacing.xxs}>
        <ChatButton onClick={handleChatButtonClick}>
          {translate(TranslationKeys.HomePage.MoveLegs.Title.CHAT_BUTTON)}
        </ChatButton>

        <SMSButton SMSNumber={SMS_NUMBER} SMSBody={SMS_BODY}>
          {translate(TranslationKeys.HomePage.MoveLegs.Title.SMS_BUTTON, { number: SMS_NUMBER })}
        </SMSButton>
      </Grid>
    </Grid>
  );
};

const componentStyles = () => ({
  container: {
    justifyContent: 'row',
    rowGap: Design.Primitives.Spacing.xxxs
  }
});
