import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useContext } from 'react';
import Divider from '@mui/material/Divider';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../../helpers/Design';
import { ContainerProgressLine } from '../../ContainerProgressLine';
import { ScheduledStatus } from '../../../../../domain/OrderEntities';
import { theme } from '../../../../../PodsTheme';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { ServiceCountdownAlert } from '../../serviceCountdown/ServiceCountdownAlert';
import { MoveLegContext } from '../MoveLegContext';
import { TransitLegDescription } from '../TransitLegDescription';

const Tx = TranslationKeys.HomePage.MoveLegs.UpcomingMoveLeg;

export const UpcomingMoveLegSection = () => {
  const { moveLeg, isLastMoveLeg, isLastRenderedMoveLeg, title } = useContext(MoveLegContext);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = moveLegStyles(isMobile, moveLeg.scheduledStatus);
  const { t: translate } = useTranslation();

  const getUpcomingMoveLegDescription = () => {
    const descriptionPrefix = translate(Tx.DESCRIPTION_PREFIX);
    switch (moveLeg.moveLegType) {
      case 'REDELIVERY':
        return descriptionPrefix + translate(Tx.MoveLegSuffix.REDELIVERY);
      case 'SELF_INITIAL_DELIVERY':
        return descriptionPrefix + translate(Tx.MoveLegSuffix.SELF_INITIAL_DELIVERY);
      case 'SELF_FINAL_PICKUP':
      case 'FINAL_PICKUP':
        return descriptionPrefix + translate(Tx.MoveLegSuffix.FINAL_PICK_UP);
      default:
        return translate(Tx.DESCRIPTION_GENERIC);
    }
  };

  return (
    <Grid
      container
      {...styles.moveLegSection}
      data-testid={`upcoming-move-leg-${moveLeg.moveLegId}`}>
      <Grid item>
        <ContainerProgressLine
          variant="FADED_DASHED"
          isUpNext={moveLeg.isUpNext}
          isFinal={isLastMoveLeg}
          dataTestId={`progress-line-${moveLeg.moveLegId}`}
        />
      </Grid>
      <ServiceCountdownAlert moveLeg={moveLeg} />

      <Grid item xs {...styles.mainBody}>
        <Grid container {...styles.titleContainer} data-testid="upcoming-move-leg-title-container">
          <Typography
            color="inherit"
            variant="h4"
            {...styles.titleText}
            data-testid="move-leg-title">
            {title}
          </Typography>
        </Grid>
        <Grid>
          {moveLeg.isTransitLeg ? (
            <TransitLegDescription />
          ) : (
            <Typography
              color="inherit"
              {...styles.descriptionText}
              data-testid="upcoming-move-leg-description">
              {getUpcomingMoveLegDescription()}
            </Typography>
          )}
        </Grid>
        {isLastRenderedMoveLeg ? (
          <Grid container {...styles.dividerContainer}>
            <Divider />
          </Grid>
        ) : (
          <Grid container style={{ height: '24px' }} />
        )}
      </Grid>
    </Grid>
  );
};

const moveLegStyles = (isMobile: boolean, status: ScheduledStatus) => {
  const getMoveLegTextColor = (futureMoveLegColor: string) => {
    switch (status) {
      case 'PAST':
        return Design.Alias.Color.neutral600;
      case 'UNSCHEDULED':
      case 'FUTURE':
        return futureMoveLegColor;
    }
  };
  return {
    moveLegSection: {
      sx: {
        flexDirection: 'row',
        columnGap: Design.Primitives.Spacing.xxs
      }
    },
    titleContainer: {
      sx: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Design.Primitives.Spacing.xxs
      }
    },
    titleText: {
      sx: {
        paddingBottom: 0,
        color: getMoveLegTextColor('#0E2E3B') // TODO: change color or add to design theme
      }
    },
    descriptionText: {
      sx: {
        ...Design.Alias.Text.BodyUniversal.Xs
      }
    },
    mainBody: {
      sx: {
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        gap: Design.Primitives.Spacing.xxs,
        marginTop: '-5px'
      }
    },
    dividerContainer: {
      sx: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        paddingRight: '8px',
        height: isMobile ? '64px' : '72px'
      }
    }
  };
};
