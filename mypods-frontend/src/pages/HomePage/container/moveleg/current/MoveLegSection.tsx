import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useMemo, useState } from 'react';
import Divider from '@mui/material/Divider';
import { addDays } from 'date-fns';
import { ServiceCountdownAlert } from '../../serviceCountdown/ServiceCountdownAlert';
import { DateComponent } from '../DateComponent';
import { formatAddress } from '../../../../../networkRequests/responseEntities/CustomerEntities';
import { ContainerProgressLine, ProgressLineVariant } from '../../ContainerProgressLine';
import { ScheduleMoveLegButton } from './ScheduleMoveLegButton';
import { ScheduledStatus } from '../../../../../domain/OrderEntities';
import { ScheduleMoveLeg } from '../../scheduling/ScheduleMoveLeg';
import { theme } from '../../../../../PodsTheme';
import { InTransitMoveLeg } from './InTransitMoveLeg';
import { AddressComponent } from '../AddressComponent';
import { EditButton } from '../../../../../components/buttons/EditButton';
import { isWithin24Hours } from '../../../../../helpers/dateHelpers';
import { getDateLabels } from '../../../../../locales/TranslationConstants';
import { VisitContainerSection } from '../VisitContainerSection';
import useMoveLegContext from '../MoveLegContext';
import { sharedMoveLegStyles } from '../sharedMoveLegStyles';
import useContainerContext from '../../../../../context/ContainerContext';
import { ReviewInitialDeliveryAlert } from '../../../../../components/alert/ReviewInitialDeliveryAlert';
import useSingleOrderContext from '../../../../../context/SingleOrderContext';
import { useGtmEvents } from '../../../../../config/google/useGtmEvents';
import { PickupWindow } from '../PickupWindow';
import {
  ORDER_MODIFICATION_ENABLED,
  REDESIGNED_SCHEDULING,
  useFeatureFlags
} from '../../../../../helpers/useFeatureFlags';
import { ChatToScheduleComponent } from '../ChatToScheduleComponent';
import { ManageMoveContainer } from '../../../Scheduling/ManageMoveContainer';
import { ManageVisitContainer } from '../../../Scheduling/ManageVisitContainer';
import { ManageDropOffContainer } from '../../../Scheduling/ManageDropOffContainer';

export const MoveLegSection = () => {
  const { moveLeg, isLastMoveLeg, isLastRenderedMoveLeg, title } = useMoveLegContext();
  const { order, container } = useContainerContext();
  const { moveLegScheduling } = useSingleOrderContext();
  const { isReady, isOrderModEnabled } = useFeatureFlags([ORDER_MODIFICATION_ENABLED]);
  const [isMoveDrawerOpen, setIsMoveDrawerOpen] = useState(false);
  const [isVisitDrawerOpen, setIsVisitDrawerOpen] = useState(false);
  const [isDropOffDrawerOpen, setIsDropOffDrawerOpen] = useState(false);
  const orderModEnabled = useMemo(
    () => isReady && isOrderModEnabled(),
    [isReady, isOrderModEnabled]
  );
  const { isRedesignedSchedulingEnabled } = useFeatureFlags([REDESIGNED_SCHEDULING]);

  const {
    currentlySelectedMoveLeg,
    editMoveLegScheduling,
    stopMoveLegScheduling,
    isSaving,
    isCancelling
  } = moveLegScheduling;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = moveLegStyles(isMobile);
  const isUnscheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate == null;
  const isScheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate != null;
  const isSchedulingMoveLeg = currentlySelectedMoveLeg === moveLeg && !isCancelling;
  const { startSchedule, startEditSchedule } = useGtmEvents();
  const [showInitialDeliveryAlert, setShowInitialDeliveryAlert] = useState<boolean>(
    moveLeg.moveLegType === 'INITIAL_DELIVERY' && !order.initialDeliveryPlacementIsReviewed
  );
  const [isEditingGtmEvent, setIsEditingGtmEvent] = useState<boolean>(false);

  const toggleEditMoveLegOn = () => {
    editMoveLegScheduling(moveLeg);
  };

  const hideInitialDeliveryAlert = () => {
    setShowInitialDeliveryAlert(false);
  };

  const gtmSchedulePayLoad = {
    transactionId: order.orderId,
    moveLegId: moveLeg.moveLegId,
    moveLegType: moveLeg.moveLegType.toString(),
    containerId: container.containerId
  };

  function isCallToScheduleLeg(): boolean {
    return (
      moveLeg.moveLegType === 'WAREHOUSE_TO_WAREHOUSE' &&
      moveLeg.scheduledStatus === 'UNSCHEDULED' &&
      !isSchedulableButNotOnline()
    );
  }

  const scheduledStatusToProgressLineVariant = (
    scheduledStatus: ScheduledStatus
  ): ProgressLineVariant => {
    if (scheduledStatus === 'UNSCHEDULED') {
      return 'FADED_DASHED';
    }
    return 'DASHED';
  };

  const shouldRenderScheduledButton = () => {
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.isCityService) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    if (isUnscheduledContainerVisit) return true;
    return moveLeg.scheduledStatus === 'UNSCHEDULED';
  };

  const shouldRenderEditButton = () => {
    if (isSchedulingMoveLeg) return false;
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') return false;
    if (moveLeg.scheduledStatus !== 'FUTURE') return false;
    if (isWithin24Hours(moveLeg.scheduledDate)) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    return !moveLeg.isCityService;
  };

  const isSchedulableButNotOnline = () => {
    if (moveLeg.isTransitLeg) return false;
    if (moveLeg.isCrossBorder) return true;
    if (moveLeg.isHawaii) return true;
    return moveLeg.isCityService;
  };

  // TODO: move dateLabels and dateValue logic in one location
  const dateLabels = getDateLabels(moveLeg.moveLegType);

  function getFirstDateValue() {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.arrivalDate;
    }
    return moveLeg.scheduledDate;
  }

  const getSecondDateValue = () => {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.moveDate;
    }
    if (moveLeg.scheduledDate) {
      return addDays(moveLeg.scheduledDate, moveLeg.transitDays);
    }
    return undefined;
  };

  if (moveLeg.isTransitLeg) return <InTransitMoveLeg />;

  return (
    <Grid container {...styles.moveLegSection} data-testid={`move-leg-${moveLeg.moveLegId}`}>
      <Grid item>
        <ContainerProgressLine
          variant={scheduledStatusToProgressLineVariant(moveLeg.scheduledStatus)}
          isUpNext={moveLeg.isUpNext}
          isFinal={isLastMoveLeg}
          dataTestId={`progress-line-${moveLeg.moveLegId}`}
        />
      </Grid>
      <Grid item xs {...styles.mainBody}>
        <Grid container {...styles.titleContainer} data-testid="move-leg-title-container">
          <Grid container item xs {...styles.titleWithEdit}>
            <Typography
              color="inherit"
              variant="h4"
              {...styles.titleText}
              data-testid="move-leg-title">
              {title}
            </Typography>
          </Grid>
          {shouldRenderEditButton() && orderModEnabled && (
            <EditButton
              dataTestId="move-leg"
              onClick={() => {
                setIsEditingGtmEvent(true);
                startEditSchedule(gtmSchedulePayLoad);
                toggleEditMoveLegOn();
              }}
              disabled={isSaving}
            />
          )}
        </Grid>
        {isSchedulableButNotOnline() && <ChatToScheduleComponent />}
        <ServiceCountdownAlert moveLeg={moveLeg} />
        {showInitialDeliveryAlert && (
          <ReviewInitialDeliveryAlert hideAlertCallback={hideInitialDeliveryAlert} />
        )}
        <Grid container {...styles.detailsContainer}>
          <Grid container {...styles.detailsContainer}>
            <DateComponent
              firstDateLabel={dateLabels.firstDateLabel}
              firstDateValue={getFirstDateValue()}
              secondDateLabel={dateLabels.secondDateLabel}
              secondDateValue={getSecondDateValue()}
            />
            <AddressComponent moveLeg={moveLeg} address={formatAddress(moveLeg.displayAddress)} />
          </Grid>
          {moveLeg.moveLegType === 'SELF_FINAL_PICKUP' && moveLeg.scheduledDate && (
            <PickupWindow etaWindow={moveLeg.eta} />
          )}
          {isSchedulingMoveLeg ? (
            <ScheduleMoveLeg
              onStopScheduling={stopMoveLegScheduling}
              isEditingGtmEvent={isEditingGtmEvent}
            />
          ) : (
            shouldRenderScheduledButton() &&
            orderModEnabled && (
              <ScheduleMoveLegButton
                moveLeg={moveLeg}
                onClick={() => {
                  setIsEditingGtmEvent(false);
                  startSchedule(gtmSchedulePayLoad);
                  if (isRedesignedSchedulingEnabled()) {
                    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
                      setIsVisitDrawerOpen(true);
                    } else if (moveLeg.moveLegType === 'REDELIVERY') {
                      setIsDropOffDrawerOpen(true);
                    } else {
                      setIsMoveDrawerOpen(true);
                    }
                  } else {
                    toggleEditMoveLegOn();
                  }
                }}
                disabled={isSaving}
              />
            )
          )}
          {(isCallToScheduleLeg() || !orderModEnabled) && <ChatToScheduleComponent />}
        </Grid>
        {/* // TODO Test that it should be schedulable online */}
        {isScheduledContainerVisit && moveLeg.isSchedulableOnline && !isSchedulingMoveLeg && (
          <VisitContainerSection
            moveLeg={moveLeg}
            onEdit={() => {
              setIsEditingGtmEvent(true);
              startEditSchedule(gtmSchedulePayLoad);
              toggleEditMoveLegOn();
            }}
          />
        )}

        {isLastRenderedMoveLeg ? (
          <Grid container {...styles.dividerContainer}>
            <Divider />
          </Grid>
        ) : (
          <Grid container style={{ height: '24px' }} />
        )}
        <ManageMoveContainer
          isOpen={isMoveDrawerOpen}
          onClose={() => setIsMoveDrawerOpen(false)}
          moveLeg={moveLeg}
        />
        <ManageVisitContainer
          isOpen={isVisitDrawerOpen}
          onClose={() => setIsVisitDrawerOpen(false)}
          moveLeg={moveLeg}
        />
        <ManageDropOffContainer
          isOpen={isDropOffDrawerOpen}
          onClose={() => setIsDropOffDrawerOpen(false)}
          moveLeg={moveLeg}
        />
      </Grid>
    </Grid>
  );
};

const moveLegStyles = (isMobile: boolean) => ({
  ...sharedMoveLegStyles(isMobile),
  titleWithEdit: {
    sx: {
      flexDirection: 'row',
      alignItems: 'center'
    }
  }
});
