import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useContext } from 'react';
import Divider from '@mui/material/Divider';
import { ContainerProgressLine, ProgressLineVariant } from '../../ContainerProgressLine';
import { ServiceCountdownAlert } from '../../serviceCountdown/ServiceCountdownAlert';
import { ScheduledStatus } from '../../../../../domain/OrderEntities';
import { theme } from '../../../../../PodsTheme';
import { MoveLegContext } from '../MoveLegContext';
import { TransitLegDescription } from '../TransitLegDescription';
import { sharedMoveLegStyles } from '../sharedMoveLegStyles';

export const InTransitMoveLeg = () => {
  const { moveLeg, isLastMoveLeg, isLastRenderedMoveLeg, title } = useContext(MoveLegContext);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = inTransitStyles(isMobile);

  const scheduledStatusToProgressLineVariant = (
    scheduledStatus: ScheduledStatus
  ): ProgressLineVariant => {
    if (scheduledStatus === 'UNSCHEDULED') {
      return 'FADED_DASHED';
    }
    return 'DASHED';
  };

  return (
    <Grid container {...styles.moveLegSection} data-testid={`move-leg-${moveLeg.moveLegId}`}>
      <Grid item>
        <ContainerProgressLine
          variant={scheduledStatusToProgressLineVariant(moveLeg.scheduledStatus)}
          isUpNext={moveLeg.isUpNext}
          isFinal={isLastMoveLeg}
          dataTestId={`progress-line-${moveLeg.moveLegId}`}
        />
      </Grid>
      <Grid item xs {...styles.mainBody}>
        <Grid container {...styles.titleContainer} data-testid="move-leg-title-container">
          <Grid container item xs>
            <Typography
              color="inherit"
              variant="h4"
              {...styles.titleText}
              data-testid="move-leg-title">
              {title}
            </Typography>
          </Grid>
        </Grid>
        <ServiceCountdownAlert moveLeg={moveLeg} />
        <TransitLegDescription />
        {isLastRenderedMoveLeg ? (
          <Grid container {...styles.dividerContainer}>
            <Divider />
          </Grid>
        ) : (
          <Grid container style={{ height: '24px' }} />
        )}
      </Grid>
    </Grid>
  );
};

const inTransitStyles = (isMobile: boolean) => ({
  ...sharedMoveLegStyles(isMobile)
});
