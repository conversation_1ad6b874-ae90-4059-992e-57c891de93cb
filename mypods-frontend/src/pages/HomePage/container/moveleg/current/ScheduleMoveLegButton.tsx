import Button from '@mui/material/Button';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../../helpers/Design';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { MoveLeg } from '../../../../../domain/OrderEntities';
import { toCamelCase } from '../../../../../helpers/stringHelper';

type ScheduleMoveLegButtonProps = {
  moveLeg: MoveLeg;
  onClick: () => void;
  disabled: boolean;
};

export const ScheduleMoveLegButton = ({
  moveLeg,
  onClick,
  disabled = false
}: ScheduleMoveLegButtonProps) => {
  const Tx = TranslationKeys.HomePage.MoveLegs;
  const styles = buttonStyles(moveLeg);
  const { t: translate } = useTranslation();

  const getScheduleButtonText = () =>
    translate(Tx.SCHEDULE_BUTTON, {
      context: toCamelCase(moveLeg.moveLegType.toString())
    });

  return (
    <Button
      fullWidth
      color="inherit"
      {...styles.button}
      onClick={onClick}
      disabled={disabled}
      data-testid="schedule-move-leg-button">
      {getScheduleButtonText()}
    </Button>
  );
};
const buttonStyles = (moveLeg: MoveLeg) => {
  const { moveLegType } = moveLeg;
  const isContainerVisit = moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate == null;
  let containerVisitStyles = {};
  if (isContainerVisit) {
    containerVisitStyles = {
      color: Design.Alias.Color.secondary500,
      backgroundColor: Design.Alias.Color.neutral100,
      borderRadius: '4px',
      border: `1px solid ${Design.Alias.Color.secondary500}`,
      ':hover': {
        color: Design.Alias.Color.neutral100,
        backgroundColor: Design.Alias.Color.secondary400
      }
    };
  }
  return {
    button: {
      sx: {
        ...Design.Alias.Text.BodyUniversal.SmBold,
        height: Design.Primitives.Spacing.lg,
        color: '#FFF',
        backgroundColor: Design.Alias.Color.secondary500,
        textTransform: 'none',
        ':hover': {
          backgroundColor: Design.Alias.Color.secondary400
        },
        ':active': {
          backgroundColor: Design.Alias.Color.secondary600
        },
        ':disabled': {
          color: Design.Alias.Color.neutral400,
          backgroundColor: 'transparent',
          borderRadius: Design.Primitives.Spacing.xxxs,
          border: `1px solid ${Design.Alias.Color.neutral400}`
        },
        ...containerVisitStyles
      }
    }
  };
};
