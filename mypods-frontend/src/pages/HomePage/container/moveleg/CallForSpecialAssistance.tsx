import { Grid, Typography } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { Design } from '../../../../helpers/Design';

export const CallForSpecialAssistance = () => {
  const { t: translate } = useTranslation();
  const styles = componentStyles();
  return (
    <Grid container>
      <Typography
        {...styles.text}
        dangerouslySetInnerHTML={{
          __html: translate(TranslationKeys.HomePage.MoveLegs.CALL_FOR_SPECIAL_ASSISTANCE)
        }}
      />
    </Grid>
  );
};

const componentStyles = () => ({
  container: {
    justifyContent: 'row',
    rowGap: Design.Primitives.Spacing.xxxs
  },
  text: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Xs,
      a: {
        color: Design.Alias.Color.secondary500,
        fontWeight: '700',
        textDecoration: 'none'
      }
    }
  }
});
