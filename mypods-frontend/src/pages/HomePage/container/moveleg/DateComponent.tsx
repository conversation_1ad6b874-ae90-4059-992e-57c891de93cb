import React from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { Design } from '../../../../helpers/Design';
import { theme } from '../../../../PodsTheme';
import { formatToLocale } from '../../../../helpers/dateHelpers';
import { TranslationKeys } from '../../../../locales/TranslationKeys';

export const DateComponent = ({
  firstDateLabel,
  firstDateValue,
  secondDateLabel,
  secondDateValue
}: {
  firstDateLabel: string;
  firstDateValue?: Date;
  secondDateLabel?: string;
  secondDateValue?: Date;
}) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t: translate } = useTranslation();
  const Tx = TranslationKeys.HomePage.MoveLegs.Scheduling.DateLabels;

  return (
    <Grid
      container
      item
      flexDirection="column"
      sx={{ width: '150px' }}
      gap={isMobile ? Design.Primitives.Spacing.xxs : Design.Primitives.Spacing.md}>
      <Grid item>
        <Typography
          color={firstDateValue == null ? Design.Alias.Color.neutral600 : 'inherit'}
          variant="subtitle2"
          sx={{ fontWeight: Design.Alias.Text.BodyUniversal.XsBold }}>
          {translate(firstDateLabel)}
        </Typography>
        <Typography
          color={firstDateValue == null ? Design.Alias.Color.neutral600 : 'inherit'}
          variant="subtitle1">
          {firstDateValue ? formatToLocale(firstDateValue) : translate(Tx.UNSCHEDULED_DATE)}
        </Typography>
      </Grid>
      {secondDateLabel && (
        <Grid item>
          <Typography
            color={secondDateValue == null ? Design.Alias.Color.neutral600 : 'inherit'}
            variant="subtitle2"
            sx={{ fontWeight: Design.Alias.Text.BodyUniversal.XsBold }}>
            {translate(secondDateLabel)}
          </Typography>
          <Typography
            color={secondDateValue == null ? Design.Alias.Color.neutral600 : 'inherit'}
            variant="subtitle1">
            {secondDateValue ? formatToLocale(secondDateValue) : translate(Tx.UNSCHEDULED_DATE)}
          </Typography>
        </Grid>
      )}
    </Grid>
  );
};
