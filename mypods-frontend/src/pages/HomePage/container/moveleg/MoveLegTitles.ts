import { useTranslation } from 'react-i18next';
import { MoveLeg, ScheduledStatus } from '../../../../domain/OrderEntities';
import { TranslationKeys } from '../../../../locales/TranslationKeys';

const { Title } = TranslationKeys.HomePage.MoveLegs;

export const useGetMoveLegStatusTitle = (moveLeg: MoveLeg) => {
  const { t: translate } = useTranslation();

  // Careful if we rename an enum, this just lowercases the status for the i18n context
  const getStatusContext = (status: ScheduledStatus) => status.toLowerCase();

  const getTitle = () => {
    const status = moveLeg.scheduledStatus;
    const statusContext = getStatusContext(status);

    if (moveLeg.isTransitLeg) return translate(Title.IN_TRANSIT);

    const translateWithScheduledContext = (titleKey: string) =>
      translate(titleKey, { context: statusContext });

    switch (moveLeg.moveLegType) {
      case 'INITIAL_DELIVERY':
        return translateWithScheduledContext(Title.INITIAL_DELIVERY);

      case 'SELF_INITIAL_DELIVERY':
        return translateWithScheduledContext(Title.SELF_INITIAL_DELIVERY);

      case 'PICKUP':
        return translateWithScheduledContext(Title.PICKUP);

      case 'VISIT_CONTAINER':
        return translate(Title.CONTAINER_AT_WAREHOUSE);

      case 'REDELIVERY':
        return translateWithScheduledContext(Title.REDELIVERY);

      case 'MOVE':
        return translateWithScheduledContext(Title.CONTAINER_MOVE_LOCAL);

      case 'WAREHOUSE_TO_WAREHOUSE':
        if (status === 'PAST' || status === 'FUTURE')
          return translate(Title.WAREHOUSE_TO_WAREHOUSE, {
            context: statusContext,
            newCity: moveLeg.displayAddress.city
          });
        return translateWithScheduledContext(Title.WAREHOUSE_TO_WAREHOUSE);

      case 'FINAL_PICKUP':
        return translateWithScheduledContext(Title.FINAL_PICKUP);

      case 'SELF_FINAL_PICKUP':
        return translateWithScheduledContext(Title.SELF_FINAL_PICKUP);

      default:
        return moveLeg.moveLegType.replace(/_/g, ' ');
    }
  };

  return {
    title: getTitle()
  };
};
