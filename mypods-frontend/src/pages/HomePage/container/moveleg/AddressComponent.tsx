import { Grid, Typography } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../helpers/Design';
import { getMoveLegAddressLabelKey } from '../../../../locales/TranslationConstants';
import { MoveLeg } from '../../../../domain/OrderEntities';

export const AddressComponent = ({ moveLeg, address }: { moveLeg: MoveLeg; address: string }) => {
  const { t: translate } = useTranslation();
  const styles = addressStyles();
  return (
    <Grid item {...styles.addressContainer}>
      <Typography color="inherit" variant="subtitle2" {...styles.addressTitle}>
        {translate(getMoveLegAddressLabelKey(moveLeg))}
      </Typography>
      <Typography color="inherit" variant="subtitle1">
        {address}
      </Typography>
    </Grid>
  );
};

const addressStyles = () => ({
  addressContainer: {
    sx: {
      flexDirection: 'column',
      alignSelf: 'stretch',
      flex: 1
    }
  },
  addressTitle: {
    sx: { fontWeight: Design.Alias.Text.BodyUniversal.XsBold.fontWeight }
  }
});
