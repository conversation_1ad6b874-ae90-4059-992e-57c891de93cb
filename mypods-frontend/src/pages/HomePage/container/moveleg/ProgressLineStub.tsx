import { Box, Grid } from '@mui/material';
import React from 'react';
import { Design } from '../../../../helpers/Design';

export const ProgressLineStub = () => {
  const styles = lineStubStyles();

  return (
    <Grid container {...styles.containerOrderDetailsSection}>
      <Grid item {...styles.containerProgressLine}>
        <Box {...styles.trailingLineTop} />
      </Grid>
    </Grid>
  );
};

const lineStubStyles = () => ({
  containerOrderDetailsSection: {
    sx: {
      flexDirection: 'row',
      columnGap: Design.Primitives.Spacing.xxs,
      color: Design.Alias.Color.neutral600
    }
  },
  trailingLineTop: {
    sx: {
      height: '24px',
      width: '50%',
      borderRight: `1px solid ${Design.Alias.Color.neutral600}`
    }
  },
  containerProgressLine: {
    sx: {
      display: 'flex',
      width: '17px',
      flexDirection: 'column',
      flexAlign: 'start',
      height: '100%'
    }
  }
});
