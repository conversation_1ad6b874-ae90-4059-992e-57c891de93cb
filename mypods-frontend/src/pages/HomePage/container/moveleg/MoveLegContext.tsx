import React, { createContext, FC, ReactNode, useContext, useMemo } from 'react';
import { MoveLeg, MoveLegAddress, ServiceAddress } from '../../../../domain/OrderEntities';
import { useGetMoveLegStatusTitle } from './MoveLegTitles';
import { IMoveLegDateState, useMoveLegDateState } from '../scheduling/useMoveLegDateState';
import { IMoveLegAddressState, useMoveLegAddressState } from '../scheduling/useMoveLegAddressState';
import { PlaceAddress } from '../../../../helpers/googlePlace/getAddressFromPlace';

export interface MoveLegContextProps {
  moveLeg: MoveLeg;
  lastMoveLegId: string;
  isLastRenderedMoveLeg: boolean;
  children: ReactNode;
}

export interface IMoveLegContextState {
  moveLeg: MoveLeg;
  isLastMoveLeg: boolean;
  isLastRenderedMoveLeg: boolean;
  title: string;
  scheduling: {
    addressState: IMoveLegAddressState;
    dateState: IMoveLegDateState;
  };
}

const initialState: IMoveLegContextState = {
  moveLeg: {
    moveLegId: '',
    moveLegType: 'INITIAL_DELIVERY',
    siteIdentity: '',
    displayAddress: {
      country: '',
      address1: '',
      city: '',
      state: '',
      postalCode: ''
    },
    originationAddress: {
      country: '',
      address1: '',
      city: '',
      state: '',
      postalCode: ''
    },
    destinationAddress: {
      country: '',
      address1: '',
      city: '',
      state: '',
      postalCode: ''
    },
    isCityService: false,
    isHawaii: false,
    isCrossBorder: false,
    isUpNext: false,
    scheduledStatus: 'FUTURE',
    serviceCountdownType: 'PICKUP',
    isTransitLeg: false,
    transitDays: 0,
    isSchedulableOnline: false,
    firstAvailableDate: new Date(),
    lastAvailableDate: new Date()
  },
  isLastMoveLeg: false,
  isLastRenderedMoveLeg: false,
  title: '',
  scheduling: {
    addressState: {
      getValueFromPlaceAddress(_: PlaceAddress): ServiceAddress {
        throw new Error('Function not implemented.');
      },
      setAddress1(_: string): void {
        throw new Error('Function not implemented.');
      },
      setAddress2(_: string): void {
        throw new Error('Function not implemented.');
      },
      setPostalCode(_: string): void {
        throw new Error('Function not implemented.');
      },
      setCity(_: string): void {
        throw new Error('Function not implemented.');
      },
      setState(_: string): void {
        throw new Error('Function not implemented.');
      },
      displayStreetAddressError(): void {
        throw new Error('Function not implemented.');
      },
      displayPostalCodeError(): void {
        throw new Error('Function not implemented.');
      },
      displayCityError(): void {
        throw new Error('Function not implemented.');
      },
      displayStateError(): void {
        throw new Error('Function not implemented.');
      },
      displayErrorForAllFields(): void {
        throw new Error('Function not implemented.');
      },
      clearErrors(): void {
        throw new Error('Function not implemented.');
      },
      value: {
        country: '',
        address1: '',
        city: '',
        state: '',
        postalCode: ''
      },
      setValue(_: MoveLegAddress): void {
        throw new Error('Function not implemented.');
      },
      isValid(): boolean {
        throw new Error('Function not implemented.');
      },
      postalCodesInServiceArea: new Set(),
      addInServiceArea(_: string): void {
        throw new Error('Function not implemented.');
      },
      removeInServiceArea(_: string): void {
        throw new Error('Function not implemented.');
      },
      isInSameServiceArea(_: string | undefined): boolean {
        throw new Error('Function not implemented.');
      },
      error: {
        streetAddress: undefined,
        city: undefined,
        postalCode: undefined,
        state: undefined
      }
    },
    dateState: {
      selectedDate: null,
      setSelectedDate(_: Date): void {
        throw new Error('Function not implemented.');
      },
      containerAvailabilityPending: false,
      addContainerAvailabilitiesFor3Months(_: Date): void {
        throw new Error('Function not implemented.');
      },
      getCalendarStartDate(): Date {
        throw new Error('Function not implemented.');
      },
      isAvailable(_: Date): boolean {
        throw new Error('Function not implemented.');
      },
      selectedDateIsValid(): boolean {
        throw new Error('Function not implemented.');
      },
      selectedDateIsDifferent(): boolean {
        throw new Error('Function not implemented.');
      },
      getCalendarStartDateWithAvailability(): Date {
        throw new Error('Function not implemented.');
      }
    }
  }
};

export const MoveLegContext = createContext<IMoveLegContextState>(initialState);
export default function useMoveLegContext() {
  return useContext(MoveLegContext);
}

export const MoveLegProvider: FC<MoveLegContextProps> = ({
  moveLeg,
  lastMoveLegId,
  isLastRenderedMoveLeg,
  children
}) => {
  const { title } = useGetMoveLegStatusTitle(moveLeg);
  const dateState = useMoveLegDateState(moveLeg);
  const addressState = useMoveLegAddressState(moveLeg.displayAddress);

  const getState = () => ({
    moveLeg,
    isLastMoveLeg: moveLeg.moveLegId === lastMoveLegId,
    isLastRenderedMoveLeg,
    title,
    scheduling: {
      addressState,
      dateState
    }
  });

  const value = useMemo(
    () => getState(),
    [moveLeg, lastMoveLegId, isLastRenderedMoveLeg, dateState, addressState]
  );

  return <MoveLegContext.Provider value={value}>{children}</MoveLegContext.Provider>;
};
