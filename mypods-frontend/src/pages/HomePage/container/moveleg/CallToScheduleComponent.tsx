import React from 'react';
import { Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { PhoneIcon } from '../../../../components/icons/PhoneIcon';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { Design } from '../../../../helpers/Design';

export const CallToScheduleComponent = ({
  isScheduled = false,
  onlineScheduleEnabled = true
}: {
  isScheduled?: boolean;
  onlineScheduleEnabled?: boolean;
}) => {
  const { t: translate } = useTranslation();
  const styles = componentStyles();
  return (
    <Grid container {...styles.container}>
      <Grid container item gap={Design.Primitives.Spacing.xxs}>
        <PhoneIcon sx={{ fontSize: '1rem' }} />
        <Typography color="inherit" data-testid="move-leg-title" {...styles.title}>
          {isScheduled
            ? translate(TranslationKeys.HomePage.MoveLegs.Title.CALL_TO_RESCHEDULE)
            : translate(TranslationKeys.HomePage.MoveLegs.Title.CALL_TO_SCHEDULE)}
        </Typography>
      </Grid>
      <Grid container item>
        <Typography
          {...styles.phoneLink}
          color="inherit"
          variant="subtitle1"
          dangerouslySetInnerHTML={{
            __html: translate(
              onlineScheduleEnabled
                ? TranslationKeys.HomePage.MoveLegs.CALL_TO_SCHEDULE_DESCRIPTION
                : TranslationKeys.HomePage.MoveLegs.CALL_TO_SCHEDULE_WHEN_ONLINE_SCHEDULING_DISABLED
            )
          }}
        />
      </Grid>
    </Grid>
  );
};

const componentStyles = () => ({
  container: {
    justifyContent: 'row',
    rowGap: Design.Primitives.Spacing.xxxs
  },
  phoneLink: {
    sx: {
      a: {
        color: Design.Alias.Color.secondary500,
        fontWeight: '700',
        textDecoration: 'none'
      }
    }
  },
  title: {
    sx: {
      ...Design.Alias.Text.Heading.Desktop.Xs
    }
  }
});
