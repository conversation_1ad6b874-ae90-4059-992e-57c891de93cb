import { Design } from '../../../../helpers/Design';

export const sharedMoveLegStyles = (isMobile: boolean) => ({
  moveLegSection: {
    sx: {
      flexDirection: 'row',
      columnGap: Design.Primitives.Spacing.xxs
    }
  },
  titleContainer: {
    sx: {
      flexDirection: 'row',
      alignItems: 'center',
      columnGap: Design.Primitives.Spacing.xxs
    }
  },
  titleText: {
    sx: {
      paddingBottom: 0,
      color: '#0E2E3B' // TODO: change color or add to design theme
    }
  },
  mainBody: {
    sx: {
      flexGrow: 1,
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs,
      marginTop: '-5px'
    }
  },
  detailsContainer: {
    sx: {
      color: Design.Alias.Color.accent900,
      flexDirection: isMobile ? 'column' : 'row',
      columnGap: Design.Primitives.Spacing.md,
      rowGap: Design.Primitives.Spacing.xxs
    }
  },
  dividerContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      paddingRight: '8px',
      height: isMobile ? '64px' : '72px'
    }
  }
});
