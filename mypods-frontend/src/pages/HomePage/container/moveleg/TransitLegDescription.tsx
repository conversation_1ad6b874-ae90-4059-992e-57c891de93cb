import { useTranslation } from 'react-i18next';
import { Grid, Typography } from '@mui/material';
import React from 'react';
import { PhoneIcon } from '../../../../components/icons/PhoneIcon';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { Design } from '../../../../helpers/Design';

export const TransitLegDescription = () => {
  const { t: translate } = useTranslation();
  return (
    <Grid container gap="4px" flexDirection="row">
      <Grid item alignContent="center">
        <PhoneIcon sx={{ fontSize: '1rem' }} />
      </Grid>
      <Grid item xs>
        <Typography
          {...styles.phoneLink}
          color="inherit"
          variant="subtitle2"
          dangerouslySetInnerHTML={{
            __html: translate(TranslationKeys.HomePage.MoveLegs.IN_TRANSIT_DESCRIPTION)
          }}
        />
      </Grid>
    </Grid>
  );
};

const styles = {
  phoneLink: {
    sx: {
      a: {
        color: Design.Alias.Color.secondary500,
        fontWeight: '700',
        textDecoration: 'none'
      }
    }
  }
};
