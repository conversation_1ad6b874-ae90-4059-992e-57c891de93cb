import { Grid, Typography } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { formatETA } from '../../../../helpers/dateHelpers';

export const PickupWindow = ({ etaWindow }: { etaWindow?: string }) => {
  const { t: translate } = useTranslation();
  const styles = addressStyles();
  return (
    <Grid item {...styles.pickupWindowContainer}>
      <Typography color="inherit" variant="subtitle2" {...styles.pickupWindowTitle}>
        {translate(TranslationKeys.HomePage.MoveLegs.Scheduling.DateLabels.LABEL_PICKUP_WINDOW)}
      </Typography>
      <Typography color="inherit" variant="subtitle1">
        {formatETA(etaWindow) ?? '-'}
      </Typography>
    </Grid>
  );
};

const addressStyles = () => ({
  pickupWindowContainer: {
    sx: {
      flexDirection: 'column',
      alignSelf: 'stretch',
      flex: 1
    }
  },
  pickupWindowTitle: {
    sx: { fontWeight: Design.Alias.Text.BodyUniversal.XsBold.fontWeight }
  }
});
