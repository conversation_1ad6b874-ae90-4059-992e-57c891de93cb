import { MoveLeg } from '../../../domain/OrderEntities';
import { screen, within } from '@testing-library/react';

const views = {
  moveLegTitle: async (moveLeg: MoveLeg) => {
    const containerTile = await screen.findByTestId('accordion-content');
    const moveLegContainer = await within(containerTile).findByTestId(
      'move-leg-' + moveLeg.moveLegId
    );
    return within(moveLegContainer).getByTestId('move-leg-title');
  },
  moveLegTitleContainer: async (moveLeg: MoveLeg) => {
    const containerTile = await screen.findByTestId('accordion-content');
    const moveLegContainer = await within(containerTile).findByTestId(
      'move-leg-' + moveLeg.moveLegId
    );
    return within(moveLegContainer).getByTestId('move-leg-title-container');
  },
  moveLegScheduleButton: async (moveLeg: MoveLeg) => {
    const containerTile = await screen.findByTestId('accordion-content');
    const moveLegContainer = await within(containerTile).findByTestId(
      'move-leg-' + moveLeg.moveLegId
    );
    return within(moveLegContainer).findByTestId('schedule-move-leg-button');
  },
  moveLegProgressLine: async (moveLeg: MoveLeg) => {
    const containerTile = await screen.findByTestId('accordion-content');
    return within(containerTile).getByTestId(`progress-line-${moveLeg.moveLegId}`);
  },
  moveLegIcon: async (moveLeg: MoveLeg, testId: string) => {
    return within(await views.moveLegProgressLine(moveLeg)).getByTestId(testId);
  }
};

export const homePageViews = views;
