import { screen, within } from '@testing-library/react';
import { HomePageSidebar } from '../HomePageSidebar';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { renderWithPoetProvidersAndState } from '../../../testUtils/RenderHelpers';
import { mockGetCustomer, mockGetPaymentMethods, mockRefreshSession } from '../../../../setupTests';
import { createCustomer, createRefreshSessionClaims } from '../../../testUtils/MyPodsFactories';

describe('HomePage Sidebar', () => {
  const Tx = TranslationKeys.HomePage.Sidebar;
  const views = {
    resources: () => screen.getByTestId('resources-sidebar-section'),
    servicesWithin: () => within(screen.getByTestId('services-sidebar-section'))
  };

  describe('should display general content', () => {
    beforeEach(() => {
      const session = createRefreshSessionClaims();
      mockRefreshSession.mockResolvedValue(session);
      mockGetCustomer.mockResolvedValue(createCustomer({ ...session }));
      mockGetPaymentMethods.mockResolvedValue([]);
      renderWithPoetProvidersAndState(<HomePageSidebar />);
    });

    it('should render resources links', async () => {
      expect(await screen.findByTestId('resources-sidebar-section')).toBeInTheDocument();
      expect(views.resources()).toHaveTextContent(Tx.Faqs.TEXT);
      expect(views.resources()).toHaveTextContent(Tx.BillingPayment.TEXT);
      expect(views.resources()).toHaveTextContent(Tx.DeliveryChecklist.TEXT);
      expect(views.resources()).toHaveTextContent(Tx.PackingTips.TEXT);
      expect(views.resources()).toHaveTextContent(Tx.PackingDosDonts.TEXT);
    });

    it('should render moving services links', async () => {
      expect(await screen.findByTestId('services-sidebar-section')).toBeInTheDocument();
      expect(views.servicesWithin().getByText(Tx.CarShipping.TEXT)).toBeInTheDocument();
      expect(views.servicesWithin().getByText(Tx.PackingHelp.TEXT)).toBeInTheDocument();
    });
  });
});
