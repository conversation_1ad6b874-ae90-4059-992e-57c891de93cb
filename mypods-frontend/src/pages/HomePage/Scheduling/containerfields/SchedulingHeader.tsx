import React from 'react';
import { Box, Step, StepLabel, Stepper, Typography } from '@mui/material';
import { Check } from '@phosphor-icons/react';
import { Design } from '../../../../helpers/Design';

export const SchedulingHeader = ({
  isDateSelected,
  icon,
  title,
  title2,
  subtitle,
  showStepper = false,
  activeStep = 0
}: {
  isDateSelected: boolean;
  icon: React.ReactNode;
  title: string;
  title2: string;
  subtitle: string;
  showStepper?: boolean;
  activeStep?: number;
}) => {
  const steps = ['Location', 'Placement', 'Confirm'];
  return (
    <Box sx={styles.moveHeader}>
      {showStepper && (
        <Stepper activeStep={activeStep} alternativeLabel sx={{ width: '100%', mb: 2 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      )}
      <Box sx={styles.moveTitle}>
        {isDateSelected ? <Check size={40} color={Design.Primitives.Color.Blue.oasis} /> : icon}
        <Typography variant="h2">{isDateSelected ? title2 : title}</Typography>
      </Box>
      <Typography variant="subtitle1">{subtitle}</Typography>
    </Box>
  );
};

const styles = {
  moveHeader: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    flexDirection: 'column',
    alignSelf: 'stretch'
  },
  moveTitle: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'center',
    justifyContent: 'center'
  }
};
