import React from 'react';
import { Box, CircularProgress } from '@mui/material';
import { Button } from 'pods-component-library';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';

interface SchedulingFooterProps {
  isDateSelected: boolean;
  onClose: () => void;
  handleBack: () => void;
  setIsDateSelected: (isDateSelected: boolean) => void;
  setSelectedDate: (selectedDate: Date | null) => void;
  selectedDate: Date | null;
  isSaving: boolean;
  handleSave: () => void;
  handleNext: () => void;
}

const Tx = TranslationKeys.HomePage.MoveLegs;

export const SchedulingFooter: React.FC<SchedulingFooterProps> = ({
  isDateSelected,
  onClose,
  handleBack,
  setIsDateSelected,
  setSelectedDate,
  selectedDate,
  isSaving,
  handleSave,
  handleNext
}) => {
  const { t: translate } = useTranslation();
  return (
    <Box sx={styles.moveFooter}>
      <Button
        variant="outlined"
        onPress={() => {
          if (!isDateSelected) {
            onClose();
          } else {
            handleBack();
          }
          setIsDateSelected(false);
          setSelectedDate(null);
        }}
        buttonSize="large"
        color="secondary"
        isDisabled={isSaving}>
        {!isDateSelected ? 'Cancel' : 'Back'}
      </Button>
      <Button
        variant="animated"
        isDisabled={!isDateSelected && !selectedDate}
        buttonSize="large"
        color="secondary"
        onPress={isDateSelected ? handleSave : handleNext}>
        {(() => {
          if (isSaving) {
            return (
              <div>
                <CircularProgress size={20} color="inherit" sx={{ mr: 1 }} /> Saving...
              </div>
            );
          }
          if (isDateSelected) {
            return translate(Tx.Scheduling.PickupContainer.CONFIRM_BUTTON);
          }
          return translate(Tx.Scheduling.PickupContainer.NEXT_BUTTON);
        })()}
      </Button>
    </Box>
  );
};

const styles = {
  moveFooter: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.sm,
    position: 'absolute',
    bottom: 0,
    background: Design.Alias.Color.neutral100,
    boxShadow: '0px 0px 8px 0px var(--color-neutral-neutral300, #CBCBCB)',
    width: '100%',
    boxSizing: 'border-box'
  }
};
