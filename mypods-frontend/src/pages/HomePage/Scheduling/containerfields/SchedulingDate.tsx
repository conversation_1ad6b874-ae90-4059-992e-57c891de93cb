import React from 'react';
import { DesktopDatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { DatepickerTextField } from '../../container/scheduling/DatepickerTextField';
import { DatePickerDay } from '../../container/scheduling/DatePickerDay';
import { Design } from '../../../../helpers/Design';

interface SchedulingDateProps {
  isDateSelected: boolean;
  selectedDate: Date | null;
  setSelectedDate: (date: Date | null) => void;
  setIsDatePickerOpen: (open: boolean) => void;
  isDatePickerOpen: boolean;
  dateState: any;
  label?: string;
  hidePickerWhenSelected?: boolean;
}

export const SchedulingDate: React.FC<SchedulingDateProps> = ({
  isDateSelected,
  selectedDate,
  setSelectedDate,
  setIsDatePickerOpen,
  isDatePickerOpen,
  dateState,
  label,
  hidePickerWhenSelected = false
}) => {
  const { t: translate } = useTranslation();
  const labelText = label
    ? translate(label)
    : translate('HomePage.MoveLegs.Scheduling.PickupContainer.DATE_LABEL');
  if (hidePickerWhenSelected && isDateSelected) {
    return (
      <Box>
        <Typography variant="subtitle1">{labelText}</Typography>
        <Typography variant="body1" color={Design.Alias.Color.accent900}>
          {selectedDate
            ? selectedDate.toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric',
                year: 'numeric'
              })
            : ''}
        </Typography>
      </Box>
    );
  }

  return (
    <div>
      {isDateSelected ? (
        <Box>
          <Typography variant="subtitle1">{labelText}</Typography>
          <Typography variant="body1" color={Design.Alias.Color.accent900}>
            {selectedDate
              ? selectedDate.toLocaleDateString('en-US', {
                  weekday: 'short',
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric'
                })
              : ''}
          </Typography>
        </Box>
      ) : (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DesktopDatePicker
            label={labelText}
            disabled={false}
            loading={dateState.containerAvailabilityPending}
            renderLoading={() => <div>Loading</div>}
            renderInput={(params) => (
              <DatepickerTextField onClick={() => setIsDatePickerOpen(true)} params={params} />
            )}
            onClose={() => setIsDatePickerOpen(false)}
            onChange={(date) => {
              setSelectedDate(date);
              setIsDatePickerOpen(false);
            }}
            value={selectedDate}
            disablePast
            disableOpenPicker={false}
            defaultCalendarMonth={new Date()}
            open={isDatePickerOpen}
            views={['day']}
            onMonthChange={(day) => {
              dateState.addContainerAvailabilitiesFor3Months(day!);
            }}
            renderDay={(day, selectedDays, pickersDayProps) => (
              <DatePickerDay
                key={day.toISOString()}
                date={day}
                selectedDay={selectedDays[0]}
                pickersDayProps={pickersDayProps}
                isAvailable={dateState.isAvailable}
                onClick={(date: Date) => {
                  setSelectedDate(date);
                  setIsDatePickerOpen(false);
                }}
              />
            )}
          />
        </LocalizationProvider>
      )}
    </div>
  );
};
