import React from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';

const Tx = TranslationKeys.HomePage.MoveLegs;

export const SchedulingDescription = () => {
  const { t: translate } = useTranslation();
  return (
    <Box sx={styles.infoContainer}>
      <Typography variant="h6">{translate(Tx.Scheduling.PickupContainer.INFO_TITLE)}</Typography>

      <Box sx={styles.infoDetails}>
        <Typography variant="subtitle1" sx={styles.infoTitle}>
          {translate(Tx.Scheduling.PickupContainer.Info.Info1.TITLE)}
        </Typography>
        <Typography variant="subtitle2" sx={{ color: Design.Alias.Color.neutral700 }}>
          {translate(Tx.Scheduling.PickupContainer.Info.Info1.DESC)}
        </Typography>
      </Box>
      <Box sx={styles.infoDetails}>
        <Typography variant="subtitle1" sx={styles.infoTitle}>
          {translate(Tx.Scheduling.PickupContainer.Info.Info2.TITLE)}
        </Typography>
        <Typography variant="subtitle2" sx={{ color: Design.Alias.Color.neutral700 }}>
          {translate(Tx.Scheduling.PickupContainer.Info.Info2.DESC)}
        </Typography>
      </Box>
    </Box>
  );
};

const styles = {
  infoContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.xs,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  },
  infoDetails: {
    gap: Design.Primitives.Spacing.xs
  },
  infoTitle: {
    fontWeight: Design.Alias.Text.BodyUniversal.SmSemi.fontWeight,
    color: Design.Alias.Color.accent900
  }
};
