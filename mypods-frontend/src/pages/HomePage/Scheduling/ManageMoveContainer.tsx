import React, { useContext, useState } from 'react';
import { Drawer, Box, IconButton, Divider } from '@mui/material';
import { ArrowUpRight, X } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import LoadingScreen from '../../../components/Loading/LoadingScreen';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { getSuccessMessage } from '../container/scheduling/ScheduleMoveLeg';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import useMoveLegContext from '../container/moveleg/MoveLegContext';
import { SchedulingDescription } from './containerfields/SchedulingDescription';
import { SchedulingAddress } from './containerfields/SchedulingAddress';
import { SchedulingHeader } from './containerfields/SchedulingHeader';
import { SchedulingFooter } from './containerfields/SchedulingFooter';
import { SchedulingDate } from './containerfields/SchedulingDate';

interface ManageMoveContainerProps {
  isOpen: boolean;
  onClose: () => void;
  moveLeg?: any;
}

const Tx = TranslationKeys.HomePage.MoveLegs.Scheduling.PickupContainer;

export const ManageMoveContainer: React.FC<ManageMoveContainerProps> = ({
  isOpen,
  onClose,
  moveLeg
}) => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const {
    scheduling: { dateState }
  } = useMoveLegContext();
  const [isMobile] = useState(window.innerWidth <= 768);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isDateSelected, setIsDateSelected] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleNext = () => {
    if (selectedDate) {
      setIsDateSelected(true);
    }
  };

  const handleBack = () => {
    setIsDateSelected(false);
  };

  const handleSave = () => {
    setIsSaving(true);
    setTimeout(() => {
      setIsSaving(false);
      onClose();
      setNotification({
        isError: false,
        message: translate(getSuccessMessage(moveLeg))
      });
      setIsDateSelected(false);
      setSelectedDate(null);
    }, 100);
  };

  return (
    <Drawer
      anchor="right"
      open={isOpen}
      onClose={onClose}
      PaperProps={{
        sx: {
          height: '100%',
          display: 'flex',
          width: isMobile ? '100%' : '30.625rem',
          flexDirection: 'column',
          alignItems: 'center',
          gap: Design.Primitives.Spacing.sm,
          flexShrink: 0
        }
      }}>
      <Box sx={styles.drawerHeader}>
        <IconButton
          onClick={() => {
            onClose();
            setIsDateSelected(false);
            setSelectedDate(null);
          }}
          sx={{ padding: 0 }}>
          <X size={24} color={Design.Alias.Color.secondary900} />
        </IconButton>
      </Box>
      {isSaving ? (
        <LoadingScreen loadingText={translate(Tx.LOADING_TEXT)} />
      ) : (
        <Box sx={styles.moveContainer}>
          <SchedulingHeader
            isDateSelected={isDateSelected}
            icon={<ArrowUpRight size={40} color={Design.Primitives.Color.Blue.oasis} />}
            title={translate(Tx.Title.STEP1)}
            title2={translate(Tx.Title.STEP2)}
            subtitle={translate(Tx.SUBTITLE)}
          />
          <Box sx={styles.addressContainer}>
            <SchedulingAddress
              showLabel
              addressValue={translate(Tx.ADDRESS_VALUE)}
              title={translate(Tx.DETAILS_TITLE)}
            />
            <Divider sx={styles.divider} />
            <SchedulingDate
              isDateSelected={isDateSelected}
              selectedDate={selectedDate}
              setSelectedDate={setSelectedDate}
              setIsDatePickerOpen={setIsDatePickerOpen}
              isDatePickerOpen={isDatePickerOpen}
              dateState={dateState}
              label={Tx.DATE_LABEL}
              hidePickerWhenSelected
            />
          </Box>

          {!isDateSelected && <SchedulingDescription />}
        </Box>
      )}
      <SchedulingFooter
        isDateSelected={isDateSelected}
        onClose={onClose}
        handleBack={handleBack}
        setIsDateSelected={setIsDateSelected}
        setSelectedDate={setSelectedDate}
        selectedDate={selectedDate}
        isSaving={isSaving}
        handleSave={handleSave}
        handleNext={handleNext}
      />
    </Drawer>
  );
};

const styles = {
  drawerHeader: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.md,
    alignSelf: 'stretch'
  },
  loadingContainer: {
    paddingX: Design.Primitives.Spacing.lg,
    paddingY: Design.Primitives.Spacing.sm
  },
  moveContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    paddingX: Design.Primitives.Spacing.sm
  },
  addressContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  },
  divider: {
    width: '100%'
  }
};
