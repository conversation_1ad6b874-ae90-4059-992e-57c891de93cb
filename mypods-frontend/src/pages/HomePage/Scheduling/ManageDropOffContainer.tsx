import React, { useContext, useState } from 'react';
import {
  Drawer,
  Box,
  IconButton,
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  Box as MuiBox
} from '@mui/material';
import { X } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { getSuccessMessage } from '../container/scheduling/ScheduleMoveLeg';
import useMoveLegContext from '../container/moveleg/MoveLegContext';
import { SchedulingFooter } from './containerfields/SchedulingFooter';
import { SchedulingDate } from './containerfields/SchedulingDate';
import { SchedulingAddress } from './containerfields/SchedulingAddress';
import { HorizontalStepper } from '../../../components/stepper/HorizontalStepper';
import { SchedulingDescription } from './containerfields/SchedulingDescription';

// Step screen components (to be implemented)
const DropOffDetailsStep = ({
  isDateSelected,
  selectedDate,
  setSelectedDate,
  setIsDatePickerOpen,
  isDatePickerOpen,
  dateState
}: any) => (
  <>
    <Box sx={styles.addressContainer}>
      <SchedulingAddress
        showLabel={isDateSelected}
        addressValue="1210 Verona Ct Apt 101 Powder Springs, GA"
        title="Drop off details"
      />
      <Box>
        <Typography variant="h6" sx={{ fontWeight: 700 }}>
          Date
        </Typography>
        <SchedulingDate
          isDateSelected={isDateSelected}
          selectedDate={selectedDate}
          setSelectedDate={setSelectedDate}
          setIsDatePickerOpen={setIsDatePickerOpen}
          isDatePickerOpen={isDatePickerOpen}
          dateState={dateState}
          label="Date"
          hidePickerWhenSelected={false}
        />
      </Box>
    </Box>
    <SchedulingDescription />
  </>
);
const placementOptions = [
  { value: 'driveway', label: 'Driveway' },
  { value: 'street', label: 'Street' },
  { value: 'parking_lot', label: 'Parking lot' }
];

const PlacementTips = () => (
  <MuiBox
    sx={{
      background: '#F3F7FB',
      borderRadius: 8,
      p: 2,
      mb: 2,
      border: '1px solid #B3D4FC',
      width: '100%'
    }}>
    <strong>Please keep these tips in mind.</strong>
    <ol style={{ margin: 0, paddingLeft: 20 }}>
      <li>Avoid steep inclines</li>
      <li>Check around the ground for objects</li>
      <li>Clear a space 12ft wide and 15ft tall</li>
      <li>Look out for wires and branches</li>
      <li>Move your vehicle on delivery day</li>
      <li>Multiple containers need more room</li>
    </ol>
  </MuiBox>
);

const PlacementStep = ({ placement, setPlacement }: any) => (
  <Box sx={styles.addressContainer}>
    <Typography variant="h6" sx={{ mb: 1 }}>
      Placement for:
    </Typography>
    <Typography variant="body1" sx={{ mb: 2 }}>
      1210 Verona Ct, Powder Springs, GA 30127
    </Typography>
    <PlacementTips />
    <FormControl component="fieldset">
      <FormLabel component="legend">Where would you like to place the container?</FormLabel>
      <RadioGroup
        aria-label="placement"
        name="placement"
        value={placement}
        onChange={(e) => setPlacement(e.target.value)}>
        {placementOptions.map((opt) => (
          <FormControlLabel
            key={opt.value}
            value={opt.value}
            control={<Radio />}
            label={opt.label}
          />
        ))}
      </RadioGroup>
    </FormControl>
  </Box>
);

const ConfirmStep = () => <div>Confirm Step</div>;

interface ManageDropOffContainerProps {
  isOpen: boolean;
  onClose: () => void;
  moveLeg?: any;
}

// const Tx = TranslationKeys.HomePage.MoveLegs;

export const ManageDropOffContainer: React.FC<ManageDropOffContainerProps> = ({
  isOpen,
  onClose,
  moveLeg
}) => {
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const {
    scheduling: { dateState }
  } = useMoveLegContext();
  const [isMobile] = useState(window.innerWidth <= 768);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isDateSelected, setIsDateSelected] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const steps = ['Location', 'Placement', 'Confirm'];
  const [placement, setPlacement] = useState('');

  const handleNextStep = () => setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  const handleBackStep = () => setCurrentStep((prev) => Math.max(prev - 1, 0));

  // const getAddressString = () => {
  //   if (moveLeg && moveLeg.displayAddress) {
  //     const addr = moveLeg.displayAddress;
  //     return [addr.address1, addr.city, addr.state, addr.postalCode].filter(Boolean).join(', ');
  //   }
  //   return '1210 Verona Ct, Powder Springs, GA 30127';
  // };

  // const handleNext = () => {
  //   if (selectedDate) {
  //     setIsDateSelected(true);
  //   }
  // };
  //
  // const handleBack = () => {
  //   setIsDateSelected(false);
  // };
  //
  // const staticHours = [
  //   { days: 'Mon-Sat', hours: '10-5 EST', bold: true },
  //   { days: 'Sun', hours: '9-6:30 PM EST', bold: false }
  // ];

  const handleSave = () => {
    setIsSaving(true);
    setTimeout(() => {
      setIsSaving(false);
      onClose();
      setNotification({
        isError: false,
        message: translate(getSuccessMessage(moveLeg))
      });
      setIsDateSelected(false);
      setSelectedDate(null);
    }, 100);
  };

  // Validation logic
  // const isStepValid = () => {
  //   if (currentStep === 0) return !!selectedDate;
  //   if (currentStep === 1) return !!placement;
  //   return true;
  // };

  return (
    <Drawer
      anchor="right"
      open={isOpen}
      onClose={onClose}
      PaperProps={{
        sx: {
          height: '100%',
          display: 'flex',
          width: isMobile ? '100%' : '30.625rem',
          flexDirection: 'column',
          alignItems: 'center',
          gap: Design.Primitives.Spacing.sm,
          flexShrink: 0
        }
      }}>
      <Box sx={styles.drawerHeader}>
        <IconButton onClick={onClose} sx={{ padding: 0 }}>
          <X size={24} color={Design.Primitives.Color.NeutralDark.black} />
        </IconButton>
      </Box>
      <Box sx={{ width: '100%' }}>
        <HorizontalStepper steps={steps} activeStep={currentStep} />
      </Box>
      <Box sx={{ flex: 1, width: '100%' }}>
        {currentStep === 0 && (
          <DropOffDetailsStep
            isDateSelected={isDateSelected}
            selectedDate={selectedDate}
            setSelectedDate={setSelectedDate}
            setIsDatePickerOpen={setIsDatePickerOpen}
            isDatePickerOpen={isDatePickerOpen}
            dateState={dateState}
          />
        )}
        {currentStep === 1 && <PlacementStep placement={placement} setPlacement={setPlacement} />}
        {currentStep === 2 && <ConfirmStep />}
      </Box>
      <SchedulingFooter
        isDateSelected={currentStep > 0}
        onClose={onClose}
        handleBack={handleBackStep}
        setIsDateSelected={() => {}}
        setSelectedDate={() => {}}
        isSaving={isSaving}
        handleSave={handleSave}
        handleNext={handleNextStep}
        // Validation: disable Next if not valid
        // The Button disables if !isDateSelected && !selectedDate, so we set selectedDate/step logic accordingly
        // For step 0, selectedDate is used; for step 1, isDateSelected is true, so we can use placement
        // We'll pass selectedDate only for step 0, and fake it for step 1
        selectedDate={
          null
          // currentStep === 0 ? selectedDate : currentStep === 1 && !placement ? null : new Date()
        }
      />
    </Drawer>
  );
};

const styles = {
  drawerHeader: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.md,
    alignSelf: 'stretch'
  },
  loadingContainer: {
    paddingX: Design.Primitives.Spacing.lg,
    paddingY: Design.Primitives.Spacing.sm
  },
  moveContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    paddingX: Design.Primitives.Spacing.sm
  },
  moveHeader: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    flexDirection: 'column',
    alignSelf: 'stretch'
  },
  moveTitle: {
    display: 'flex',
    gap: Design.Primitives.Spacing.xxs,
    alignItems: 'center',
    justifyContent: 'center'
  },
  addressContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.sm,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  },
  addressDetails: {
    gap: Design.Primitives.Spacing.xxs
  },
  addressDetailsItem: {
    gap: Design.Primitives.Spacing.xxs,
    display: 'flex',
    paddingLeft: Design.Primitives.Spacing.xs,
    alignItems: 'center',
    alignSelf: 'stretch'
  },
  divider: {
    width: '100%'
  },
  infoContainer: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: Design.Primitives.Spacing.xs,
    alignSelf: 'stretch',
    border: `1px solid ${Design.Alias.Color.neutral200}`,
    borderRadius: Design.Primitives.Spacing.sm
  },
  infoDetails: {
    gap: Design.Primitives.Spacing.xxs
  },
  infoTitle: {
    fontWeight: Design.Alias.Text.BodyUniversal.SmSemi.fontWeight,
    color: Design.Alias.Color.accent900
  },
  moveFooter: {
    display: 'flex',
    padding: Design.Primitives.Spacing.sm,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: Design.Primitives.Spacing.sm,
    position: 'absolute',
    bottom: 0,
    background: Design.Alias.Color.neutral100,
    boxShadow: '0px 0px 8px 0px var(--color-neutral-neutral300, #CBCBCB)',
    width: '100%',
    boxSizing: 'border-box'
  }
};
