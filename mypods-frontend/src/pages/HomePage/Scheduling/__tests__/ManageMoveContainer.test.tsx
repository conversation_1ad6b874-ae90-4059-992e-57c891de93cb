import { createContainer } from '@mui/system';
import { QueryClientProvider } from '@tanstack/react-query';
import { render, cleanup } from '@testing-library/react';
import { Suspense } from 'react';
import { Container } from 'react-dom/client';
import { mockRefreshSession, mockedUseFeatureFlags } from '../../../../../setupTests';
import { NotificationProvider } from '../../../../components/notifications/NotificationContext';
import { ContainerProvider } from '../../../../context/ContainerContext';
import { SingleOrderProvider } from '../../../../context/SingleOrderContext';
import { MoveLeg, Order } from '../../../../domain/OrderEntities';
import {
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../../testUtils/MyPodsFactories';
import { testQueryClient, runPendingPromises } from '../../../../testUtils/RenderHelpers';
import { MoveLegSection } from '../../container/moveleg/current/MoveLegSection';
import { MoveLegProvider } from '../../container/moveleg/MoveLegContext';
import { ManageMoveContainer } from '../ManageMoveContainer';

describe('ManagePickupContainer', () => {
  const renderPanel = async (moveLeg: MoveLeg, onClose: () => void = vi.fn()) => {
    const result = render(
      <ManageMoveContainer isOpen={true} onClose={onClose} moveLeg={moveLeg} />
    );
    await runPendingPromises();
    return result;
  };

  describe('General Behavior', () => {
    it('should display the correct title', async () => {
      const moveLeg = createMoveLeg({
        moveLegType: 'PICKUP'
      });
      await renderPanel(moveLeg);
      expect(screen.getByText('Manage pick up')).toBeInTheDocument();
    });
  });
});
