import { Grid, <PERSON> } from '@mui/material';
import React from 'react';
import { Design } from '../../helpers/Design';
import { QuestionMarkIcon } from '../../components/icons/QuestionMarkIcon';
import { CarIcon } from '../../components/icons/CarIcon';
import { DollyIcon } from '../../components/icons/DollyIcon';
import { RightChevronIcon } from '../../components/icons/RightChevronIcon';
import { BoxIcon } from '../../components/icons/BoxIcon';
import { ROUTES } from '../../Routes';
import { BlueLink } from '../../components/buttons/NavigationLink';

export interface SidebarCardProps {
  icon: string;
  text: string;
  link: string;
}

export const SidebarCard: React.FC<SidebarCardProps> = ({ icon, text, link }: SidebarCardProps) => {
  const styles = cardStyles();
  const renderIcon = (iconToRender: string) => {
    switch (iconToRender) {
      case 'question':
        return <QuestionMarkIcon sx={{ height: '20px', width: '20px' }} />;
      case 'car':
        return <CarIcon sx={{ height: '18px', width: '16px' }} />;
      case 'dolly':
        return <DollyIcon sx={{ height: '18px', width: '16px' }} />;
      case 'box':
        return <BoxIcon sx={{ height: '20px', width: '20px' }} />;
      default:
        return <QuestionMarkIcon sx={{ height: '20px', width: '20px' }} />;
    }
  };

  const renderContainerGrid = () => (
    <Grid container item {...styles.container}>
      <Grid item alignSelf="center">
        <Grid container item {...styles.icon}>
          {renderIcon(icon)}
        </Grid>
      </Grid>
      <Grid item xs {...styles.text}>
        {text}
      </Grid>
      <Grid item alignSelf="center">
        <Grid container item {...styles.chevron}>
          <RightChevronIcon />
        </Grid>
      </Grid>
    </Grid>
  );

  if (Object.values(ROUTES).includes(link as ROUTES)) {
    return (
      <BlueLink to={link} style={{ width: '100%', color: 'inherit', textAlign: 'left' }}>
        {renderContainerGrid()}
      </BlueLink>
    );
  }

  return (
    <Link href={link} target="blank" underline="none" color="inherit" width="100%">
      {renderContainerGrid()}
    </Link>
  );
};
const cardStyles = () => ({
  container: {
    sx: {
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.xxs,
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)',
      padding: Design.Primitives.Spacing.xs,
      borderRadius: '8px',
      border: `1px solid ${Design.Alias.Color.neutral300}`,
      backgroundColor: Design.Alias.Color.neutral100,
      ':hover': {
        backgroundColor: '#1DBCFC0D'
      },
      ':active': {
        backgroundColor: '#B3E3F680'
      }
    }
  },
  icon: {
    sx: {
      alignContent: 'center'
    }
  },
  text: {
    sx: {
      alignContent: 'center',
      color: Design.Alias.Color.accent900,
      ...Design.Alias.Text.BodyUniversal.SmBold
    }
  },
  chevron: {
    sx: {
      alignContent: 'center'
    }
  }
});
