import React from 'react';
import { vi } from 'vitest';
import { screen, within } from '@testing-library/react';
import {
  createBillingInformation,
  createBillingInvoice,
  createMonthlyStatement,
  createPaymentMethod
} from '../../../../testUtils/MyPodsFactories';
import { renderWithLegacyProvidersAndState } from '../../../../testUtils/RenderHelpers';
import { LegacyBillingPage } from '../LegacyBillingPage';
import { legacyBillingPageViews as views } from './LegacyBillingPageViews';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { initialBillingState } from '../../../../context/BillingContext';
import { mockGetPaymentMethods } from '../../../../../setupTests';
import { PaymentMethod } from '../../../../networkRequests/responseEntities/PaymentEntities';

const mockGetBillingInformation = vi.hoisted(() => vi.fn());
const mockGetCustomer = vi.hoisted(() => vi.fn());
vi.mock('../../../networkRequests/MyPodsService', async () => ({
  useMyPodsService: () => ({
    getBillingInformation: mockGetBillingInformation,
    getCustomer: mockGetCustomer
  })
}));

vi.mock('../components/LegacyAcornFinancing', () => ({
  LegacyAcornFinancing: () => {
    return <p>Financing Card</p>;
  }
}));

describe('Billing Page', () => {
  let paymentMethods: PaymentMethod[];
  const statements = [createMonthlyStatement()];
  const paidInvoice = createBillingInvoice();
  const invoiceNumber = 'PODS123456789';
  const totalAmount = 19.95;
  const partialAmountRemaining = 4.55;
  const partialTotalAmount = 10.55;
  const partialPaidInvoice = createBillingInvoice({
    invoiceNumber,
    totalDue: partialTotalAmount,
    amountPaid: 6,
    balanceDue: partialAmountRemaining,
    isPaid: false
  });
  const unpaidInvoice = createBillingInvoice({
    isPaid: false
  });
  const invoices = [paidInvoice, unpaidInvoice, partialPaidInvoice];
  const billingInformation = createBillingInformation({
    invoices,
    monthlyStatements: statements,
    totalBalance: totalAmount
  });

  function renderPage() {
    return renderWithLegacyProvidersAndState(<LegacyBillingPage />, {
      billingState: {
        ...initialBillingState,
        billingInformation: billingInformation,
        paymentMethods: paymentMethods
      },
      initialEntries: ['/billing']
    });
  }

  beforeEach(() => {
    paymentMethods = [createPaymentMethod()];
    mockGetPaymentMethods.mockResolvedValue(paymentMethods);
  });

  it('displays account balance and make payment link', async () => {
    renderPage();

    expect(
      await screen.findByRole('heading', {
        name: `${TranslationKeys.BillingPage.TOTAL_ACCOUNT_BALANCE}`
      })
    ).toBeInTheDocument();
    expect(
      await screen.findByRole('link', {
        name: `${TranslationKeys.BillingPage.MAKE_PAYMENT_LINK}`
      })
    ).toBeInTheDocument();
  });

  it('loads sections of the billing page', async () => {
    renderPage();

    expect(await views.upcomingPayments.section()).toBeInTheDocument();
    expect(views.paymentHistory.section()).toBeInTheDocument();
    expect(views.statements.section()).toBeInTheDocument();
  });

  it('renders the section headings with an accordion that is expanded by default', async () => {
    renderPage();

    expect(
      await within(await views.upcomingPayments.section()).findByRole('heading', {
        name: TranslationKeys.BillingPage.UpcomingPayments.HEADER
      })
    ).toBeInTheDocument();
    expect(await screen.findByTestId('upcoming-payments-content')).toBeVisible();
    ``;
    expect(
      await within(views.paymentHistory.section()).findByRole('heading', {
        name: TranslationKeys.BillingPage.PaymentHistory.HEADER
      })
    ).toBeInTheDocument();
    expect(screen.getByTestId('payment-history-content')).toBeVisible();

    expect(
      await within(views.statements.section()!).findByRole('heading', {
        name: TranslationKeys.BillingPage.Statements.HEADER
      })
    ).toBeInTheDocument();
    expect(screen.getByTestId('statements-content')).toBeVisible();
  });

  it('renders unpaid invoice with partial payment', async () => {
    renderPage();

    expect(
      await screen.findByText(`${TranslationKeys.BillingPage.INVOICE} #${invoiceNumber}`)
    ).toBeInTheDocument();
    expect(await screen.findByText(`$${totalAmount}`)).toBeInTheDocument();
    expect(await screen.findByText(`$${partialAmountRemaining}`)).toBeInTheDocument();
    expect(await screen.findByText(`$${partialTotalAmount}`)).toBeInTheDocument();
  });
  it('renders FAQ Card but without the statement info alert', async () => {
    renderPage();
    expect(
      screen.queryByText(`${TranslationKeys.BillingPage.BillingFaqCard.STATEMENT_INFO_ALERT_TITLE}`)
    ).not.toBeInTheDocument();
  });
});
