import React, { act } from 'react';
import { render, screen } from '@testing-library/react';
import { BillingCardData, LegacyBillingCard } from '../components/LegacyBillingCard';
import { CHECKMARK_ICON_ID } from '../../../../components/icons/CheckmarkIcon';
import { EXCLAMATION_ICON_ID } from '../../../../components/icons/ExclamationIcon';
import { DOCUMENT_ICON_ID } from '../../../../components/icons/DocumentIcon';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import userEvent from '@testing-library/user-event';
import { mockLegacyGetFile, mockLegacyGetSasUrl } from '../../../../../setupTests';
import { QueryClientProvider } from '@tanstack/react-query';
import { testQueryClient } from '../../../../testUtils/RenderHelpers';
import { NotificationProvider } from '../../../../components/notifications/NotificationContext';
import { vi } from 'vitest';
import FileSaver from 'file-saver';
import { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

describe('BillingCard', () => {
  const id = 'ABC12399';
  const documentId = '**********';
  const invoiceNumber = 'PODS111222333';
  const amount = 1909.95;
  const totalAmount = 1939.95;
  const expectedTotal = '$1,909.95';
  const createBillingCardWithDefaults = (options?: Partial<BillingCardData>): BillingCardData => ({
    id,
    documentId,
    invoiceNumber,
    amount,
    totalAmount,
    date: new Date().toISOString(),
    type: 'unpaid-invoice',
    currencyType: 'USD',
    isPoet: false,
    ...options
  });

  const renderBillingCard = (billingCardData = createBillingCardWithDefaults()): void => {
    render(
      <QueryClientProvider client={testQueryClient()}>
        <NotificationProvider>
          <LegacyBillingCard {...billingCardData} />
        </NotificationProvider>
      </QueryClientProvider>
    );
  };

  const saveAsSpy = vi.spyOn(FileSaver, 'saveAs');

  describe('when Poet invoice', () => {
    it('opens file when clicked', async () => {
      const expectedUrl = 'https://document.pdf';
      mockLegacyGetSasUrl.mockResolvedValue(expectedUrl);
      renderBillingCard(createBillingCardWithDefaults({ isPoet: true }));

      const button = screen.getByRole('button');

      expect(button).toBeEnabled();
      await act(async () => {
        await userEvent.click(button);
      });

      expect(mockLegacyGetSasUrl).toHaveBeenCalledWith(documentId, true);
      expect(window.location.href).toBe(expectedUrl);
    });
  });

  it('opens file when clicked', async () => {
    const expectedUrl = 'https://document.pdf';
    mockLegacyGetFile.mockResolvedValue(expectedUrl);
    renderBillingCard();

    const button = screen.getByRole('button');

    expect(button).toBeEnabled();
    await act(async () => {
      await userEvent.click(button);
    });

    expect(mockLegacyGetFile).toHaveBeenCalledWith(documentId.toString(), true);
    expect(saveAsSpy).toHaveBeenCalledWith(expectedUrl, `${id}.pdf`);
  });

  it('should display an error notification if a billing document fails to open', async () => {
    mockLegacyGetFile.mockRejectedValue({});
    renderBillingCard();

    const button = screen.getByRole('button');

    await act(async () => {
      await userEvent.click(button);
    });
    expect(screen.getByRole('alert')).toHaveTextContent(
      TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE
    );
  });

  it('renders future unpaid invoice', () => {
    const expectedDueDate = '1/1/30';
    const invoiceNumber = 'PODS123456789';
    renderBillingCard(
      createBillingCardWithDefaults({
        date: '2030-01-01',
        type: 'unpaid-invoice',
        invoiceNumber: invoiceNumber
      })
    );

    expect(
      screen.getByText(`${TranslationKeys.BillingPage.INVOICE} #${invoiceNumber}`)
    ).toBeInTheDocument();
    expect(screen.getByTestId(DOCUMENT_ICON_ID)).toBeInTheDocument();
    expect(screen.getByText(expectedTotal)).toBeInTheDocument();
    expect(
      screen.getByText(`${TranslationKeys.BillingPage.STATUS}[${expectedDueDate}]`)
    ).toBeInTheDocument();
  });

  it('renders future unpaid invoice with partial payment', () => {
    const invoiceNumber = 'PODS123456789';
    const totalAmount = 1939.95;
    renderBillingCard(
      createBillingCardWithDefaults({
        date: '2030-01-01',
        type: 'unpaid-invoice',
        totalAmount: totalAmount,
        invoiceNumber: invoiceNumber
      })
    );

    expect(
      screen.getByText(`${TranslationKeys.BillingPage.INVOICE} #${invoiceNumber}`)
    ).toBeInTheDocument();
    expect(screen.getByTestId(DOCUMENT_ICON_ID)).toBeInTheDocument();
    expect(screen.getByText(expectedTotal)).toBeInTheDocument();
    expect(screen.getByText('$1,939.95')).toBeInTheDocument();
  });

  it('renders unpaid invoice past due', () => {
    const expectedDueDate = '1/1/20';
    const invoiceNumber = 'PODS123456789';
    renderBillingCard(
      createBillingCardWithDefaults({
        date: '2020-01-01',
        type: 'unpaid-invoice',
        invoiceNumber: invoiceNumber
      })
    );

    expect(
      screen.getByText(`${TranslationKeys.BillingPage.INVOICE} #${invoiceNumber}`)
    ).toBeInTheDocument();
    expect(screen.getByTestId(EXCLAMATION_ICON_ID)).toBeInTheDocument();
    expect(screen.getByText(expectedTotal)).toBeInTheDocument();
    screen.getByText(`${TranslationKeys.BillingPage.STATUS}[${expectedDueDate}]`);
  });

  it('renders paid invoice', () => {
    const expectedDueDate = '1/1/20';
    const invoiceNumber = 'PODS123456789';
    renderBillingCard(
      createBillingCardWithDefaults({
        date: '2020-01-01',
        type: 'paid-invoice',
        invoiceNumber: invoiceNumber
      })
    );

    expect(
      screen.getByText(`${TranslationKeys.BillingPage.INVOICE} #${invoiceNumber}`)
    ).toBeInTheDocument();
    expect(screen.getByTestId(CHECKMARK_ICON_ID)).toBeInTheDocument();
    expect(screen.getByText(expectedTotal)).toBeInTheDocument();
    expect(
      screen.getByText(`${TranslationKeys.BillingPage.STATUS}[${expectedDueDate}]`)
    ).toBeInTheDocument();
  });

  it('renders monthly statement', () => {
    const expectedDueDate = '04/24';
    renderBillingCard(
      createBillingCardWithDefaults({
        date: '2024-04-01',
        type: 'statement'
      })
    );

    expect(
      screen.getByText(`April ${TranslationKeys.BillingPage.Statements.Statement.TITLE}`)
    ).toBeInTheDocument();
    expect(screen.getByTestId(DOCUMENT_ICON_ID)).toBeInTheDocument();
    expect(screen.queryByText('$', { exact: false })).not.toBeInTheDocument();
    expect(screen.getByText(expectedDueDate)).toBeInTheDocument();
    expect(screen.getByText(`#${id}`)).toBeInTheDocument();
  });

  it('should display error when documentId is zero', async () => {
    renderBillingCard(createBillingCardWithDefaults({ documentId: '0' }));

    const button = screen.getByRole('button');

    await act(async () => {
      await userEvent.click(button);
    });
    expect(mockLegacyGetFile).not.toHaveBeenCalled();
    expect(screen.getByRole('alert')).toHaveTextContent(
      TranslationKeys.CommonComponents.Notification.FILE_NOT_READY_ERROR
    );
  });

  it('should display NOT_FOUND error when documentId is not found', async () => {
    const mockAxiosResponse: AxiosResponse<unknown, any> = {
      data: 'Resource not found', // or use undefined if you want no data
      status: 404,
      statusText: 'Not Found',
      headers: {},
      config: {} as InternalAxiosRequestConfig<any>
    };
    mockLegacyGetFile.mockRejectedValue(
      new AxiosError(
        'Request failed with status code 404',
        '404',
        undefined,
        undefined,
        mockAxiosResponse
      )
    );
    renderBillingCard();

    const button = screen.getByRole('button');

    await act(async () => {
      await userEvent.click(button);
    });
    expect(screen.getByRole('alert')).toHaveTextContent(
      TranslationKeys.CommonComponents.Notification.DOCUMENT_NOT_FOUND
    );
  });
});
