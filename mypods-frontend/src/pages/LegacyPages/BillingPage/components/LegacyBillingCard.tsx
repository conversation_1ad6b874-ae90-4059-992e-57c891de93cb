import React, { useContext } from 'react';
import { ButtonBase, CircularProgress, Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { format, isBefore, parseISO, startOfDay } from 'date-fns';
import { saveAs } from 'file-saver';
import { AxiosError } from 'axios';
import { formatByCurrency } from '../../../../networkRequests/responseEntities/BillingEntities';
import { formatDate } from '../../../../helpers/dateHelpers';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { DownloadIcon } from '../../../../components/icons/DownloadIcon';
import { LegacyBillingCardIcon } from './LegacyBillingCardIcon';
import { BillingCardType } from '../../../../domain/BillingEntities';
import { NotificationContext } from '../../../../components/notifications/NotificationContext';
import { useGtmEvents } from '../../../../config/google/useGtmEvents';
import {
  createGtmErrorRequest,
  GA_GENERIC_BACKEND_MESSAGE
} from '../../../../config/google/googleAnalyticsUtils';
import { useLegacyGetFile } from '../../../../networkRequests/legacy/mutations/useLegacyGetFile';
import { useLegacyGetSasUrl } from '../../../../networkRequests/legacy/mutations/useLegacyGetSasUrl';

export type BillingCardData = {
  id: string;
  documentId: string;
  invoiceNumber: string;
  amount: number;
  totalAmount: number;
  date: string;
  type: BillingCardType;
  currencyType: string;
  isPoet: boolean;
};
export const LegacyBillingCard: React.FC<BillingCardData> = ({
  id,
  documentId,
  invoiceNumber,
  amount,
  totalAmount,
  date,
  type,
  currencyType,
  isPoet
}: BillingCardData) => {
  const getFile = useLegacyGetFile();
  const getSasUrl = useLegacyGetSasUrl();
  const { setNotification } = useContext(NotificationContext);
  const { t: translate } = useTranslation();
  const paymentAmount = () => formatByCurrency(amount, currencyType);
  const statementDate = () => format(parseISO(date), 'MM/yy');
  const { fileDownload, errorEvent } = useGtmEvents();
  const isLoading = getFile.isPending || getSasUrl.isPending;

  const hasDatePassed = (invoiceDate: string): boolean => {
    const dateFormatted = parseISO(invoiceDate);
    const now = new Date();
    return isBefore(dateFormatted, startOfDay(now));
  };

  const isPartiallyPaid = () => amount > 0 && amount < totalAmount;

  const datePassed = hasDatePassed(date);

  const onPoetSuccess = (url: string) => {
    fileDownload(url);
    window.location.href = url;
  };

  function getPoetFileById() {
    getSasUrl.mutate(
      { docRef: documentId, isBillingDocument: true },
      { onSuccess: onPoetSuccess, onError }
    );
  }

  function onError(error: unknown) {
    if (error instanceof AxiosError && error.response?.status) {
      if (error.response?.status === 404) {
        setNotification({
          isError: true,
          message: translate(TranslationKeys.CommonComponents.Notification.DOCUMENT_NOT_FOUND)
        });
      }
    } else {
      setNotification({
        isError: true,
        message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE)
      });
    }
    errorEvent(createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, 'billing', 'backend'));
  }

  function getFileById() {
    getFile.mutate(
      { documentId, isBillingDocument: true },
      {
        onSuccess: (url: string) => {
          fileDownload(type);
          saveAs(url, `${id}.pdf`);
        },
        onError
      }
    );
  }

  const downloadPdf = async () => {
    if (documentId === '' || documentId === '0') {
      setNotification({
        isError: true,
        message: translate(TranslationKeys.CommonComponents.Notification.FILE_NOT_READY_ERROR)
      });
      errorEvent(createGtmErrorRequest('Document ID is zero', 'billing', 'front_end'));
      return;
    }
    if (isPoet) return getPoetFileById();
    getFileById();
  };

  const getDateOrID = () => {
    let value;
    let style = styles.dueDate;
    const formattedDate = formatDate(parseISO(date!), 'M/d/yy');
    if (type === 'statement') {
      value = `#${id}`;
    } else if (datePassed && type === 'unpaid-invoice') {
      value = translate(TranslationKeys.BillingPage.STATUS, {
        date: formattedDate
      });
      style = styles.pastDueDate;
    } else {
      value = translate(TranslationKeys.BillingPage.STATUS, {
        date: formattedDate
      });
    }
    return <Typography {...style}>{value}</Typography>;
  };

  const getTitle = () => {
    if (type === 'statement') {
      const fromISO = parseISO(date);
      const month = format(fromISO, 'MMMM');
      return `${month} ${translate(TranslationKeys.BillingPage.Statements.Statement.TITLE)}`;
    }
    return `${translate(TranslationKeys.BillingPage.INVOICE)} #${invoiceNumber}`;
  };

  return (
    <ButtonBase
      sx={{ display: 'flex', width: '100%', textAlign: 'unset' }}
      onClick={downloadPdf}
      disabled={isLoading}>
      <Grid container key={id} {...styles.card}>
        <LegacyBillingCardIcon type={type} hasDatePassed={hasDatePassed(date)} />
        <Grid container item flex={1} {...styles.container}>
          <Grid item {...styles.titleContainer}>
            <Grid>
              <Typography {...styles.cardTitle}>{getTitle()}</Typography>
            </Grid>
            <Grid item>{getDateOrID()}</Grid>
          </Grid>
          <Grid item {...styles.detailsContainer}>
            {type !== 'statement' ? (
              <Grid item {...styles.titleContainer}>
                <Grid>
                  <Typography {...styles.dueAmount}>{paymentAmount()}</Typography>
                </Grid>
                {isPartiallyPaid() && (
                  <Grid {...styles.partialPaidAmount}>
                    {formatByCurrency(totalAmount, currencyType)}
                  </Grid>
                )}
              </Grid>
            ) : (
              <Typography {...styles.cardAmount}>{statementDate()}</Typography>
            )}
          </Grid>
        </Grid>
        <Grid item {...styles.iconContainer}>
          {isLoading ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            <DownloadIcon {...styles.downloadIcon} />
          )}
        </Grid>
      </Grid>
    </ButtonBase>
  );
};

const styles = {
  card: {
    sx: {
      columnGap: Design.Primitives.Spacing.sm,
      display: 'flex',
      flexDirection: 'row',
      padding: '16px',
      justifyContent: 'space-between',
      borderRadius: '8px',
      border: '1px solid var(--color-neutral-neutral300, #CBCBCB)',
      background: 'var(--color-defaults-neutral100-D, #FFF)',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)'
    }
  },
  downloadIcon: {
    sx: {
      width: '24px',
      height: '24px'
    }
  },
  cardTitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.MdBold,
      color: Design.Alias.Color.accent900
    }
  },
  cardAmount: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      color: Design.Alias.Color.accent900
    }
  },
  container: {
    sx: {
      flexDirection: 'row',
      justifyContent: 'space-between'
    }
  },
  dueDate: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: Design.Alias.Color.neutral700
    }
  },
  pastDueDate: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: Design.Alias.Color.errorMain
    }
  },
  titleContainer: {
    sx: {
      flexDirection: 'column'
    }
  },
  detailsContainer: {
    sx: {
      flexDirection: 'column',
      alignContent: 'center',
      textDecoration: 'Strikethrough'
    }
  },
  iconContainer: {
    sx: {
      alignContent: 'center'
    }
  },
  dueAmount: {
    sx: {
      textAlign: 'right',
      textDecorationSkip: 'none',
      color: Design.Alias.Color.accent900,
      fontSize: Design.Primitives.Font.Size.BodyUniversal
    }
  },
  partialPaidAmount: {
    sx: {
      textAlign: 'right',
      textDecorationSkip: 'none',
      textDecoration: 'line-through',
      textUnderlinePosition: 'from-font',
      color: Design.Alias.Color.neutral700
    }
  }
};
