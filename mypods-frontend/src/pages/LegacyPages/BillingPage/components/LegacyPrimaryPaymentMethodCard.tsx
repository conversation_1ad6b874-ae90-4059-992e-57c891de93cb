import React, { useContext, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import isEmpty from 'lodash/isEmpty';
import { Chip, Grid, Typography, useMediaQuery } from '@mui/material';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { NotificationContext } from '../../../../components/notifications/NotificationContext';
import { Design } from '../../../../helpers/Design';
import { theme } from '../../../../PodsTheme';
import { CardIcon, PaymentTypeIcon } from '../../../../components/icons/PaymentTypeIcon';
import {
  isCitiBank,
  PaymentMethod
} from '../../../../networkRequests/responseEntities/PaymentEntities';
import { ROUTES } from '../../../../Routes';
import { BlueLink } from '../../../../components/buttons/NavigationLink';
import { useLegacyGetPaymentMethods } from '../../../../networkRequests/legacy/queries/useLegacyGetPaymentMethods';

export const LegacyPrimaryPaymentMethodCard = () => {
  const { t: translate } = useTranslation();
  const {
    paymentMethods,
    error: paymentsError,
    isSuccess: isPaymentsSuccess,
    isError: isPaymentsError,
    isPending: isPaymentsPending
  } = useLegacyGetPaymentMethods();
  const { setNotification } = useContext(NotificationContext);
  const Tx = TranslationKeys.BillingPage.PaymentMethods.DefaultPaymentMethod;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const styles = style(isMobile);

  useEffect(() => {
    if (paymentsError) {
      setNotification({
        message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
        isError: true
      });
    }
  }, [paymentsError]);

  const primaryPaymentMethod = paymentMethods?.filter(
    (paymentMethod) => paymentMethod.isPrimary
  )[0];

  const renderPaymentDetails = (paymentMethod: PaymentMethod) => {
    const paymentType = paymentMethod.cardType;

    switch (paymentType) {
      case CardIcon.PAYPAL:
        return <Typography {...styles.cardType}>PayPal</Typography>;
      case CardIcon.LINE_OF_CREDIT: {
        if (paymentMethod.locLender?.includes('Upgrade'))
          return (
            <Typography {...styles.cardType}>
              {translate(TranslationKeys.PaymentMethodsPage.PaymentType.MOVE_LOAN_UPGRADE_TEXT)}
            </Typography>
          );
        if (isCitiBank(paymentMethod)) {
          return (
            <Typography {...styles.cardType}>
              {translate(TranslationKeys.PaymentMethodsPage.PaymentType.MOVE_LOAN_CITI_BANK_TEXT)}
            </Typography>
          );
        }
        break;
      }
      default: {
        return (
          <>
            <Typography {...styles.cardType}>{`${paymentMethod.displayCardType}...`}</Typography>
            <Typography {...styles.lastFour}>{paymentMethod.cardNumberLastFourDigits}</Typography>
          </>
        );
      }
    }
  };

  return (
    <>
      {isPaymentsPending && <Typography>Loading payment information...</Typography>}
      {isPaymentsError && <Grid> Womp :( </Grid>}
      {isPaymentsSuccess && primaryPaymentMethod && (
        <>
          <Typography variant={isMobile ? 'h4' : 'h5'} {...styles.paymentHeading}>
            {translate(Tx.HEADER)}
          </Typography>
          <Grid container {...style(isMobile).paymentSection}>
            <Grid {...styles.paymentDetails}>
              <PaymentTypeIcon cardType={primaryPaymentMethod.cardType as CardIcon} />
              {renderPaymentDetails(primaryPaymentMethod)}
            </Grid>
            <Grid>
              <Chip label={translate(TranslationKeys.PaymentMethodsPage.DEFAULT)} size="small" />
            </Grid>
          </Grid>
          <Grid sx={{ marginTop: isMobile ? '16px' : '36px' }}>
            <BlueLink to={ROUTES.VIEW_PAYMENT_METHODS}>
              {translate(Tx.VIEW_PAYMENT_METHODS_LINK)}
            </BlueLink>
          </Grid>
        </>
      )}
      {isPaymentsSuccess && isEmpty(primaryPaymentMethod) && (
        <Grid container {...styles.emptyPaymentMethodSection}>
          <Grid item>
            <Typography variant={isMobile ? 'h4' : 'h5'} {...styles.paymentHeading}>
              {translate(Tx.NOT_FOUND)}
            </Typography>
          </Grid>
          <Grid item>
            <BlueLink to={ROUTES.VIEW_PAYMENT_METHODS}>
              {translate(Tx.VIEW_PAYMENT_METHODS_LINK)}
            </BlueLink>
          </Grid>
        </Grid>
      )}
    </>
  );
};

const style = (isMobile?: boolean) => ({
  paymentHeading: {
    sx: {
      color: Design.Alias.Color.accent900,
      marginBottom: '16px'
    }
  },
  paymentSection: {
    sx: {
      display: 'flex',
      flexDirection: isMobile ? 'row' : 'column',
      justifyContent: 'space-between'
    }
  },
  paymentDetails: {
    sx: {
      display: 'flex',
      marginBottom: isMobile ? 0 : '8px',
      '.MuiGrid-root': { marginBottom: 0 }
    }
  },
  emptyPaymentMethodSection: {
    sx: {
      justifyContent: 'space-between',
      flexDirection: 'column',
      minHeight: '-webkit-fill-available'
    }
  },
  cardType: {
    sx: { color: Design.Alias.Color.accent900, marginLeft: '8px' }
  },
  lastFour: {
    sx: { color: Design.Alias.Color.accent900 }
  }
});
