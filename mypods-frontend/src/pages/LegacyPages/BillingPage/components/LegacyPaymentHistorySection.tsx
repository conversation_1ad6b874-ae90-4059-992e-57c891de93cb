import React from 'react';
import { Accordion, AccordionDetails, AccordionSummary, Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { RightChevronIcon } from '../../../../components/icons/RightChevronIcon';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { BillingInvoice } from '../../../../networkRequests/responseEntities/BillingEntities';
import { BillingCardData, LegacyBillingCard } from './LegacyBillingCard';

type Props = {
  paymentHistory: BillingInvoice[];
  styles: any;
};

export const LegacyPaymentHistorySection: React.FC<Props> = ({ paymentHistory, styles }) => {
  const { t: translate } = useTranslation();
  const convertBillingInvoiceToBillingCardData = (invoice: BillingInvoice): BillingCardData => ({
    amount: invoice.isPaid ? invoice.totalDue : invoice.balanceDue,
    totalAmount: invoice.totalDue,
    currencyType: invoice.currencyType!,
    date: invoice.dueDate!,
    id: invoice.invoiceNumber!,
    documentId: invoice.documentId!,
    invoiceNumber: invoice.invoiceNumber!,
    type: invoice.isPaid ? 'paid-invoice' : 'unpaid-invoice',
    isPoet: invoice.isPoet
  });
  return (
    <Grid data-testid="payment-history-section">
      <Accordion disableGutters defaultExpanded {...styles.accordion}>
        <AccordionSummary
          {...styles.accordionSummary}
          expandIcon={<RightChevronIcon {...styles.rightChevronIcon} />}>
          <Grid container {...styles.accordionHeader}>
            <Typography variant="h4" {...styles.accordionTitle}>
              {translate(TranslationKeys.BillingPage.PaymentHistory.HEADER)}
            </Typography>
          </Grid>
        </AccordionSummary>
        <AccordionDetails {...styles.accordionDetails}>
          <Grid container data-testid="payment-history-content" gap="8px">
            {paymentHistory?.map((payment) => (
              <LegacyBillingCard
                key={payment.invoiceNumber}
                {...convertBillingInvoiceToBillingCardData(payment)}
              />
            ))}
          </Grid>
        </AccordionDetails>
      </Accordion>
    </Grid>
  );
};
