import React from 'react';
import { Grid } from '@mui/material';
import { CheckmarkIcon } from '../../../../components/icons/CheckmarkIcon';
import { ExclamationIcon } from '../../../../components/icons/ExclamationIcon';
import { DocumentIcon } from '../../../../components/icons/DocumentIcon';
import { Design } from '../../../../helpers/Design';
import { BillingCardType } from '../../../../domain/BillingEntities';

// -- types --
interface Props {
  type: BillingCardType;
  hasDatePassed: boolean;
}

// -- impls --
export const LegacyBillingCardIcon: React.FC<Props> = ({ type, hasDatePassed }: Props) => {
  const styles = cardIconStyles;

  if (type === 'paid-invoice') {
    return (
      <Grid item style={{ alignContent: 'center' }}>
        <CheckmarkIcon {...styles.checkmarkIcon} />
      </Grid>
    );
  }
  if (type === 'unpaid-invoice' && hasDatePassed) {
    return (
      <Grid item style={{ alignContent: 'center' }}>
        <ExclamationIcon {...styles.exclamationIcon} />
      </Grid>
    );
  }
  return (
    <Grid item style={{ alignContent: 'center' }}>
      <DocumentIcon />
    </Grid>
  );
};

// -- styles --
const cardIconStyles = {
  checkmarkIcon: {
    style: {
      color: Design.Alias.Color.successMain
    },
    sx: {
      width: '20px',
      height: '20px'
    }
  },
  exclamationIcon: {
    style: {
      color: Design.Alias.Color.errorMain
    }
  }
};
