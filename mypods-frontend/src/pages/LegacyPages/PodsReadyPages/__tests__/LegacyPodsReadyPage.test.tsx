import { PodsReadyRoutes } from '../../../../PodsReadyRoutes';
import {
  mockNavigate,
  mockLegacyStartPodsReadySession,
  mockSendPasswordOnboardingStart
} from '../../../../../setupTests';
import { createPodsReadyClaims } from '../../../../testUtils/MyPodsFactories';
import {
  renderWithLegacyProvidersAndState,
  runPendingPromises
} from '../../../../testUtils/RenderHelpers';
import { LegacyPodsReadyPage } from '../LegacyPodsReadyPage';
import { vi } from 'vitest';

const mockUseShowPodsReadySingleOrder = vi.hoisted(() => vi.fn());
const mockUseLegacyStartPodsReadySession = vi.hoisted(() => vi.fn());

vi.mock('../../../../helpers/legacy/useLegacyShowPodsReadySingleOrder', () => ({
  useLegacyShowPodsReadySingleOrder: mockUseShowPodsReadySingleOrder
}));
vi.mock(
  '../../../../networkRequests/legacy/queries/podsReady/useLegacyStartPodsReadySession',
  () => ({
    useLegacyStartPodsReadySession: mockUseLegacyStartPodsReadySession
  })
);

describe('LegacyPodsReadyPage', () => {
  beforeEach(() => {
    mockLegacyStartPodsReadySession.mockResolvedValue(createPodsReadyClaims());
  });

  const renderPage = async () => {
    const result = renderWithLegacyProvidersAndState(<LegacyPodsReadyPage />);
    await runPendingPromises();
    return result;
  };

  describe('When show pods ready single order is enabled', () => {
    beforeEach(() => {
      mockUseShowPodsReadySingleOrder.mockReturnValue({
        showPodsReadySingleOrder: true,
        isFetching: false
      });
    });

    it('redirects a user with a token param & a password to auth login', async () => {
      mockUseLegacyStartPodsReadySession.mockReturnValue({
        hasToken: true,
        podsReadySessionClaims: createPodsReadyClaims({ hasPassword: true })
      });

      await renderPage();

      expect(mockNavigate).not.toHaveBeenCalled();
    });

    it('redirects a user without a password, but with a token param, to tasks page', async () => {
      mockUseLegacyStartPodsReadySession.mockReturnValue({
        hasToken: true,
        podsReadySessionClaims: createPodsReadyClaims({ hasPassword: false })
      });

      await renderPage();

      expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.TASKS);
    });
  });

  describe('When show pods ready single order is not enabled', () => {
    beforeEach(() => {
      mockUseShowPodsReadySingleOrder.mockReturnValue(false);
    });

    it('redirects a user without a password, but with a token param, to password page and send event', async () => {
      mockUseLegacyStartPodsReadySession.mockReturnValue({
        hasToken: true,
        podsReadySessionClaims: createPodsReadyClaims({ hasPassword: false })
      });

      await renderPage();

      expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.SET_PASSWORD, { replace: true });
      expect(mockSendPasswordOnboardingStart).toHaveBeenCalled();
    });

    it('redirects a user without a password and without token sends start password event', async () => {
      mockUseLegacyStartPodsReadySession.mockReturnValue({
        hasToken: false,
        podsReadySessionClaims: createPodsReadyClaims({ hasPassword: false })
      });

      await renderPage();

      expect(mockSendPasswordOnboardingStart).toHaveBeenCalled();
    });

    it('redirects a user with a token param & a password to auth login', async () => {
      mockUseLegacyStartPodsReadySession.mockReturnValue({
        hasToken: true,
        podsReadySessionClaims: createPodsReadyClaims({ hasPassword: true })
      });

      await renderPage();

      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });
});
