import React, { ReactNode, useEffect, useState } from 'react';
import { AxiosError } from 'axios';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { Button } from 'pods-component-library';
import { css } from 'pods-component-library/styled-system/css';
import { PageLayout } from '../../../../components/PageLayout';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { SetPasswordTextField } from '../../../PodsReadyPages/SetPassword/SetPasswordTextField';
import { NewPasswordTypeaheadValidations } from '../../../AccountPage/AccountFields/PasswordField/NewPasswordTypeaheadValidations';
import { useValidatePassword } from '../../../../helpers/validation/Validators';
import { NewPasswordHelperText } from '../../../AccountPage/AccountFields/PasswordField/NewPasswordHelperText';
import { ErrorResponse } from '../../../../networkRequests/responseEntities/ErrorEntities';
import { PodsReadyCreateAccountStatus } from '../../../../networkRequests/responseEntities/PodsReadyEntities';
import { AccountFieldAlertProps } from '../../../AccountPage/AccountFields/AccountFieldAlert';
import { SetPasswordFieldAlert } from '../../../PodsReadyPages/SetPassword/SetPasswordErrorAlert';
import { useLegacyCreatePodsReadyAccount } from '../../../../networkRequests/legacy/mutations/useLegacyCreatePodsReadyAccount';
import { ROUTES } from '../../../../Routes';
import { PodsReadyRoutes } from '../../../../PodsReadyRoutes';
import { ENV_VARS } from '../../../../environment';
import {
  PODS_READY_SINGLE_ORDER_ENABLED,
  useFeatureFlags
} from '../../../../helpers/useFeatureFlags';
import { useLegacyPasswordOnboardingEvents } from '../../../../config/usePasswordOnboardingEvents';
import { theme } from '../../../../PodsTheme';
import { FixedBottomContainer } from '../../../../components/FixedBottomContainer';

const Tx = TranslationKeys.PodsReady.SetPasswordPage;

// -- impls --
export const LegacySetPasswordPage: React.FC = () => {
  const { t: translate } = useTranslation();
  const { sendPasswordOnboardingComplete } = useLegacyPasswordOnboardingEvents();
  const navigate = useNavigate();
  const [password, setPassword] = useState<string>('');
  const [isDisabled, setIsDisabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [passwordHelperText, setPasswordHelperText] = useState<ReactNode | undefined>();
  const [inputGroupAlert, setInputGroupAlert] = useState<AccountFieldAlertProps | undefined>();
  const { rules, isValid } = useValidatePassword(password);
  const createPodsReadyAccount = useLegacyCreatePodsReadyAccount();
  const { isPodsReadySingleOrderEnabled } = useFeatureFlags([PODS_READY_SINGLE_ORDER_ENABLED]);
  const passwordChangeSuccessRedirect = isPodsReadySingleOrderEnabled()
    ? PodsReadyRoutes.SUCCESS
    : ROUTES.HOME;
  const styles = style(isMobile);
  useEffect(() => {
    setIsDisabled(password.length < 1);
  }, [password]);

  // -- Event Handlers --
  const handlePasswordChange = (value: string) => {
    setPassword(value);
    setPasswordHelperText(undefined);
  };

  const handleSaveButton = () => {
    setPasswordHelperText(undefined);

    if (isValid) {
      setIsDisabled(true);
      setIsLoading(true);
      createPodsReadyAccount.mutate(
        { password },
        {
          onSuccess: () => {
            sendPasswordOnboardingComplete();
            navigate(passwordChangeSuccessRedirect, { replace: true });
          },
          onError: (error: unknown) => {
            displayErrorNotification(error);
          },
          onSettled: () => {
            setIsDisabled(false);
            setIsLoading(false);
          }
        }
      );
    } else {
      setPasswordHelperText(<NewPasswordHelperText rules={rules} />);
    }
  };

  const setDefaultErrorAlert = () => {
    setInputGroupAlert({
      title: translate(Tx.Notifications.Title.DEFAULT),
      message: translate(Tx.Notifications.Message.DEFAULT)
    });
  };

  const displayErrorNotification = (error: unknown) => {
    if (error instanceof AxiosError && error.response?.data?.status) {
      const errorResponse = error.response.data as ErrorResponse;
      const status = errorResponse.status as PodsReadyCreateAccountStatus;
      switch (status) {
        case PodsReadyCreateAccountStatus.USERNAME_ALREADY_TAKEN:
        case PodsReadyCreateAccountStatus.CUSTOMER_ID_TAKEN:
        case PodsReadyCreateAccountStatus.INVALID_PASSWORD: {
          setInputGroupAlert({
            title: translate(Tx.Notifications.Title[status]),
            message: translate(Tx.Notifications.Message[status])
          });
          return;
        }
        case PodsReadyCreateAccountStatus.ERROR:
        default: {
          setDefaultErrorAlert();
        }
      }
    } else {
      setDefaultErrorAlert();
    }
  };

  const renderButton = () => (
    <Button
      type="submit"
      variant="filled"
      buttonSize="large"
      color="primary"
      isDisabled={isDisabled}
      isLoading={isLoading}
      css={css.raw({ ...styles.button })}>
      {translate(Tx.BUTTON_TEXT)}
    </Button>
  );

  const handleFormSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    handleSaveButton();
  };

  return (
    <Grid container {...styles.container}>
      <PageLayout columnsLg={4}>
        <Grid container item {...styles.container}>
          <form onSubmit={handleFormSubmit} style={{ width: '100%' }}>
            <Grid container {...styles.form}>
              <Grid>
                <Grid item {...styles.logoPadding}>
                  <img
                    src={`${ENV_VARS.ASSETS_BASE_URL}/pods-secondary-logo-rgb-1.webp`}
                    alt="pods-logo"
                    {...styles.logo}
                  />
                </Grid>
                {inputGroupAlert && (
                  <SetPasswordFieldAlert
                    title={inputGroupAlert.title}
                    message={inputGroupAlert.message}
                  />
                )}
                <Typography {...styles.introText}>{translate(Tx.INTRO)}</Typography>
                <Typography {...styles.titleText}>{translate(Tx.TITLE)}</Typography>
                <Typography {...styles.setupText}>{translate(Tx.SUBTITLE)}</Typography>
              </Grid>
              <Grid>
                <SetPasswordTextField
                  hasAutoFocus={false}
                  onChange={handlePasswordChange}
                  helperText={passwordHelperText}
                />
              </Grid>
              <NewPasswordTypeaheadValidations rules={rules} />
              <Grid item {...styles.buttonGroup}>
                {isMobile ? (
                  <FixedBottomContainer>{renderButton()}</FixedBottomContainer>
                ) : (
                  renderButton()
                )}
              </Grid>
            </Grid>
          </form>
        </Grid>
      </PageLayout>
    </Grid>
  );
};

// -- styles --
const style = (isMobile: boolean) => ({
  container: {
    sx: {
      height: '100vh',
      justifyContent: 'center',
      alignItems: isMobile ? '' : 'center'
    }
  },
  form: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      maxHeight: '800px',
      gap: Design.Primitives.Spacing.md,
      padding: Design.Primitives.Spacing.sm,
      borderRadius: Design.Primitives.Spacing.xxs,
      border: isMobile ? '' : `1px solid ${Design.Alias.Color.neutral300}`,
      background: Design.Alias.Color.neutral100,
      boxShadow: isMobile ? '' : `0px 4px 6px 0px rgba(0, 0, 0, 0.10)`
    }
  },
  introText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: Design.Alias.Color.neutral700
    }
  },
  titleText: {
    sx: {
      ...Design.Alias.Text.Heading.Desktop.Lg,
      fontWeight: 900,
      color: Design.Alias.Color.secondary900
    }
  },
  setupText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      color: Design.Alias.Color.neutral700
    }
  },
  logo: {
    height: '22px'
  },
  logoPadding: { paddingBottom: Design.Primitives.Spacing.md },
  button: {
    paddingY: '12px',
    paddingX: '16px'
  },
  buttonGroup: {
    sx: {
      marginTop: 'auto'
    }
  }
});
