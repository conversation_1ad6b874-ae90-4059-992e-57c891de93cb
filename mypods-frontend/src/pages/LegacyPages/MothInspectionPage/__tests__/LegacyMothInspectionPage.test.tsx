import { screen, waitFor } from '@testing-library/react';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { renderWithPoetProvidersAndState } from '../../../../testUtils/RenderHelpers';
import { ROUTES } from '../../../../Routes';
import { mockNavigate, mockRefreshSession } from '../../../../../setupTests';
import { createRefreshSessionClaims } from '../../../../testUtils/MyPodsFactories';
import { LegacyMothInspectionPage } from '../LegacyMothInspectionPage';

describe('Moth Inspection Page', () => {
  let user: UserEvent;

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    user = userEvent.setup();
  });

  const renderPage = () => {
    renderWithPoetProvidersAndState(<LegacyMothInspectionPage />);
  };

  it('should navigate back when back button clicked', async () => {
    renderPage();

    await screen.findByTestId('back-link');
    await waitFor(() => user.click(screen.getByTestId('back-link')));

    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it('should navigate to moth and fly inspection form page on next click', async () => {
    renderPage();

    await screen.findByTestId('back-link');
    await waitFor(async () =>
      user.click(
        await screen.findByRole('button', { name: TranslationKeys.CommonComponents.NEXT_BUTTON })
      )
    );
    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.MOTH_FLY_INSPECTION_FORM);
  });
});
