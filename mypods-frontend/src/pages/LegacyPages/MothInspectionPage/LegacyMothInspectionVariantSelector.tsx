import React, { useContext, useEffect } from 'react';
import { useImmer } from 'use-immer';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import isEmpty from 'lodash/isEmpty';
import { PageLayout } from '../../../components/PageLayout';
import { MOTH_PAGINATION_ENABLED, useFeatureFlags } from '../../../helpers/useFeatureFlags';
import {
  MothCategoryState,
  MothCheckboxConfig
} from '../../MothFlyInspectionFormPage/MothInspectionFormTypes';
import { mothFormConfig } from '../../MothFlyInspectionFormPage/mothInspectionConfig';
import { useEntryPointContext } from '../../../context/EntryPointContext';
import { addSignedMothOrderId, unsignedMothAgreements } from '../../../helpers/entrypointHelpers';
import { ViewAgreementSteps } from '../../../config/google/GoogleEntities';
import { LegacyPaginatedMothFlyInspectionForm } from '../../MothFlyInspectionFormPage/Paginated/PaginatedMothFlyInspectionForm';
import { useSplitEvents } from '../../../config/useSplitEvents';
import { NonPaginatedMothContainer } from '../../MothFlyInspectionFormPage/NonPaginatedMothContainer';
import { useGtmEventsWithCustomer } from '../../../config/google/useGtmEvents';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { SignMothAgreementRequest } from '../../../networkRequests/responseEntities/DocumentApiEntities';
import { getTimezoneOffset } from '../../../helpers/dateHelpers';
import { useLegacyGetCustomer } from '../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import useLegacyOrdersContext from '../../../context/legacy/LegacyOrdersContext';
import { formatAddress } from '../../../networkRequests/responseEntities/CustomerEntities';
import { Order } from '../../../domain/OrderEntities';
import { SplitEventType } from '../../../config/SplitEventTypes';
import { ROUTES } from '../../../Routes';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { useLegacySignMothAgreement } from '../../../networkRequests/legacy/mutations/useLegacySignMothAgreement';

const Tx = TranslationKeys.MothInspectionFormPage;
const OTHER_CHECKBOX_LABEL = 'Other';

export const LegacyMothInspectionVariantSelector = () => {
  const { t: translate } = useTranslation();
  const { customer } = useLegacyGetCustomer();
  const { orders } = useLegacyOrdersContext();
  const navigate = useNavigate();
  const location = useLocation();
  const { entryPointResult, setEntryPointResult } = useEntryPointContext();
  const { setNotification } = useContext(NotificationContext);
  const splitEvents = useSplitEvents(customer.id);
  const gtmEvents = useGtmEventsWithCustomer(customer);
  const { isMothPaginationEnabled } = useFeatureFlags([MOTH_PAGINATION_ENABLED]);
  const [checkboxState, setCheckboxState] = useImmer<MothCheckboxConfig>(mothFormConfig);

  const signMothAgreement = useLegacySignMothAgreement();
  const [firstOutstandingMothAgreement, ...remainingOutstandingMothAgreements] =
    unsignedMothAgreements(entryPointResult);
  const remainingRentalAgreements = entryPointResult.outstandingRentalAgreements;
  useEffect(() => {
    splitEvents.send(SplitEventType.MOTH_FORM_START);
    gtmEvents.viewAgreementStep(
      ViewAgreementSteps.moth.form,
      firstOutstandingMothAgreement?.orderId ?? ''
    );
  }, []);

  const findFirstMoveLegsOfTypeInitialDelivery = () => {
    const currentOrder = orders.find(
      (order: Order) => order.orderId === firstOutstandingMothAgreement.orderId
    );
    return currentOrder?.containers?.[0].moveLegs.find(
      (moveLeg) => moveLeg.moveLegType === 'INITIAL_DELIVERY'
    );
  };

  const formattedInspectionAddress = () => {
    const firstMoveLeg = findFirstMoveLegsOfTypeInitialDelivery();
    if (firstMoveLeg) {
      return formatAddress(firstMoveLeg.displayAddress);
    }
    return formatAddress(customer.billingAddress);
  };

  const handleSubmit = () => {
    const { selectedBoxes, otherTextBoxes }: { [k: string]: any[] } = getSelectedCheckboxes();

    const request: SignMothAgreementRequest = {
      orderId: firstOutstandingMothAgreement.orderId,
      firstName: customer.firstName,
      lastName: customer.lastName,
      email: customer.email?.address ?? '',
      phone: customer.primaryPhone?.number ?? customer.secondaryPhone?.number ?? '',
      address: formattedInspectionAddress(),
      selectedCheckboxes: selectedBoxes,
      otherCheckboxes: otherTextBoxes,
      ...getTimezoneOffset()
    };
    gtmEvents.submitAgreement('moth_fly_inventory', request.orderId);

    signMothAgreement.mutate(request, {
      onSuccess: () => {
        gtmEvents.successAgreement('moth_fly_inventory', request.orderId);
        setNotification({
          isError: false,
          message: translate(Tx.SUCCESS_MESSAGE)
        });
        addSignedMothOrderId(request.orderId);
        setEntryPointResult({
          ...entryPointResult,
          outstandingMothAgreements: remainingOutstandingMothAgreements
        });

        splitEvents.send(SplitEventType.MOTH_FORM_SUCCESS);
        if (isEmpty(remainingRentalAgreements) && isEmpty(remainingOutstandingMothAgreements)) {
          splitEvents.send(SplitEventType.PODS_READY_COMPLETE);
        }
        navigate(location.state?.onSuccessRoute ?? ROUTES.HOME, { replace: true });
      },
      onError: () => {
        setNotification({
          isError: true,
          message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE)
        });
      }
    });
  };

  const getSelectedCheckboxes = () =>
    checkboxState.reduce(
      (selections: { [k: string]: any[] }, element: MothCategoryState) => {
        element.subcategories.forEach((subCategories) => {
          subCategories.checkboxes.forEach((checkbox) => {
            if (!checkbox.checked) {
              return;
            }

            if (checkbox.label === OTHER_CHECKBOX_LABEL) {
              selections.otherTextBoxes.push({
                id: checkbox.id,
                description: checkbox.description ?? ''
              });
            } else {
              selections.selectedBoxes.push(checkbox.id);
            }
          });
        });
        return selections;
      },
      { selectedBoxes: [], otherTextBoxes: [] }
    );

  if (!firstOutstandingMothAgreement) {
    return <div>No outstanding agreement found</div>;
  }

  return (
    <PageLayout columnsLg={9}>
      {isMothPaginationEnabled() ? (
        <LegacyPaginatedMothFlyInspectionForm
          checkboxState={checkboxState}
          setCheckboxState={setCheckboxState}
          handleSubmit={handleSubmit}
          orderId={firstOutstandingMothAgreement.orderId}
          isPending={signMothAgreement.isPending}
          customer={customer}
        />
      ) : (
        <NonPaginatedMothContainer
          checkboxState={checkboxState}
          setCheckboxState={setCheckboxState}
          handleSubmit={handleSubmit}
          firstOutstandingMothAgreementOrderId={firstOutstandingMothAgreement.orderId}
          isPending={signMothAgreement.isPending}
          customer={customer}
        />
      )}
    </PageLayout>
  );
};
