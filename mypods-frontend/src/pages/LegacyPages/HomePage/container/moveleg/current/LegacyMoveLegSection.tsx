import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useState } from 'react';
import Divider from '@mui/material/Divider';
import { addDays } from 'date-fns';
import useContainerContext from '../../../../../../context/ContainerContext';
import useSingleOrderContext from '../../../../../../context/SingleOrderContext';
import { theme } from '../../../../../../PodsTheme';
import { useGtmEvents } from '../../../../../../config/google/useGtmEvents';
import { ScheduledStatus } from '../../../../../../domain/OrderEntities';
import {
  ContainerProgressLine,
  ProgressLineVariant
} from '../../../../../HomePage/container/ContainerProgressLine';
import { isWithin24Hours } from '../../../../../../helpers/dateHelpers';
import { getDateLabels } from '../../../../../../locales/TranslationConstants';
import { InTransitMoveLeg } from '../../../../../HomePage/container/moveleg/current/InTransitMoveLeg';
import { CallForSpecialAssistance } from '../../../../../HomePage/container/moveleg/CallForSpecialAssistance';
import { EditButton } from '../../../../../../components/buttons/EditButton';
import { ServiceCountdownAlert } from '../../../../../HomePage/container/serviceCountdown/ServiceCountdownAlert';
import { DateComponent } from '../../../../../HomePage/container/moveleg/DateComponent';
import { AddressComponent } from '../../../../../HomePage/container/moveleg/AddressComponent';
import { formatAddress } from '../../../../../../networkRequests/responseEntities/CustomerEntities';
import { PickupWindow } from '../../../../../HomePage/container/moveleg/PickupWindow';
import { ScheduleMoveLegButton } from '../../../../../HomePage/container/moveleg/current/ScheduleMoveLegButton';
import { CallToScheduleComponent } from '../../../../../HomePage/container/moveleg/CallToScheduleComponent';
import { sharedMoveLegStyles } from '../../../../../HomePage/container/moveleg/sharedMoveLegStyles';
import { LegacyVisitContainerSection } from '../LegacyVisitContainerSection';
import { LegacyReviewInitialDeliveryAlert } from '../../../../../../components/legacyComponents/alert/LegacyReviewInitialDeliveryAlert';
import useLegacyMoveLegContext from '../LegacyMoveLegContext';
import { LegacyScheduleMoveLeg } from '../../scheduling/LegacyScheduleMoveLeg';
import { ManageMoveContainer } from '../../../../../HomePage/Scheduling/ManageMoveContainer';
import { REDESIGNED_SCHEDULING, useFeatureFlags } from '../../../../../../helpers/useFeatureFlags';

export const LegacyMoveLegSection = () => {
  const { moveLeg, isLastMoveLeg, isLastRenderedMoveLeg, title } = useLegacyMoveLegContext();
  const { order, container } = useContainerContext();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { moveLegScheduling } = useSingleOrderContext();
  const {
    currentlySelectedMoveLeg,
    editMoveLegScheduling,
    stopMoveLegScheduling,
    isSaving,
    isCancelling
  } = moveLegScheduling;
  const { isRedesignedSchedulingEnabled } = useFeatureFlags([REDESIGNED_SCHEDULING]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = moveLegStyles(isMobile);
  const isUnscheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate == null;
  const isScheduledContainerVisit =
    moveLeg.moveLegType === 'VISIT_CONTAINER' && moveLeg.containerVisitDate != null;
  const isSchedulingMoveLeg = currentlySelectedMoveLeg === moveLeg && !isCancelling;
  const { startSchedule, startEditSchedule } = useGtmEvents();
  const [showInitialDeliveryAlert, setShowInitialDeliveryAlert] = useState<boolean>(
    moveLeg.moveLegType === 'INITIAL_DELIVERY' && !order.initialDeliveryPlacementIsReviewed
  );
  const [isEditingGtmEvent, setIsEditingGtmEvent] = useState<boolean>(false);

  const toggleEditMoveLegOn = () => {
    editMoveLegScheduling(moveLeg);
  };

  const hideInitialDeliveryAlert = () => {
    setShowInitialDeliveryAlert(false);
  };

  const gtmSchedulePayLoad = {
    transactionId: order.orderId,
    moveLegId: moveLeg.moveLegId,
    moveLegType: moveLeg.moveLegType.toString(),
    containerId: container.containerId
  };

  function isCallToScheduleLeg(): boolean {
    return (
      moveLeg.moveLegType === 'WAREHOUSE_TO_WAREHOUSE' &&
      moveLeg.scheduledStatus === 'UNSCHEDULED' &&
      !shouldCallForAssistance()
    );
  }

  const scheduledStatusToProgressLineVariant = (
    scheduledStatus: ScheduledStatus
  ): ProgressLineVariant => {
    if (scheduledStatus === 'UNSCHEDULED') {
      return 'FADED_DASHED';
    }
    return 'DASHED';
  };

  const shouldRenderScheduleButton = () => {
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.isCityService) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    if (isUnscheduledContainerVisit) return true;
    if (moveLeg.scheduledStatus !== 'UNSCHEDULED') return false;
    return true;
  };

  const shouldRenderEditButton = () => {
    if (isSchedulingMoveLeg) return false;
    if (!moveLeg.isSchedulableOnline) return false;
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') return false;
    if (moveLeg.scheduledStatus !== 'FUTURE') return false;
    if (isWithin24Hours(moveLeg.scheduledDate)) return false;
    if (moveLeg.isCrossBorder) return false;
    if (moveLeg.isHawaii) return false;
    if (moveLeg.isCityService) return false;
    return true;
  };

  const shouldCallForAssistance = () => {
    if (moveLeg.isTransitLeg) return false;
    if (moveLeg.isCrossBorder) return true;
    if (moveLeg.isHawaii) return true;
    if (moveLeg.isCityService) return true;
    return false;
  };

  // TODO: move dateLabels and dateValue logic in one location
  const dateLabels = getDateLabels(moveLeg.moveLegType);

  function getFirstDateValue() {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.arrivalDate;
    }
    return moveLeg.scheduledDate;
  }

  const getSecondDateValue = () => {
    if (moveLeg.moveLegType === 'VISIT_CONTAINER') {
      return moveLeg.moveDate;
    }
    if (moveLeg.scheduledDate) {
      return addDays(moveLeg.scheduledDate, moveLeg.transitDays);
    }
    return undefined;
  };

  if (moveLeg.isTransitLeg) return <InTransitMoveLeg />;

  return (
    <Grid container {...styles.moveLegSection} data-testid={`move-leg-${moveLeg.moveLegId}`}>
      <Grid item>
        <ContainerProgressLine
          variant={scheduledStatusToProgressLineVariant(moveLeg.scheduledStatus)}
          isUpNext={moveLeg.isUpNext}
          isFinal={isLastMoveLeg}
          dataTestId={`progress-line-${moveLeg.moveLegId}`}
        />
      </Grid>
      <Grid item xs {...styles.mainBody}>
        <Grid container {...styles.titleContainer} data-testid="move-leg-title-container">
          <Grid container item xs {...styles.titleWithEdit}>
            <Typography
              color="inherit"
              variant="h4"
              {...styles.titleText}
              data-testid="move-leg-title">
              {title}
            </Typography>
          </Grid>
          {shouldRenderEditButton() && (
            <EditButton
              dataTestId="move-leg"
              onClick={() => {
                setIsEditingGtmEvent(true);
                startEditSchedule(gtmSchedulePayLoad);
                toggleEditMoveLegOn();
              }}
              disabled={isSaving}
            />
          )}
        </Grid>
        {shouldCallForAssistance() && <CallForSpecialAssistance />}
        <ServiceCountdownAlert moveLeg={moveLeg} />
        {showInitialDeliveryAlert && (
          <LegacyReviewInitialDeliveryAlert hideAlertCallback={hideInitialDeliveryAlert} />
        )}
        <Grid container {...styles.detailsContainer}>
          <Grid container {...styles.detailsContainer}>
            <DateComponent
              firstDateLabel={dateLabels.firstDateLabel}
              firstDateValue={getFirstDateValue()}
              secondDateLabel={dateLabels.secondDateLabel}
              secondDateValue={getSecondDateValue()}
            />
            <AddressComponent moveLeg={moveLeg} address={formatAddress(moveLeg.displayAddress)} />
          </Grid>
          {moveLeg.moveLegType === 'SELF_FINAL_PICKUP' && moveLeg.scheduledDate && (
            <PickupWindow etaWindow={moveLeg.eta} />
          )}
          {isSchedulingMoveLeg ? (
            <LegacyScheduleMoveLeg
              onStopScheduling={stopMoveLegScheduling}
              isEditingGtmEvent={isEditingGtmEvent}
            />
          ) : (
            shouldRenderScheduleButton() && (
              <ScheduleMoveLegButton
                moveLeg={moveLeg}
                onClick={() => {
                  setIsEditingGtmEvent(false);
                  startSchedule(gtmSchedulePayLoad);
                  if (isRedesignedSchedulingEnabled()) {
                    setIsDrawerOpen(true);
                  } else {
                    toggleEditMoveLegOn();
                  }
                }}
                disabled={isSaving}
              />
            )
          )}
          {isCallToScheduleLeg() && (
            <CallToScheduleComponent
              isScheduled={moveLeg.scheduledStatus === 'FUTURE'}
              onlineScheduleEnabled
            />
          )}
        </Grid>
        {/* // TODO Test that it should be schedulable online */}
        {isScheduledContainerVisit && moveLeg.isSchedulableOnline && !isSchedulingMoveLeg && (
          <LegacyVisitContainerSection
            moveLeg={moveLeg}
            onEdit={() => {
              setIsEditingGtmEvent(true);
              startEditSchedule(gtmSchedulePayLoad);
              toggleEditMoveLegOn();
            }}
          />
        )}

        {isLastRenderedMoveLeg ? (
          <Grid container {...styles.dividerContainer}>
            <Divider />
          </Grid>
        ) : (
          <Grid container style={{ height: '24px' }} />
        )}
        <ManageMoveContainer
          isOpen={isDrawerOpen}
          onClose={() => setIsDrawerOpen(false)}
          moveLeg={moveLeg}
        />
      </Grid>
    </Grid>
  );
};

const moveLegStyles = (isMobile: boolean) => ({
  ...sharedMoveLegStyles(isMobile),
  titleWithEdit: {
    sx: {
      flexDirection: 'row',
      alignItems: 'center'
    }
  }
});
