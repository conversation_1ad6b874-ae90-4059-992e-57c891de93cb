import { useContext, useState } from 'react';
import { utcToZonedTime, zonedTimeToUtc } from 'date-fns-tz';
import {
  addDays,
  format,
  isAfter,
  isBefore,
  isSameDay,
  max,
  startOfDay,
  startOfMonth
} from 'date-fns';
import { MoveLeg } from '../../../../../domain/OrderEntities';
import { useLegacyGetContainerAvailability } from '../../../../../networkRequests/legacy/mutations/useLegacyGetContainerAvailability';
import { ContainerContext } from '../../../../../context/ContainerContext';
import {
  ContainerAvailability,
  ContainerAvailabilityRequest
} from '../../../../../networkRequests/responseEntities/AvailabilityAPIEntities';

export type DateTimestamp = string;

export interface IMoveLegDateState {
  selectedDate: Date | null;
  setSelectedDate: (date: Date) => void;
  containerAvailabilityPending: boolean;
  addContainerAvailabilitiesFor3Months: (startDate: Date) => void;
  getCalendarStartDate: () => Date;
  getCalendarStartDateWithAvailability: () => Date;
  isAvailable: (date: Date) => boolean;
  selectedDateIsValid: () => boolean;
  selectedDateIsDifferent: () => boolean;
}

export const useLegacyMoveLegDateState = (moveLeg: MoveLeg) => {
  const { container, order } = useContext(ContainerContext);
  const [selectedDate, setSelectedDateInEngine] = useState<Date | null>(
    moveLeg.scheduledDate ?? null
  );
  const [availabilityMap, setAvailabilityMap] = useState<Map<DateTimestamp, ContainerAvailability>>(
    new Map<DateTimestamp, ContainerAvailability>()
  );

  const getContainerAvailability = useLegacyGetContainerAvailability();
  const firstAvailable = moveLeg.firstAvailableDate;
  const lastAvailable = moveLeg.lastAvailableDate;

  const currentTimeInEastern = utcToZonedTime(
    zonedTimeToUtc(new Date(), Intl.DateTimeFormat().resolvedOptions().timeZone),
    'US/Eastern'
  );
  const defaultEarliestAvailability = startOfDay(
    addDays(currentTimeInEastern, currentTimeInEastern.getHours() >= 17 ? 2 : 1)
  );

  const setSelectedDate = (date: Date) => {
    if (isAvailable(date)) setSelectedDateInEngine(date);
    else setSelectedDateInEngine(null);
  };

  const addAvailability = (availability: ContainerAvailability[]) => {
    const newMap = new Map(availabilityMap);
    availability.forEach((it) => {
      newMap.set(it.date, it);
    });
    setAvailabilityMap(newMap);
  };

  const addContainerAvailabilitiesFor3Months = (startDate: Date) => {
    const dateToCheck = max([startOfMonth(startDate), defaultEarliestAvailability]);
    const formattedDate = format(dateToCheck, 'yyyy-MM-dd');

    if (hasEnoughAvailabilityInformation(dateToCheck)) return;

    const request: ContainerAvailabilityRequest = {
      date: formattedDate,
      postalCode: moveLeg.displayAddress.postalCode!,
      orderType: order.orderType,
      moveLegType: moveLeg.moveLegType,
      siteIdentity: moveLeg.siteIdentity
    };
    getContainerAvailability.mutate(request, {
      onSuccess: (data) => {
        if (container.containerSize === '8') addAvailability(data.eightFootAvailability);
        else if (container.containerSize === '12') addAvailability(data.twelveFootAvailability);
        else if (container.containerSize === '16') addAvailability(data.sixteenFootAvailability);
      }
    });
  };

  const getCalendarStartDate = (): Date => {
    if (isBefore(firstAvailable, defaultEarliestAvailability)) return defaultEarliestAvailability;
    return firstAvailable;
  };

  const getCalendarStartDateWithAvailability = (): Date => {
    // the start date not accounting for container availability
    const startDate = getCalendarStartDate();
    // but if this date is not available, we need to iterate over the availability to find the
    // first available date after this start date
    const sortedAvailability = Array.from(availabilityMap.keys())
      .sort()
      .map((it) => new Date(it));
    const firstAvailableStartDate = sortedAvailability.find((it) => {
      const isOnOrAfterStartDate = isSameDay(it, startDate) || isAfter(it, startDate);
      if (isOnOrAfterStartDate && isAvailable(it)) return true;
      return false;
    });
    return firstAvailableStartDate ?? startDate;
  };

  const getAvailability = (date: Date) => availabilityMap.get(format(date, 'yyyy-MM-dd'));

  const hasEnoughAvailabilityInformation = (dateToCheck: Date): boolean =>
    availabilityMap.size > 0 &&
    getAvailability(dateToCheck) != null &&
    getAvailability(new Date(dateToCheck.getFullYear(), dateToCheck.getMonth() + 1, 0)) != null &&
    getAvailability(new Date(dateToCheck.getFullYear(), dateToCheck.getMonth() + 1, 1)) != null &&
    getAvailability(new Date(dateToCheck.getFullYear(), dateToCheck.getMonth() + 2, 1)) != null;

  const isAvailable = (date: Date) => {
    if (isBefore(date, firstAvailable)) return false;
    if (isAfter(date, lastAvailable)) return false;

    return getAvailability(date)?.isAvailable ?? false;
  };

  const selectedDateIsValid = () => selectedDate != null;
  const selectedDateIsDifferent = () =>
    selectedDate != null &&
    (moveLeg.scheduledDate == null || !isSameDay(selectedDate, moveLeg.scheduledDate));

  return {
    selectedDate,
    setSelectedDate,
    containerAvailabilityPending: getContainerAvailability.isPending,
    addContainerAvailabilitiesFor3Months,
    getCalendarStartDate,
    getCalendarStartDateWithAvailability,
    isAvailable,
    selectedDateIsValid,
    selectedDateIsDifferent
  } as IMoveLegDateState;
};
