import React from 'react';
import { Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useLegacyContainerPlacementContext } from '../context/LegacyContainerPlacementContext';
import { TranslationKeys } from '../../../../../../../locales/TranslationKeys';
import { ContainerPlacementContent } from '../../../../../../HomePage/container/scheduling/containerplacement/ContainerPlacementContent';
import { ContainerPlacementHeader } from '../../../../../../HomePage/container/scheduling/containerplacement/ContainerPlacementHeader';
import { ContainerPlacementActionButton } from '../../../../../../HomePage/container/scheduling/containerplacement/ContainerPlacementActionButton';

const Tx = TranslationKeys.HomePage.ContainerPlacement;

export const LegacyPlacementTipsScreen = () => {
  const styles = placementTipsScreenStyle();
  const { t: translate } = useTranslation();
  const { manager } = useLegacyContainerPlacementContext();

  const handleNextClick = () => {
    manager.selectChoice('anything');
  };

  return (
    <ContainerPlacementContent>
      <ContainerPlacementHeader titleKey={Tx.PlacementTips.TITLE} />
      <Grid item container {...styles.listContainer}>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM1)}</Typography>
        </Grid>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM2)}</Typography>
        </Grid>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM3)}</Typography>
        </Grid>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM4)}</Typography>
        </Grid>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM5)}</Typography>
        </Grid>
        <Grid item container>
          <Typography>{translate(Tx.PlacementTips.ITEM6)}</Typography>
        </Grid>
      </Grid>
      <ContainerPlacementActionButton
        label={translate(TranslationKeys.CommonComponents.NEXT_BUTTON)}
        onClick={handleNextClick}
      />
    </ContainerPlacementContent>
  );
};

const placementTipsScreenStyle = () => ({
  listContainer: {
    sx: {
      gap: '.5rem',
      paddingLeft: '.25rem'
    }
  }
});
