import Button from '@mui/material/Button';
import React from 'react';
import { useLegacyContainerPlacementContext } from '../context/LegacyContainerPlacementContext';
import { SiteTypeSelectionWrapper } from '../../../../../../HomePage/container/scheduling/containerplacement/screens/SiteTypeSelectionWrapper';
import {
  getImageForFinalPlacement,
  getImageForScreen
} from '../../../../../../HomePage/container/scheduling/containerplacement/screenmanager/getImages';
import { FinalContainerPlacement } from '../../../../../../../domain/OrderEntities';
import { SiteTypeChildScreens } from '../../../../../../HomePage/container/scheduling/containerplacement/screenmanager/ContainerPlacementModalTypes';

export type SiteTypeSelectionNonFinalProps = {
  siteTypes: SiteTypeChildScreens[];
  prompt: string;
};

export type SiteTypeSelectionFinalProps = {
  selections: FinalContainerPlacement[];
  prompt: string;
};

export const LegacySiteTypeNonFinal = ({ siteTypes, prompt }: SiteTypeSelectionNonFinalProps) => {
  const { manager } = useLegacyContainerPlacementContext();
  const handleOnClick = (screen: SiteTypeChildScreens) => {
    manager.selectChoiceWithChild('anything', screen);
  };

  return (
    <SiteTypeSelectionWrapper prompt={prompt}>
      {siteTypes.map((type) => (
        <Button key={type} onClick={() => handleOnClick(type)}>
          <img alt={type} src={getImageForScreen(type)} />
        </Button>
      ))}
    </SiteTypeSelectionWrapper>
  );
};

export const LegacySiteTypeFinal = ({ selections, prompt }: SiteTypeSelectionFinalProps) => {
  const { setContainerPlacement, manager } = useLegacyContainerPlacementContext();
  const handleOnClick = (selection: FinalContainerPlacement) => {
    setContainerPlacement((prev) => ({
      ...prev,
      placement: selection
    }));
    manager.selectChoice(selection);
  };

  return (
    <SiteTypeSelectionWrapper prompt={prompt}>
      {selections.map((type) => (
        <Button key={type} onClick={() => handleOnClick(type)}>
          <img alt={type} src={getImageForFinalPlacement(type)} />
        </Button>
      ))}
    </SiteTypeSelectionWrapper>
  );
};
