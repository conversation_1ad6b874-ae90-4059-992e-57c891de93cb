import React, { useEffect } from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { LegacyContainerPlacementNavigationHeader } from './LegacyContainerPlacementNavigationHeader';
import { PodsModalContent } from '../../../../../../components/Modals/PodsModalBody';
import useLegacyMoveLegContext from '../../moveleg/LegacyMoveLegContext';
import { useGtmEvents } from '../../../../../../config/google/useGtmEvents';
import { useLegacyContainerPlacementContext } from './context/LegacyContainerPlacementContext';
import { theme } from '../../../../../../PodsTheme';
import useContainerContext from '../../../../../../context/ContainerContext';
import { PodsModal } from '../../../../../../components/Modals/PodsModal';

export type ContainerPlacementModalProps = {
  open: boolean;
  handleOnClose: () => void;
};

export const LegacyContainerPlacementModal = ({
  open,
  handleOnClose
}: ContainerPlacementModalProps) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { manager } = useLegacyContainerPlacementContext();
  const { order, container } = useContainerContext();
  const { moveLeg } = useLegacyMoveLegContext();
  const gtmEvents = useGtmEvents();

  useEffect(() => {
    if (open) {
      const args = {
        orderId: order.orderId,
        containerId: container.containerId,
        moveLegId: moveLeg.moveLegId,
        moveLegType: moveLeg.moveLegType,
        stepName: manager.currentScreen.name
      };
      gtmEvents.viewPlacementStep(args);
    }
  }, [manager.currentScreen.name, open]);

  const renderCurrentScreen = () => manager.currentScreen.component;

  return (
    <PodsModal open={open}>
      <Grid sx={{ ...(!isMobile && { minHeight: '704px' }) }}>
        <LegacyContainerPlacementNavigationHeader handleOnClosed={handleOnClose} />
        <PodsModalContent>{renderCurrentScreen()}</PodsModalContent>
      </Grid>
    </PodsModal>
  );
};
