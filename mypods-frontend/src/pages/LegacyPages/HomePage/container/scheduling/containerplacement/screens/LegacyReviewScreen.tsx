import { Grid } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../../../../../locales/TranslationKeys';
import { ContainerPlacementHeader } from '../../../../../../HomePage/container/scheduling/containerplacement/ContainerPlacementHeader';
import { ContainerPlacementContent } from '../../../../../../HomePage/container/scheduling/containerplacement/ContainerPlacementContent';
import { ContainerPlacementActionButton } from '../../../../../../HomePage/container/scheduling/containerplacement/ContainerPlacementActionButton';
import { useLegacyContainerPlacementContext } from '../context/LegacyContainerPlacementContext';
import { LegacyReviewContainerPlacement } from './LegacyReviewContainerPlacement';

const Tx = TranslationKeys.HomePage.ContainerPlacement;

export const LegacyReviewScreen = () => {
  const styles = reviewScreenStyles();
  const { t: translate } = useTranslation();
  const { containerPlacement, handleFinish } = useLegacyContainerPlacementContext();

  return (
    <ContainerPlacementContent>
      <ContainerPlacementHeader
        titleKey={Tx.ReviewScreen.TITLE}
        subTitleKey={Tx.ReviewScreen.SUBTITLE}
      />
      <Grid container item {...styles.reviewContainer}>
        <LegacyReviewContainerPlacement />
      </Grid>
      <ContainerPlacementActionButton
        label={translate(Tx.ReviewScreen.FINISH_BUTTON)}
        onClick={() => handleFinish(containerPlacement)}
      />
    </ContainerPlacementContent>
  );
};

const reviewScreenStyles = () => ({
  reviewContainer: {
    sx: {
      flexDirection: 'column'
    }
  }
});
