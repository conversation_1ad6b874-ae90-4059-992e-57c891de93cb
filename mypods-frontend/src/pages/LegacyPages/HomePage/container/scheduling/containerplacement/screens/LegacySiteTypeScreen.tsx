import { Grid, useMediaQuery } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../../../../../locales/TranslationKeys';
import { useLegacyContainerPlacementContext } from '../context/LegacyContainerPlacementContext';
import { ContainerPlacementSiteType } from '../../../../../../../domain/OrderEntities';
import { theme } from '../../../../../../../PodsTheme';
import { SiteTypeScreens } from '../../../../../../HomePage/container/scheduling/containerplacement/screenmanager/ContainerPlacementModalTypes';
import { getContainerPlacementSiteTypeButton } from '../../../../../../../locales/TranslationConstants';
import { ContainerPlacementHeader } from '../../../../../../HomePage/container/scheduling/containerplacement/ContainerPlacementHeader';
import { StretchableLoadingButton } from '../../../../../../../components/buttons/StretchableLoadingButton';
import { Design } from '../../../../../../../helpers/Design';

const Tx = TranslationKeys.HomePage.ContainerPlacement;

export const LegacySiteTypeScreen = () => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = siteTypeStyles(isMobile);
  const { t: translate } = useTranslation();
  const { setContainerPlacement, manager } = useLegacyContainerPlacementContext();

  const handleButtonClick = (
    siteType: ContainerPlacementSiteType,
    childScreenName: SiteTypeScreens
  ) => {
    setContainerPlacement((prev) => ({
      ...prev,
      siteType
    }));
    manager.selectChoiceWithChild(siteType, childScreenName);
  };

  return (
    <Grid container {...styles.contentContainer}>
      <ContainerPlacementHeader titleKey={Tx.SiteTypeScreen.TITLE} />
      <Grid container {...styles.outerButtonContainer}>
        <Grid container item spacing={2} {...styles.innerButtonContainer}>
          <Grid item xs={6}>
            <StretchableLoadingButton
              isMobile={isMobile}
              label={translate(getContainerPlacementSiteTypeButton('DRIVEWAY'))}
              onClick={() => handleButtonClick('DRIVEWAY', 'DRIVEWAY_TYPE')}
              variant="outlined"
            />
          </Grid>
          <Grid item xs={6}>
            <StretchableLoadingButton
              isMobile={isMobile}
              label={translate(getContainerPlacementSiteTypeButton('STREET'))}
              onClick={() => handleButtonClick('STREET', 'STREET_TYPE')}
              variant="outlined"
            />
          </Grid>
          <Grid item xs={6}>
            <StretchableLoadingButton
              isMobile={isMobile}
              label={translate(getContainerPlacementSiteTypeButton('PARKING_LOT'))}
              onClick={() => handleButtonClick('PARKING_LOT', 'PARKING_LOT_TYPE')}
              variant="outlined"
            />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

const siteTypeStyles = (isMobile: boolean) => ({
  contentContainer: {
    sx: {
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  outerButtonContainer: {
    sx: { justifyContent: 'center' }
  },
  innerButtonContainer: {
    sx: { maxWidth: isMobile ? undefined : '400px', justifyContent: 'center' }
  }
});
