import React, { FC, ReactNode, useMemo, useState } from 'react';
import {
  LegacyContainerPlacementContext,
  LegacyContainerPlacementContextState,
  initialContainerPlacement
} from './LegacyContainerPlacementContext';
import { ContainerPlacement } from '../../../../../../../domain/OrderEntities';
import { useContainerPlacementScreenManager } from '../../../../../../HomePage/container/scheduling/containerplacement/screenmanager/useContainerPlacementScreenManager';
import { legacyContainerPlacementFirstScreen } from '../screenmanager/LegacyContainerPlacementScreenConfig';

export type LegacyContainerPlacementProviderProps = {
  existingPlacement?: ContainerPlacement;
  handleFinish: (containerPlacement: ContainerPlacement) => void;
  children: ReactNode;
};

export const LegacyContainerPlacementProvider: FC<LegacyContainerPlacementProviderProps> = ({
  existingPlacement,
  children,
  handleFinish
}) => {
  const manager = useContainerPlacementScreenManager(
    legacyContainerPlacementFirstScreen(),
    existingPlacement
  );
  const [containerPlacement, setContainerPlacement] = useState<ContainerPlacement>(
    existingPlacement ?? initialContainerPlacement
  );

  const value: LegacyContainerPlacementContextState = useMemo(
    () => ({
      manager,
      containerPlacement,
      setContainerPlacement,
      handleFinish
    }),
    [manager, containerPlacement, setContainerPlacement, handleFinish]
  );

  return (
    <LegacyContainerPlacementContext.Provider value={value}>
      {children}
    </LegacyContainerPlacementContext.Provider>
  );
};
