import { createContext, Dispatch, SetStateAction, useContext } from 'react';
import { IContainerPlacementScreenManager } from '../../../../../../HomePage/container/scheduling/containerplacement/screenmanager/useContainerPlacementScreenManager';
import { ContainerPlacement } from '../../../../../../../domain/OrderEntities';
import { ContainerPlacementScreenName } from '../../../../../../HomePage/container/scheduling/containerplacement/screenmanager/ContainerPlacementModalTypes';

export type LegacyContainerPlacementContextState = {
  manager: IContainerPlacementScreenManager;
  containerPlacement: ContainerPlacement;
  setContainerPlacement: Dispatch<SetStateAction<ContainerPlacement>>;
  handleFinish: (containerPlacement: ContainerPlacement) => void;
};

export const initialContainerPlacement: ContainerPlacement = {
  isPavedSurface: false,
  siteType: 'DRIVEWAY',
  placement: 'DRIVEWAY_STRAIGHT_CLOSE_REAR',
  driverNotes: ''
};

const initialState: LegacyContainerPlacementContextState = {
  manager: {
    currentScreen: {
      name: 'PAVED_SURFACE',
      component: undefined,
      children: []
    },
    selectChoice(_choice: any): void {
      throw new Error('Function not implemented.');
    },
    selectChoiceWithChild(_choice: any, _childScreen: ContainerPlacementScreenName): void {
      throw new Error('Function not implemented.');
    },
    hasNextStep(): boolean {
      throw new Error('Function not implemented.');
    },
    nextStep(): void {
      throw new Error('Function not implemented.');
    },
    hasPreviousStep(): boolean {
      throw new Error('Function not implemented.');
    },
    previousStep(): void {
      throw new Error('Function not implemented.');
    },
    goto(_: ContainerPlacementScreenName): void {
      throw new Error('Function not implemented.');
    },
    gotoSiteTypeSelectedChild(): void {
      throw new Error('Function not implemented.');
    },
    completedPercentage(): number {
      throw new Error('Function not implemented.');
    }
  },
  containerPlacement: initialContainerPlacement,
  setContainerPlacement: (_) => {},
  handleFinish(_: ContainerPlacement): void {}
};

export const LegacyContainerPlacementContext =
  createContext<LegacyContainerPlacementContextState>(initialState);

export function useLegacyContainerPlacementContext() {
  return useContext(LegacyContainerPlacementContext);
}
