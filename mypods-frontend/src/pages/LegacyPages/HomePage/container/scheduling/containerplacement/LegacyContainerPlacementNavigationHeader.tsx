import { Grid, Icon<PERSON>utton, LinearProgress, useMediaQuery, Box } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import React from 'react';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import { theme } from '../../../../../../PodsTheme';
import { useLegacyContainerPlacementContext } from './context/LegacyContainerPlacementContext';
import { Design } from '../../../../../../helpers/Design';

interface Props {
  handleOnClosed: () => void;
}

export const LegacyContainerPlacementNavigationHeader = ({ handleOnClosed }: Props) => {
  const { manager } = useLegacyContainerPlacementContext();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = headerNavStyles(isMobile);

  return (
    <Grid item container {...styles.header}>
      <Grid item container>
        <Grid
          item
          container
          xs={8}
          sx={{ gap: isMobile ? Design.Primitives.Spacing.lg : Design.Primitives.Spacing.md }}>
          {manager.hasPreviousStep() && (
            <Grid item xs={1}>
              <IconButton
                {...styles.iconButton}
                onClick={() => manager.previousStep()}
                aria-label="previous">
                <ChevronLeft {...styles.icon} />
              </IconButton>
            </Grid>
          )}
          {manager.hasNextStep() && (
            <Grid item xs={1}>
              <IconButton
                {...styles.iconButton}
                onClick={() => manager.nextStep()}
                aria-label="next">
                <ChevronRight {...styles.icon} />
              </IconButton>
            </Grid>
          )}
        </Grid>
        <Grid container item xs={4} {...styles.closeContainer}>
          <Grid item>
            <IconButton {...styles.iconButton} onClick={handleOnClosed} aria-label="close">
              <CloseIcon {...styles.icon} />
            </IconButton>
          </Grid>
        </Grid>
      </Grid>

      <Grid item container>
        <Box sx={{ width: '100%', paddingTop: '1em' }}>
          <LinearProgress
            variant="determinate"
            color="secondary"
            value={manager.completedPercentage()}
          />
        </Box>
      </Grid>
    </Grid>
  );
};

const headerNavStyles = (isMobile: boolean) => ({
  header: {
    sx: {
      paddingY: Design.Primitives.Spacing.md,
      paddingX: isMobile ? Design.Primitives.Spacing.xs : Design.Primitives.Spacing.lgPlus,
      backgroundColor: Design.Alias.Color.neutral200
    }
  },
  closeContainer: {
    sx: {
      justifyContent: 'flex-end'
    }
  },
  iconButton: {
    sx: {
      backgroundColor: Design.Alias.Color.neutral700,
      ':hover': {
        backgroundColor: Design.Alias.Color.neutral700
      }
    }
  },
  icon: {
    sx: {
      fill: Design.Alias.Color.neutral100
    }
  }
});
