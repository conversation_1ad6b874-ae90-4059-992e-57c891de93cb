import React from 'react';
import {
  nodeBuilder,
  screenConfigBuilder
} from '../../../../../../HomePage/container/scheduling/containerplacement/screenmanager/screenConfigBuilder';
import {
  SiteTypeSelectionFinalProps,
  SiteTypeSelectionNonFinalProps
} from '../../../../../../HomePage/container/scheduling/containerplacement/screens/SiteTypeSelectionScreens';
import { TranslationKeys } from '../../../../../../../locales/TranslationKeys';
import { LegacyReviewScreen } from '../screens/LegacyReviewScreen';
import { LegacyPavedSurfaceScreen } from '../screens/LegacyPavedSurfaceScreen';
import { LegacyPlacementTipsScreen } from '../screens/LegacyPlacementTips';
import { LegacySiteTypeScreen } from '../screens/LegacySiteTypeScreen';
import {
  LegacySiteTypeFinal,
  LegacySiteTypeNonFinal
} from '../screens/LegacySiteTypeSelectionScreens';
import { LegacyDrivewayNotesScreen } from '../screens/LegacyDriverNotesScreen';

const Tx = TranslationKeys.HomePage.ContainerPlacement.SiteTypeScreen.Prompts;
// prettier-ignore
export const legacyContainerPlacementFirstScreen = () =>
  screenConfigBuilder(nodeBuilder('PAVED_SURFACE', <LegacyPavedSurfaceScreen />))
     
      .nextNode(nodeBuilder('PLACEMENT_TIPS',<LegacyPlacementTipsScreen/>))
      .nextNode(nodeBuilder('SITE_TYPE', <LegacySiteTypeScreen />)
        .addChild(
          nodeBuilder('DRIVEWAY_TYPE', <LegacySiteTypeNonFinal {...drivewayProps} />)
            .addChild(nodeBuilder('DRIVEWAY_STRAIGHT', <LegacySiteTypeNonFinal  {...drivewayStraightProps} />)
              .addChild(nodeBuilder('DRIVEWAY_STRAIGHT_CLOSE',
                <LegacySiteTypeFinal {...drivewayStraightCloseProps} />))
              .addChild(nodeBuilder('DRIVEWAY_STRAIGHT_MIDDLE',
                <LegacySiteTypeFinal {...drivewayStraightMiddleProps} />))
              .addChild(nodeBuilder('DRIVEWAY_STRAIGHT_FAR',
                <LegacySiteTypeFinal {...drivewayStraightFarProps} />)))
            .addChild(nodeBuilder('DRIVEWAY_CIRCULAR', <LegacySiteTypeNonFinal {...drivewayCircularProps} />)
              .addChild(nodeBuilder('DRIVE_CIRCULAR_CLOSE',
                <LegacySiteTypeFinal {...drivewayCircularCloseProps} />))
              .addChild(nodeBuilder('DRIVE_CIRCULAR_FAR',
                <LegacySiteTypeFinal {...drivewayCircularFarProps} />)))
            .addChild(nodeBuilder('DRIVEWAY_L_SHAPED', <LegacySiteTypeNonFinal {...drivewayLShapedProps} />)
              .addChild(nodeBuilder('DRIVE_L_SHAPED_CLOSE',
                <LegacySiteTypeFinal {...drivewayLShapedCloseProps} />))
              .addChild(nodeBuilder('DRIVE_L_SHAPED_MIDDLE',
                <LegacySiteTypeFinal {...drivewayLShapedMiddleProps} />))
              .addChild(nodeBuilder('DRIVE_L_SHAPED_FAR',
                <LegacySiteTypeFinal {...drivewayLShapedFarProps} />))))
        .addChild(nodeBuilder('STREET_TYPE', <LegacySiteTypeFinal {...streetAlleywayProps} />))
        .addChild(nodeBuilder('PARKING_LOT_TYPE', <LegacySiteTypeNonFinal {...parkingLotProps} />)
          .addChild(nodeBuilder('PARKING_LOT_FRONT',
            <LegacySiteTypeFinal {...parkingLotFrontProps} />))
          .addChild(nodeBuilder('PARKING_LOT_RIGHT',
            <LegacySiteTypeFinal {...parkingLotRightProps} />))
          .addChild(nodeBuilder('PARKING_LOT_LEFT',
            <LegacySiteTypeFinal {...parkingLotLeftProps} />))
          .addChild(nodeBuilder('PARKING_LOT_BACK',
            <LegacySiteTypeFinal {...parkingLotBackProps} />)))
    )
    .nextNode(nodeBuilder('DRIVER_NOTES', <LegacyDrivewayNotesScreen />))
    .nextNode(nodeBuilder('REVIEW', <LegacyReviewScreen />))
    .build();

// region Driveway
const drivewayProps: SiteTypeSelectionNonFinalProps = {
  siteTypes: ['DRIVEWAY_STRAIGHT', 'DRIVEWAY_CIRCULAR', 'DRIVEWAY_L_SHAPED'],
  prompt: Tx.Driveway.SITE_TYPE
};

// region Driveway Straight
const drivewayStraightProps: SiteTypeSelectionNonFinalProps = {
  siteTypes: ['DRIVEWAY_STRAIGHT_CLOSE', 'DRIVEWAY_STRAIGHT_MIDDLE', 'DRIVEWAY_STRAIGHT_FAR'],
  prompt: Tx.Driveway.CONTAINER_LOCATION
};
const drivewayStraightCloseProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_STRAIGHT_CLOSE_CAB', 'DRIVEWAY_STRAIGHT_CLOSE_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
const drivewayStraightMiddleProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_STRAIGHT_MIDDLE_CAB', 'DRIVEWAY_STRAIGHT_MIDDLE_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
const drivewayStraightFarProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_STRAIGHT_FAR_CAB', 'DRIVEWAY_STRAIGHT_FAR_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
// endregion

// region Driveway Circular
const drivewayCircularProps: SiteTypeSelectionNonFinalProps = {
  siteTypes: ['DRIVE_CIRCULAR_CLOSE', 'DRIVE_CIRCULAR_FAR'],
  prompt: Tx.Driveway.CONTAINER_LOCATION
};
const drivewayCircularCloseProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_CIRCULAR_CLOSE_CAB', 'DRIVEWAY_CIRCULAR_CLOSE_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
const drivewayCircularFarProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_CIRCULAR_FAR_CAB', 'DRIVEWAY_CIRCULAR_FAR_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
// endregion

// region Driveway L Shaped
const drivewayLShapedProps: SiteTypeSelectionNonFinalProps = {
  siteTypes: ['DRIVE_L_SHAPED_CLOSE', 'DRIVE_L_SHAPED_MIDDLE', 'DRIVE_L_SHAPED_FAR'],
  prompt: Tx.Driveway.CONTAINER_LOCATION
};
const drivewayLShapedCloseProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_L_SHAPED_CLOSE_CAB', 'DRIVEWAY_L_SHAPED_CLOSE_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
const drivewayLShapedMiddleProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_L_SHAPED_MIDDLE_CAB', 'DRIVEWAY_L_SHAPED_MIDDLE_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
const drivewayLShapedFarProps: SiteTypeSelectionFinalProps = {
  selections: ['DRIVEWAY_L_SHAPED_FAR_CAB', 'DRIVEWAY_L_SHAPED_FAR_REAR'],
  prompt: Tx.Driveway.CAB_DIRECTION
};
// endregion
// endregion Driveway

// region Street Alleyway
const streetAlleywayProps: SiteTypeSelectionFinalProps = {
  selections: ['STREET_LEFT_CAB', 'STREET_FRONT_CAB', 'STREET_RIGHT_CAB', 'STREET_BACK_CAB'],
  prompt: Tx.StreetAlleyway.SITE_TYPE
};
// endregion

// region Parking Lot
const parkingLotProps: SiteTypeSelectionNonFinalProps = {
  siteTypes: ['PARKING_LOT_RIGHT', 'PARKING_LOT_FRONT', 'PARKING_LOT_LEFT', 'PARKING_LOT_BACK'],
  prompt: Tx.ParkingLot.SITE_TYPE
};

const parkingLotFrontProps: SiteTypeSelectionFinalProps = {
  selections: [
    'PARKING_LOT_FRONT_01',
    'PARKING_LOT_FRONT_02',
    'PARKING_LOT_FRONT_03',
    'PARKING_LOT_FRONT_04'
  ],
  prompt: Tx.Driveway.CAB_DIRECTION
};

const parkingLotBackProps: SiteTypeSelectionFinalProps = {
  selections: [
    'PARKING_LOT_BACK_01',
    'PARKING_LOT_BACK_02',
    'PARKING_LOT_BACK_03',
    'PARKING_LOT_BACK_04'
  ],
  prompt: Tx.Driveway.CAB_DIRECTION
};

const parkingLotLeftProps: SiteTypeSelectionFinalProps = {
  selections: [
    'PARKING_LOT_LEFT_01',
    'PARKING_LOT_LEFT_02',
    'PARKING_LOT_LEFT_03',
    'PARKING_LOT_LEFT_04'
  ],
  prompt: Tx.Driveway.CAB_DIRECTION
};

const parkingLotRightProps: SiteTypeSelectionFinalProps = {
  selections: [
    'PARKING_LOT_RIGHT_01',
    'PARKING_LOT_RIGHT_02',
    'PARKING_LOT_RIGHT_03',
    'PARKING_LOT_RIGHT_04'
  ],
  prompt: Tx.Driveway.CAB_DIRECTION
};
// endregion
