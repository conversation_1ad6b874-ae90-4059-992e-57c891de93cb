import React from 'react';
import { ContainerProvider } from '../../../../context/ContainerContext';
import { SingleOrderProvider } from '../../../../context/SingleOrderContext';
import { ContainerSkeleton } from '../../../../components/Loading/ContainerSkeleton';
import { LegacyContainerTile } from './LegacyContainerTile';
import useLegacyOrdersContext from '../../../../context/legacy/LegacyOrdersContext';

export const LegacyContainerTileWrapper = () => {
  const { orders, showLoader } = useLegacyOrdersContext();

  if (showLoader) {
    return <ContainerSkeleton />;
  }

  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {orders &&
        orders?.map((order) => (
          <SingleOrderProvider state={{ order }} key={order.orderId}>
            <React.Fragment key={order.orderId}>
              {order.containers?.map((container, index) => (
                <ContainerProvider
                  key={container.containerId ?? `container-context${index}}`}
                  state={{ container, order }}>
                  <LegacyContainerTile />
                </ContainerProvider>
              ))}
            </React.Fragment>
          </SingleOrderProvider>
        ))}
    </>
  );
};
