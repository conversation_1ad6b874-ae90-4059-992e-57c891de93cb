import { screen } from '@testing-library/react';
import { renderWithQueryProvider } from '../../../../testUtils/RenderHelpers';
import { LegacyHomePage } from '../LegacyHomePage';
import {
  createContainer,
  createCustomer,
  createMoveLeg,
  createOrder,
  createPaymentMethod
} from '../../../../testUtils/MyPodsFactories';
import React from 'react';
import { vi } from 'vitest';
import { Order } from '../../../../domain/OrderEntities';
import { mockLegacyGetCustomer, mockLegacyGetPaymentMethods } from '../../../../../setupTests';
import { NotificationProvider } from '../../../../components/notifications/NotificationContext';
import { LegacyOrdersContext } from '../../../../context/legacy/LegacyOrdersContext';

describe('HomePage', () => {
  const eightFootContainer = createContainer({
    containerSize: '8',
    containerId: '321654987',
    moveLegs: [createMoveLeg({ moveLegId: '1' })]
  });
  const oneOrder = [createOrder({ containers: [eightFootContainer] })];

  function renderPageWithOrders(orders: Order[] = oneOrder) {
    mockLegacyGetPaymentMethods.mockResolvedValue([createPaymentMethod()]);
    mockLegacyGetCustomer.mockResolvedValue(createCustomer());

    renderWithQueryProvider(
      <LegacyOrdersContext.Provider
        value={{
          orders: orders,
          refetch: vi.fn(),
          refetchOnFailure: vi.fn(),
          showLoader: false
        }}>
        <NotificationProvider>{<LegacyHomePage />}</NotificationProvider>
      </LegacyOrdersContext.Provider>
    );
  }

  it('should display multiple containers for an order', async () => {
    const sixteenFootContainer = createContainer({
      containerSize: '16',
      containerId: '789456123',
      moveLegs: []
    });
    const orders = [createOrder({ containers: [eightFootContainer, sixteenFootContainer] })];

    renderPageWithOrders(orders);

    expect(await screen.findByTestId(eightFootContainer.containerId)).toBeInTheDocument();
    expect(await screen.findByTestId(sixteenFootContainer.containerId)).toBeInTheDocument();
  });
});
