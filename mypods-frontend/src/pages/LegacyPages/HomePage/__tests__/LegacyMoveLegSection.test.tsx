import { cleanup, render, screen, within } from '@testing-library/react';
import { vi } from 'vitest';
import React from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import userEvent from '@testing-library/user-event';
import { addDays, subDays } from 'date-fns';
import Button from '@mui/material/Button';
import {
  Container,
  ContainerPlacement,
  MoveLeg,
  MoveLegTypeEnum,
  Order
} from '../../../../domain/OrderEntities';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { initialContainerPlacement } from '../../../HomePage/container/scheduling/containerplacement/context/ContainerPlacementContext';
import { ContainerPlacementModalProps } from '../../../HomePage/container/scheduling/containerplacement/ContainerPlacementModal';
import { createContainer, createMoveLeg, createOrder } from '../../../../testUtils/MyPodsFactories';
import { moveLegActions, moveLegViews } from '../../../HomePage/__tests__/MoveLegViews';
import { testQueryClient } from '../../../../testUtils/RenderHelpers';
import { NotificationProvider } from '../../../../components/notifications/NotificationContext';
import { ContainerProvider } from '../../../../context/ContainerContext';
import { LegacyMoveLegProvider } from '../container/moveleg/LegacyMoveLegContext';
import { LegacyMoveLegSection } from '../container/moveleg/current/LegacyMoveLegSection';
import { SingleOrderProvider } from '../../../../context/SingleOrderContext';
import { formatETA, formatToLocale } from '../../../../helpers/dateHelpers';
import { getDatePickerLabelKey } from '../../../../locales/TranslationConstants';
import { mockLegacyAcceptInitialDeliveryPlacement } from '../../../../../setupTests';
import { useLegacyContainerPlacementContext } from '../container/scheduling/containerplacement/context/LegacyContainerPlacementContext';

const Tx = TranslationKeys.HomePage.MoveLegs;
const TxDateLabels = TranslationKeys.HomePage.MoveLegs.Scheduling.DateLabels;

let testContainerPlacement: ContainerPlacement = initialContainerPlacement;
vi.mock(
  '../../../HomePage/container/scheduling/containerplacement/ContainerPlacementModal',
  () => ({
    ContainerPlacementModal: (_: ContainerPlacementModalProps) => {
      const { handleFinish } = useLegacyContainerPlacementContext();
      return (
        <Button
          onClick={() => {
            handleFinish(testContainerPlacement);
          }}>
          {TranslationKeys.HomePage.ContainerPlacement.ReviewScreen.FINISH_BUTTON}
        </Button>
      );
    }
  })
);

describe('LegacyMoveLegSection', () => {
  const actions = moveLegActions(userEvent.setup({ delay: null }));
  const todayJsDate = new Date('2024-01-01');
  const todayDate = new Date('2024-01-01')!;
  const futureDate = addDays(todayDate, 7);
  const pastDate = subDays(todayDate, 7);
  const baseOrder = createOrder();

  const renderMoveLeg = (
    moveLeg: MoveLeg,
    container: Container = createContainer({ moveLegs: [moveLeg] }),
    order: Order = { ...baseOrder, containers: [container] }
  ) => {
    return render(
      <QueryClientProvider client={testQueryClient()}>
        <NotificationProvider>
          <SingleOrderProvider state={{ order }}>
            <ContainerProvider state={{ container, order }}>
              <LegacyMoveLegProvider
                moveLeg={moveLeg}
                lastMoveLegId={''}
                isLastRenderedMoveLeg={false}>
                <LegacyMoveLegSection />
              </LegacyMoveLegProvider>
            </ContainerProvider>
          </SingleOrderProvider>
        </NotificationProvider>
      </QueryClientProvider>
    );
  };

  beforeEach(() => {
    vi.useFakeTimers({ now: todayJsDate });
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
    cleanup();
  });

  describe('General Behavior', () => {
    it('should display schedule button if move leg is unscheduled', () => {
      const unscheduledMoveLeg = createMoveLeg({
        scheduledDate: undefined,
        scheduledStatus: 'UNSCHEDULED'
      });

      renderMoveLeg(unscheduledMoveLeg);
      expect(moveLegViews.scheduleButton()).toBeInTheDocument();
    });

    it('should display edit button if move leg is scheduled', () => {
      let date = new Date();
      date.setDate(date.getDate() + 2);
      const moveLeg = createMoveLeg({ scheduledDate: date });

      renderMoveLeg(moveLeg);

      expect(moveLegViews.editButton()).toBeInTheDocument();
    });

    it('should display transit leg component, when given type is a transit leg: ', () => {
      const inTransitLeg = createMoveLeg({ isTransitLeg: true });

      renderMoveLeg(inTransitLeg);
      expect(screen.getByText(Tx.IN_TRANSIT_DESCRIPTION)).toBeInTheDocument();
    });

    it('should display call to schedule component when transit leg is warehouse to warehouse and unscheduled', () => {
      const unscheduledWTWLeg = createMoveLeg({
        moveLegType: 'WAREHOUSE_TO_WAREHOUSE',
        scheduledDate: undefined,
        isSchedulableOnline: false
      });

      renderMoveLeg(unscheduledWTWLeg);

      expect(screen.getByText(Tx.Title.CALL_TO_SCHEDULE)).toBeInTheDocument();
      expect(screen.getByText(Tx.CALL_TO_SCHEDULE_DESCRIPTION)).toBeInTheDocument();
    });

    it('should display arrival date and move date for a visit container leg', () => {
      const storageMoveLeg = createMoveLeg({
        scheduledDate: pastDate,
        moveLegType: 'VISIT_CONTAINER',
        arrivalDate: todayDate,
        moveDate: futureDate
      });

      renderMoveLeg(storageMoveLeg);

      expect(screen.getByText(TxDateLabels.LABEL_ARRIVAL)).toBeInTheDocument();
      expect(screen.getByText(formatToLocale(storageMoveLeg.arrivalDate!))).toBeInTheDocument();
      expect(screen.getByText(TxDateLabels.LABEL_MOVE)).toBeInTheDocument();
      expect(screen.getByText(formatToLocale(storageMoveLeg.moveDate!))).toBeInTheDocument();
    });

    it('should display container at warehouse title', () => {
      const storageMoveLeg = createMoveLeg({
        scheduledDate: pastDate,
        moveLegType: 'VISIT_CONTAINER',
        arrivalDate: todayDate,
        moveDate: futureDate
      });

      renderMoveLeg(storageMoveLeg);

      expect(
        within(screen.getByTestId('move-leg-title-container')).getByText(
          TranslationKeys.HomePage.MoveLegs.Title.CONTAINER_AT_WAREHOUSE
        )
      ).toBeInTheDocument();
    });

    it('should display arrival date and move date for a visit container move leg', () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        arrivalDate: pastDate,
        moveDate: futureDate
      });

      renderMoveLeg(visitContainerMoveLeg);

      expect(screen.getByText(TxDateLabels.LABEL_ARRIVAL)).toBeInTheDocument();
      expect(
        screen.getByText(formatToLocale(visitContainerMoveLeg.arrivalDate!))
      ).toBeInTheDocument();
      expect(screen.getByText(TxDateLabels.LABEL_MOVE)).toBeInTheDocument();
      expect(screen.getByText(formatToLocale(visitContainerMoveLeg.moveDate!))).toBeInTheDocument();
    });

    it('should not display schedule container visit section by default', () => {
      renderMoveLeg(createMoveLeg());

      expect(screen.queryByText('Scheduled Container Visit')).not.toBeInTheDocument();
    });

    it('should display scheduled container visit section for visit container move leg', () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: futureDate,
        eta: '9:00 AM - 5:00 PM'
      });

      renderMoveLeg(visitContainerMoveLeg);

      expect(screen.getByText(Tx.Title.VISIT_CONTAINER)).toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.DateLabels.LABEL_VISIT)).toBeInTheDocument();
      expect(
        screen.getByText(formatToLocale(visitContainerMoveLeg.scheduledDate!))
      ).toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.DateLabels.LABEL_VISIT_WINDOW)).toBeInTheDocument();
      expect(screen.getByText(formatETA(visitContainerMoveLeg.eta!))).toBeInTheDocument();
      expect(moveLegViews.containerVisitEditButton()).toBeInTheDocument();
      expect(moveLegViews.containerVisitCancelButton()).toBeInTheDocument();
    });

    it('should display edit button for container visit', () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: futureDate,
        eta: '9:00 AM - 5:00 PM'
      });

      renderMoveLeg(visitContainerMoveLeg);

      expect(moveLegViews.containerVisitEditButton()).toBeInTheDocument();
    });

    it('should display no warehouse hours found if no eta is found on the move leg', () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: futureDate,
        eta: undefined
      });

      renderMoveLeg(visitContainerMoveLeg);

      expect(screen.getByText(Tx.Title.VISIT_CONTAINER)).toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.DateLabels.LABEL_VISIT)).toBeInTheDocument();
      expect(
        screen.getByText(formatToLocale(visitContainerMoveLeg.scheduledDate!))
      ).toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.DateLabels.LABEL_VISIT_WINDOW)).toBeInTheDocument();
      expect(screen.getByText(Tx.Scheduling.NO_WAREHOUSE_HOURS_FOUND)).toBeInTheDocument();
      expect(moveLegViews.containerVisitEditButton()).toBeInTheDocument();
      expect(moveLegViews.containerVisitCancelButton()).toBeInTheDocument();
    });

    it('should allow for rescheduling when edit is clicked for a visit container move leg', async () => {
      const visitContainerMoveLeg = createMoveLeg({
        scheduledDate: todayDate,
        moveLegType: 'VISIT_CONTAINER',
        containerVisitDate: futureDate,
        eta: '9:00 AM - 5:00 PM'
      });

      renderMoveLeg(visitContainerMoveLeg);

      await actions.clickContainerVisitEditButton();

      expect(screen.queryByText(Tx.Title.VISIT_CONTAINER)).not.toBeInTheDocument();
      expect(moveLegViews.saveButton()).toBeInTheDocument();
      expect(
        screen.getByRole('textbox', {
          name: getDatePickerLabelKey(visitContainerMoveLeg)
        })
      ).toBeInTheDocument();
    });

    it('should display call for special assistance component if the move leg is moving internationally', () => {
      const internationalMoveLeg = createMoveLeg({
        isCrossBorder: true
      });

      renderMoveLeg(internationalMoveLeg);

      expect(screen.getByText(Tx.CALL_FOR_SPECIAL_ASSISTANCE)).toBeInTheDocument();
    });

    it('should not display call to schedule component if the move leg is moving internationally', () => {
      const internationalMoveLeg = createMoveLeg({
        isCrossBorder: true,
        moveLegType: MoveLegTypeEnum.WAREHOUSE_TO_WAREHOUSE,
        scheduledDate: undefined
      });

      renderMoveLeg(internationalMoveLeg);

      expect(screen.queryByText(Tx.CALL_TO_SCHEDULE_DESCRIPTION)).not.toBeInTheDocument();
    });

    it('should not display edit button and schedule button if the move leg is moving internationally', () => {
      const internationalMoveLeg = createMoveLeg({
        isCrossBorder: true,
        scheduledDate: undefined
      });

      renderMoveLeg(internationalMoveLeg);

      expect(moveLegViews.editButton()).not.toBeInTheDocument();
      expect(moveLegViews.scheduleButton()).not.toBeInTheDocument();
    });

    it('should display call for special assistance component if the move leg for Hawaii', () => {
      const hawaiiMoveLeg = createMoveLeg({
        isHawaii: true
      });

      renderMoveLeg(hawaiiMoveLeg);

      expect(screen.getByText(Tx.CALL_FOR_SPECIAL_ASSISTANCE)).toBeInTheDocument();
    });

    it('should display call to schedule component if the move leg for Hawaii', () => {
      const hawaiiMoveLeg = createMoveLeg({
        isHawaii: true,
        moveLegType: MoveLegTypeEnum.WAREHOUSE_TO_WAREHOUSE,
        scheduledDate: undefined
      });

      renderMoveLeg(hawaiiMoveLeg);

      expect(screen.queryByText(Tx.CALL_TO_SCHEDULE_DESCRIPTION)).not.toBeInTheDocument();
    });

    it('should not display edit button and schedule button if the move leg for Hawaii', () => {
      const hawaiiMoveLeg = createMoveLeg({
        isHawaii: true,
        scheduledDate: undefined
      });

      renderMoveLeg(hawaiiMoveLeg);

      expect(moveLegViews.editButton()).not.toBeInTheDocument();
      expect(moveLegViews.scheduleButton()).not.toBeInTheDocument();
    });

    it('should display the correct call text, if the move leg is City Service', () => {
      const cityServiceMoveLeg = createMoveLeg({
        isCityService: true,
        moveLegType: MoveLegTypeEnum.WAREHOUSE_TO_WAREHOUSE,
        scheduledDate: undefined
      });

      renderMoveLeg(cityServiceMoveLeg);

      expect(screen.queryByText(Tx.CALL_TO_SCHEDULE_DESCRIPTION)).not.toBeInTheDocument();
      expect(screen.queryByText(Tx.CALL_FOR_SPECIAL_ASSISTANCE)).toBeInTheDocument();
    });

    it('should not display edit button and schedule button if the move involves City Service', () => {
      const cityServiceMoveLeg = createMoveLeg({
        isCityService: true,
        scheduledDate: undefined
      });

      renderMoveLeg(cityServiceMoveLeg);

      expect(moveLegViews.editButton()).not.toBeInTheDocument();
      expect(moveLegViews.scheduleButton()).not.toBeInTheDocument();
    });

    it('should not display call for special assistance component if the move leg is transit', () => {
      const inTransitHawaiiMoveLeg = createMoveLeg({
        isHawaii: true,
        isTransitLeg: true
      });

      renderMoveLeg(inTransitHawaiiMoveLeg);

      expect(screen.queryByText(Tx.CALL_FOR_SPECIAL_ASSISTANCE)).not.toBeInTheDocument();
    });

    it('when SELF_FINAL_PICKUP move leg is scheduled, then pickup window is displayed', () => {
      const selfFinalPickupMoveLeg = createMoveLeg({
        moveLegType: 'SELF_FINAL_PICKUP',
        scheduledDate: futureDate,
        eta: '07:30 AM - 03:00 PM'
      });

      renderMoveLeg(selfFinalPickupMoveLeg);

      expect(screen.getByText(Tx.Scheduling.DateLabels.LABEL_PICKUP_WINDOW)).toBeInTheDocument();
      expect(screen.getByText('7:30AM to 3PM')).toBeInTheDocument();
    });

    it('when SELF_FINAL_PICKUP move leg is unscheduled, then pickup window is absent', () => {
      const selfFinalPickupMoveLeg = createMoveLeg({
        moveLegType: 'SELF_FINAL_PICKUP',
        scheduledDate: undefined
      });

      renderMoveLeg(selfFinalPickupMoveLeg);

      expect(
        screen.queryByText(Tx.Scheduling.DateLabels.LABEL_PICKUP_WINDOW)
      ).not.toBeInTheDocument();
    });
  });

  describe('Review Initial Container Placement', () => {
    const initialDeliveryMoveLeg = createMoveLeg({
      moveLegType: 'INITIAL_DELIVERY'
    });
    const orderId = 'orderId1';

    const renderWithInitialDelivery = (
      moveLeg: MoveLeg,
      initialDeliveryPlacementIsReviewed: boolean
    ) => {
      const container = createContainer({ moveLegs: [moveLeg] });
      const orderNeedingReview = createOrder({
        orderId,
        initialDeliveryPlacementIsReviewed,
        containers: [container]
      });
      renderMoveLeg(moveLeg, container, orderNeedingReview);
    };

    it('should display alert for initial delivery when customer has not reviewed container placement for order', () => {
      renderWithInitialDelivery(initialDeliveryMoveLeg, false);

      expect(screen.getByText(Tx.InitialDeliveryReviewAlert.UPPER_TITLE)).toBeInTheDocument();
      expect(screen.getByText(Tx.InitialDeliveryReviewAlert.TITLE)).toBeInTheDocument();
      expect(screen.getByText(Tx.InitialDeliveryReviewAlert.SUBTITLE)).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: TranslationKeys.CommonComponents.REVIEW_BUTTON })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: TranslationKeys.CommonComponents.ACCEPT_BUTTON })
      ).toBeInTheDocument();
    });

    it('should NOT display alert for initial delivery when customer has reviewed container placement for order', () => {
      renderWithInitialDelivery(initialDeliveryMoveLeg, true);

      expect(screen.queryByText(Tx.InitialDeliveryReviewAlert.TITLE)).not.toBeInTheDocument();
      expect(screen.queryByText(Tx.InitialDeliveryReviewAlert.SUBTITLE)).not.toBeInTheDocument();
    });

    it('should NOT display alert for non initial delivery', () => {
      const finalPickupMoveLeg = createMoveLeg({
        moveLegType: 'FINAL_PICKUP'
      });
      renderWithInitialDelivery(finalPickupMoveLeg, false);

      expect(screen.queryByText(Tx.InitialDeliveryReviewAlert.TITLE)).not.toBeInTheDocument();
      expect(screen.queryByText(Tx.InitialDeliveryReviewAlert.SUBTITLE)).not.toBeInTheDocument();
    });

    it('accepts container placement when accept button is clicked', async () => {
      renderWithInitialDelivery(initialDeliveryMoveLeg, false);

      await actions.clickAcceptDeliveryPlacement();

      expect(mockLegacyAcceptInitialDeliveryPlacement).toBeCalledWith(orderId);
    });

    it('displays the container placement modal when review is clicked', async () => {
      renderWithInitialDelivery(initialDeliveryMoveLeg, false);
      await actions.clickReviewDeliveryPlacement();

      expect(moveLegViews.containerPlacementFinishButton()).toBeInTheDocument();
    });
  });

  describe('should display move leg icon: ', () => {
    it('empty circle when move leg is scheduled in the past', () => {
      renderMoveLeg(createMoveLeg({ scheduledDate: pastDate }));

      expect(screen.getByTestId('timeline-icon-empty')).toBeInTheDocument();
    });

    it('filled circle when move leg is up next and has been scheduled', () => {
      renderMoveLeg(createMoveLeg({ scheduledDate: futureDate, isUpNext: true }));

      expect(screen.getByTestId('timeline-icon-filled')).toBeInTheDocument();
    });

    it('empty circle when move leg is not up next and has been scheduled', () => {
      renderMoveLeg(createMoveLeg({ scheduledDate: futureDate, isUpNext: false }));

      expect(screen.getByTestId('timeline-icon-empty')).toBeInTheDocument();
    });

    it('faded empty circle when move leg is unscheduled', () => {
      renderMoveLeg(createMoveLeg({ scheduledDate: undefined }));

      expect(screen.getByTestId('timeline-icon-faded')).toBeInTheDocument();
    });
  });
});
