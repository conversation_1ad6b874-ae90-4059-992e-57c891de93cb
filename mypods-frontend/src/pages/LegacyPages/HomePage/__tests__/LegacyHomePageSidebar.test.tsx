import { screen, waitForElementToBeRemoved, within } from '@testing-library/react';
import { LegacyHomePageSidebar } from '../LegacyHomePageSidebar';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import {
  renderWithLegacyProvidersAndState,
  testQueryClient
} from '../../../../testUtils/RenderHelpers';
import {
  mockLegacyGetCustomer,
  mockLegacyGetCustomerOrders,
  mockLegacyGetPaymentMethods
} from '../../../../../setupTests';
import {
  createCustomer,
  createOrder,
  createPaymentMethod
} from '../../../../testUtils/MyPodsFactories';
import { QueryClient } from '@tanstack/react-query';
import { LegacyQueryCacheKeys } from '../../../../networkRequests/QueryCacheKeys';
import { Customer } from '../../../../networkRequests/responseEntities/CustomerEntities';

describe('LegacyHomePageSidebar', () => {
  const Tx = TranslationKeys.HomePage.Sidebar;
  let queryClient: QueryClient;
  let customer: Customer;

  const views = {
    resources: () => screen.getByTestId('resources-sidebar-section'),
    servicesWithin: () => within(screen.getByTestId('services-sidebar-section'))
  };

  beforeEach(() => {
    customer = createCustomer();
    queryClient = testQueryClient();
    queryClient.setQueryData([LegacyQueryCacheKeys.LEGACY_CUSTOMER_CACHE_KEY], customer);
    mockLegacyGetPaymentMethods.mockResolvedValue([createPaymentMethod()]);
    mockLegacyGetCustomer.mockResolvedValue(customer);
    mockLegacyGetCustomerOrders.mockResolvedValue([createOrder()]);
  });

  const renderSidebar = async () => {
    const result = renderWithLegacyProvidersAndState(<LegacyHomePageSidebar />, {
      initialCustomer: createCustomer()
    });
    await waitForElementToBeRemoved(() => result.queryByText('Loading customer information...'));
    return result;
  };

  describe('should display general content', () => {
    it('should render resources links', async () => {
      await renderSidebar();

      expect(screen.getByTestId('resources-sidebar-section')).toBeInTheDocument();
      expect(views.resources()).toHaveTextContent(Tx.Faqs.TEXT);
      expect(views.resources()).toHaveTextContent(Tx.BillingPayment.TEXT);
      expect(views.resources()).toHaveTextContent(Tx.DeliveryChecklist.TEXT);
      expect(views.resources()).toHaveTextContent(Tx.PackingTips.TEXT);
      expect(views.resources()).toHaveTextContent(Tx.PackingDosDonts.TEXT);
    });
    it('should render moving services links', async () => {
      await renderSidebar();

      expect(screen.getByTestId('services-sidebar-section')).toBeInTheDocument();
      expect(views.servicesWithin().getByText(Tx.CarShipping.TEXT)).toBeInTheDocument();
      expect(views.servicesWithin().getByText(Tx.PackingHelp.TEXT)).toBeInTheDocument();
    });
  });
});
