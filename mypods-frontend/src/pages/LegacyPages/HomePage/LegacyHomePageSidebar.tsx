import { Grid, Typography } from '@mui/material';
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import { SidebarCard, SidebarCardProps } from '../../HomePage/SidebarCard';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { LegacyAcornFinancing } from '../BillingPage/components/LegacyAcornFinancing';
import { ROUTES } from '../../../Routes';

export const LegacyHomePageSidebar = () => {
  const { t: translate } = useTranslation();
  const styles = sideBarStyles();
  const Tx = TranslationKeys.HomePage.Sidebar;
  const resourcesList: SidebarCardProps[] = [
    { icon: 'question', text: translate(Tx.Faqs.TEXT), link: translate(Tx.Faqs.URL) },
    {
      icon: 'question',
      text: translate(Tx.BillingPayment.TEXT),
      link: ROUTES.BILLING
    },
    {
      icon: 'question',
      text: translate(Tx.DeliveryChecklist.TEXT),
      link: translate(Tx.DeliveryChecklist.URL)
    },
    { icon: 'question', text: translate(Tx.PackingTips.TEXT), link: translate(Tx.PackingTips.URL) },
    {
      icon: 'question',
      text: translate(Tx.PackingDosDonts.TEXT),
      link: translate(Tx.PackingDosDonts.URL)
    }
  ];
  const servicesList: SidebarCardProps[] = [
    {
      icon: 'car',
      text: translate(Tx.CarShipping.TEXT),
      link: translate(Tx.CarShipping.URL)
    },
    {
      icon: 'dolly',
      text: translate(Tx.PackingHelp.TEXT),
      link: translate(Tx.PackingHelp.URL)
    }
  ];
  return (
    <Grid container item xs={12} sm={4} {...styles.container} data-testid="sidebar-container">
      <Grid container item data-testid="financing-sidebar-section" {...styles.cardContainer}>
        <LegacyAcornFinancing />
      </Grid>
      <Grid container item data-testid="resources-sidebar-section" {...styles.cardContainer}>
        <Typography {...styles.title}>{translate(Tx.RESOURCES_TITLE)}</Typography>
        <Grid container {...styles.cardContainer}>
          {resourcesList.map((card, index) => (
            <Fragment key={index}>
              <SidebarCard icon={card.icon} text={card.text} link={card.link} />
            </Fragment>
          ))}
        </Grid>
      </Grid>
      <Grid container item {...styles.cardContainer} data-testid="services-sidebar-section">
        <Typography {...styles.title}>{translate(Tx.SERVICES_TITLE)}</Typography>
        <Grid container {...styles.cardContainer}>
          {servicesList.map((card, index) => (
            <Fragment key={index}>
              <SidebarCard icon={card.icon} text={card.text} link={card.link} />
            </Fragment>
          ))}
        </Grid>
      </Grid>
    </Grid>
  );
};

const sideBarStyles = () => ({
  container: {
    alignSelf: 'start',
    gap: '26px'
  },
  cardContainer: {
    sx: {
      gap: Design.Primitives.Spacing.xxs
    }
  },
  title: {
    sx: {
      ...Design.Alias.Text.Heading.Desktop.Xs,
      color: Design.Alias.Color.accent900,
      lineHeight: '22px'
    }
  }
});
