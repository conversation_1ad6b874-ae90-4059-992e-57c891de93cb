import { renderWithQueryProvider } from '../../../../testUtils/RenderHelpers';
import { LegacyDocumentsPage } from '../LegacyDocumentsPage';
import { screen } from '@testing-library/react';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { createAPIDocuments, createCustomer } from '../../../../testUtils/MyPodsFactories';
import { mockGetCustomer, mockLegacyGetDocuments } from '../../../../../setupTests';

describe('Document Page', () => {
  it('should render documents page', async () => {
    const documents = createAPIDocuments();
    mockLegacyGetDocuments.mockResolvedValue(documents);
    mockGetCustomer.mockResolvedValue(createCustomer());

    renderWithQueryProvider(<LegacyDocumentsPage />);

    expect(await screen.findByText(TranslationKeys.DocumentsPage.HEADER));
    expect(screen.getByText(TranslationKeys.DocumentsPage.SUBTITLE));
    expect(screen.getByText(`#${documents.documents[0].orderId}`)).toBeInTheDocument();
  });

  it.skip('given customer tries access a doc they do not own, should display an error', () => {});

  it.skip('displays a fallback message when the user has no documents', () => {});
});
