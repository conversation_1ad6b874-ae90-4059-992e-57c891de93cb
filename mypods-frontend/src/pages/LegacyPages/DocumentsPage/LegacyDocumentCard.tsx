import React, { useContext } from 'react';
import { ButtonBase, CircularProgress, Grid, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { saveAs } from 'file-saver';
import { AxiosError } from 'axios';
import { Design } from '../../../helpers/Design';
import { DocumentIcon } from '../../../components/icons/DocumentIcon';
import { CheckmarkIcon } from '../../../components/icons/CheckmarkIcon';
import { IDocument } from '../../../domain/DocumentEntities';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import {
  DOCUMENT_TITLES_RETRIEVED_BY_COMPANY_CODE,
  SIGNABLE_DOCUMENTS
} from '../../../helpers/Documents';
import { DownloadIcon } from '../../../components/icons/DownloadIcon';
import { useGtmEvents } from '../../../config/google/useGtmEvents';
import { useLegacyGetFile } from '../../../networkRequests/legacy/mutations/useLegacyGetFile';
import { useLegacyGetSasUrl } from '../../../networkRequests/legacy/mutations/useLegacyGetSasUrl';
import { useLegacyGetRentalAgreement } from '../../../networkRequests/legacy/mutations/useLegacyGetRentalAgreement';

// -- types --
interface Props {
  document: IDocument;
}

// -- impls --
export const LegacyDocumentCard: React.FC<Props> = ({ document }: Props) => {
  const { setNotification } = useContext(NotificationContext);
  const getFile = useLegacyGetFile();
  const getSasUrl = useLegacyGetSasUrl();
  const getRentalAgreement = useLegacyGetRentalAgreement();
  const { t: translate } = useTranslation();
  const { id: documentId, orderId, title, companyCode } = document;
  const styles = documentCardStyles;
  const gtmEvents = useGtmEvents();
  const isLoading = getFile.isPending || getSasUrl.isPending;

  const onError = (error: unknown) => {
    if (error instanceof AxiosError && error.response?.status) {
      if (error.response?.status === 404) {
        setNotification({
          isError: true,
          message: translate(TranslationKeys.CommonComponents.Notification.DOCUMENT_NOT_FOUND)
        });
      }
    } else {
      setNotification({
        isError: true,
        message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE)
      });
    }
  };

  const onSuccess = (url: string) => {
    gtmEvents.fileDownload(title);
    saveAs(url, `${title} #${document.orderId}.pdf`);
  };

  const onPoetSuccess = (url: string) => {
    gtmEvents.fileDownload(title);
    window.location.href = url;
  };

  function openPdfInTab() {
    if (documentId === '0') {
      setNotification({
        isError: true,
        message: translate(TranslationKeys.CommonComponents.Notification.FILE_NOT_READY_ERROR)
      });
      return;
    }

    if (DOCUMENT_TITLES_RETRIEVED_BY_COMPANY_CODE.includes(title)) {
      const singleCompanyCode =
        companyCode.split('|').length > 1 ? companyCode.split('|')[0] : companyCode;
      getRentalAgreement.mutate(singleCompanyCode, {
        onSuccess,
        onError
      });
    } else {
      getFile.mutate(
        { documentId, isBillingDocument: false },
        {
          onSuccess,
          onError
        }
      );
    }
  }

  const openPoetDocument = (poetData: IDocument) => {
    if (poetData.id === '') {
      setNotification({
        isError: true,
        message: translate(TranslationKeys.CommonComponents.Notification.FILE_NOT_READY_ERROR)
      });
      return;
    }

    getSasUrl.mutate(
      { docRef: poetData.id, isBillingDocument: false },
      {
        onSuccess: onPoetSuccess,
        onError
      }
    );
  };

  const onClick = async () => {
    if (document.isPoet) {
      openPoetDocument(document);
      return;
    }
    openPdfInTab();
  };

  return (
    <ButtonBase {...styles.button} onClick={onClick} disabled={isLoading}>
      <Grid container {...styles.card}>
        <Grid {...styles.documentData}>
          <Grid item style={{ alignContent: 'center' }}>
            {SIGNABLE_DOCUMENTS.includes(title) ? (
              <CheckmarkIcon {...styles.checkmarkIcon} data-testid="checkmark-icon" />
            ) : (
              <DocumentIcon data-testid="document-icon" />
            )}
          </Grid>
          <Grid item {...styles.titleContainer}>
            <Typography {...styles.cardTitle}>{title}</Typography>
            <Typography>#{orderId}</Typography>
          </Grid>
        </Grid>
        <Grid item {...styles.iconContainer}>
          {isLoading ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            <DownloadIcon {...styles.downloadIcon} />
          )}
        </Grid>
      </Grid>
    </ButtonBase>
  );
};

// -- styles --
const documentCardStyles = {
  button: {
    sx: {
      display: 'flex',
      width: '100%',
      textAlign: 'unset'
    }
  },
  card: {
    sx: {
      gap: Design.Primitives.Spacing.sm,
      display: 'flex',
      flexDirection: 'row',
      padding: '16px',
      flexWrap: 'nowrap',
      justifyContent: 'space-between',
      borderRadius: '8px',
      border: '1px solid var(--color-neutral-neutral300, #CBCBCB)',
      background: 'var(--color-defaults-neutral100-D, #FFF)',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)'
    }
  },
  cardTitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.MdBold,
      color: Design.Alias.Color.accent900
    }
  },
  cardSubtitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: Design.Alias.Color.neutral700
    }
  },
  documentData: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  titleContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column'
    }
  },
  iconContainer: {
    sx: {
      alignContent: 'center'
    }
  },
  checkmarkIcon: {
    style: {
      color: Design.Alias.Color.successMain
    },
    sx: {
      width: '20px',
      height: '20px'
    }
  },
  downloadIcon: {
    sx: {
      width: '24px',
      height: '24px'
    }
  }
};
