import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { PageLayout } from '../../../components/PageLayout';
import { Design } from '../../../helpers/Design';
import { theme } from '../../../PodsTheme';
import { LegacyDocumentCard } from './LegacyDocumentCard';
import { IDocument } from '../../../domain/DocumentEntities';
import { NotificationContext } from '../../../components/notifications/NotificationContext';
import { DocumentsSkeleton } from '../../DocumentsPage/utility/DocumentsSkeleton';
import { useLegacyGetDocuments } from '../../../networkRequests/legacy/queries/useLegacyGetDocuments';

// -- impls --
export const LegacyDocumentsPage: React.FC = () => {
  // -- hooks --
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t: translate } = useTranslation();
  const { documents, error, isPending } = useLegacyGetDocuments();

  // -- state --
  const { setNotification } = useContext(NotificationContext);

  // -- constants --
  const styles = documentPageStyles(isMobile);

  // -- handlers --
  // TODO: Confirm the correct behavior for errors with design/product - eh
  if (error)
    setNotification({
      message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
      isError: true
    });

  if (isPending || !documents) return <DocumentsSkeleton />;

  return (
    <PageLayout columnsLg={6}>
      <Grid {...styles.documentPage}>
        <Grid {...styles.headerSection}>
          <Typography variant="h1">{translate(TranslationKeys.DocumentsPage.HEADER)}</Typography>
          <Typography {...styles.subtitle}>
            {translate(TranslationKeys.DocumentsPage.SUBTITLE)}
          </Typography>
        </Grid>
        <Grid {...styles.documentCards}>
          {documents &&
            documents.map((document: IDocument) => (
              <LegacyDocumentCard document={document} key={document.id} />
            ))}
        </Grid>
      </Grid>
    </PageLayout>
  );
};

// -- styles --
const documentPageStyles = (isMobile: boolean) => ({
  documentPage: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.lgPlus
    }
  },
  headerSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.sm
    }
  },
  subtitle: {
    sx: {
      ...(isMobile ? Design.Alias.Text.BodyUniversal.Md : Design.Alias.Text.BodyUniversal.Lg),
      color: Design.Alias.Color.accent900
    }
  },
  documentCards: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs
    }
  }
});
