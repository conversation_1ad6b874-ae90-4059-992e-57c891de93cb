import React, { useState } from 'react';
import { useBillingContext } from '../../../context/BillingContext';
import { BillingInvoice } from '../../../networkRequests/responseEntities/BillingEntities';
import { LegacyCommercialMakePaymentPage } from './LegacyCommercialMakePaymentPage';
import { LegacyCommercialSelectInvoicesPage } from './LegacyCommercialSelectInvoicesPage';

export const LegacyCommercialMakePaymentFlow = () => {
  const { billingInformation } = useBillingContext();
  const unpaidInvoices = billingInformation.invoices.filter(
    (invoice) => invoice.balanceDue > 0 && !invoice.isPaid
  );
  const [hasCompletedSelection, setHasCompletedSelection] = useState<boolean>(false);
  const [selectedInvoices, setSelectedInvoices] = useState<BillingInvoice[] | null>(null);

  const completeSelection = (invoices: BillingInvoice[]) => {
    setSelectedInvoices(invoices);
    setHasCompletedSelection(true);
  };

  if (selectedInvoices != null && hasCompletedSelection) {
    return (
      <LegacyCommercialMakePaymentPage
        invoicesToPay={selectedInvoices}
        goToSelectPage={() => setHasCompletedSelection(false)}
      />
    );
  }

  return (
    <LegacyCommercialSelectInvoicesPage
      unpaidInvoices={unpaidInvoices}
      selectedInvoices={selectedInvoices}
      makePayment={completeSelection}
    />
  );
};
