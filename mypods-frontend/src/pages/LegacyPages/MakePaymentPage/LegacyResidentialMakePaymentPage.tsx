import React, { useState } from 'react';
import { Grid, Typography, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { BackLink } from '../../../components/buttons/BackLink';
import { ROUTES } from '../../../Routes';
import { LegacyPaymentMethodSelect } from './components/LegacyPaymentMethodSelect';
import { LegacyPaymentAmountButtonGroup } from './components/LegacyPaymentAmountButtonGroup';
import { PageLayout } from '../../../components/PageLayout';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { StretchableLoadingButton } from '../../../components/buttons/StretchableLoadingButton';
import { theme } from '../../../PodsTheme';
import { useLegacyMakePaymentState } from './useLegacyMakePaymentState';
import { useBillingContext } from '../../../context/BillingContext';

// -- types --
interface Props {}

const Tx = TranslationKeys.MakePaymentsPage;

// -- impls --
export const LegacyResidentialMakePaymentPage: React.FC<Props> = () => {
  const { t: translate } = useTranslation();
  const { billingInformation } = useBillingContext();
  const unpaidInvoices = billingInformation.invoices.filter(
    (invoice) => invoice.balanceDue > 0 && !invoice.isPaid
  );
  const state = useLegacyMakePaymentState(unpaidInvoices);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = makePaymentStyles();
  const [isFormValid, setIsFormValid] = useState<boolean>(true);

  return (
    <PageLayout columnsLg={6}>
      <Grid {...styles.container}>
        <Grid {...styles.headerSection}>
          <BackLink route={ROUTES.BILLING} />
          <Grid>
            <Typography variant="h1">{translate(Tx.HEADER)}</Typography>
          </Grid>
        </Grid>
        <LegacyPaymentAmountButtonGroup
          totalAmount={billingInformation.totalBalance}
          setCustomPaymentAmount={state.setPaymentAmount}
          setIsFormValid={setIsFormValid}
        />
        <LegacyPaymentMethodSelect onChange={state.handlePaymentMethodChange} />
        <StretchableLoadingButton
          isMobile={isMobile}
          label={translate(state.submitButtonTx)}
          onClick={state.handleSubmitPayment}
          isLoading={state.isPaymentPending}
          disabled={!isFormValid || state.isPaymentPending || !state.paymentMethod}
        />
      </Grid>
    </PageLayout>
  );
};

// -- styles --
const makePaymentStyles = () => ({
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  headerSection: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  }
});
