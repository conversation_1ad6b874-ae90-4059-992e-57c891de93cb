import React, { act } from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { AxiosError, AxiosResponse } from 'axios';
import { renderWithLegacyProvidersAndState } from '../../../../testUtils/RenderHelpers';
import { mockLegacyPayInvoices, mockNavigate } from '../../../../../setupTests';
import { LegacyMakePaymentPage } from '../LegacyMakePaymentPage';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { initialBillingState } from '../../../../context/BillingContext';
import {
  createBillingInformation,
  createBillingInvoice,
  createCustomer,
  createPaymentMethod
} from '../../../../testUtils/MyPodsFactories';
import { ROUTES } from '../../../../Routes';
import {
  PayInvoicesRequest,
  PaymentDeclinedErrors,
  PaymentMethod
} from '../../../../networkRequests/responseEntities/PaymentEntities';
import {
  BillingInvoice,
  formatByCurrency
} from '../../../../networkRequests/responseEntities/BillingEntities';
import {
  Customer,
  CustomerType
} from '../../../../networkRequests/responseEntities/CustomerEntities';
import { expectNotificationAlertContainsTitle } from '../../../../testUtils/assertions';
import { vi } from 'vitest';

const Tx = TranslationKeys.MakePaymentsPage;

const views = {
  backLink: () => screen.getByTestId('back-link'),
  invoiceLabel: (id: string) => screen.queryByText(Tx.INVOICE_PREFIX + id),
  totalLabel: () => screen.queryByText(Tx.TOTAL_LABEL),
  nextButton: () =>
    screen.getByRole('button', { name: TranslationKeys.CommonComponents.NEXT_BUTTON }),
  submitButton: () => screen.getByRole('button', { name: Tx.SUBMIT_BUTTON }),
  amountField: (): HTMLInputElement => screen.getByRole('textbox', { name: Tx.INPUT_LABEL }),
  customPaymentButton: () => screen.queryByTestId('custom-amount-button'),
  customPaymentButtonSelected: () => screen.queryByTestId('custom-amount-button-selected'),
  fullPaymentButton: () => screen.queryByTestId('full-amount-button'),
  fullPaymentButtonSelected: () => screen.queryByTestId('full-amount-button-selected'),
  paymentMethodDropdown: () =>
    screen.queryByLabelText(TranslationKeys.MakePaymentsPage.SELECT_LABEL),
  addPaymentMethodDropdown: () =>
    screen.queryByRole('link', { name: TranslationKeys.MakePaymentsPage.ADD_PAYMENT_METHOD_LINK })
};

describe('LegacyMakePaymentPage Tests', () => {
  let user: UserEvent;
  const customer = createCustomer();
  const totalBalanceAmount = 200;
  const invoice = createBillingInvoice({ invoiceNumber: '123', balanceDue: 100, isPaid: false });
  const defaultPaymentMethod = createPaymentMethod({ isPrimary: true });
  const commercialCustomer = createCustomer({ customerType: CustomerType.COMMERCIAL });
  const unpaidInvoice1 = createBillingInvoice({
    invoiceNumber: '1',
    balanceDue: 100.9,
    isPaid: false
  });
  const invoice1InDollars = formatByCurrency(unpaidInvoice1.balanceDue);
  const unpaidInvoice2 = createBillingInvoice({
    invoiceNumber: '2',
    balanceDue: 200.45,
    isPaid: false
  });
  const invoice2InDollars = formatByCurrency(unpaidInvoice2.balanceDue);
  const totalCommercialAmount = unpaidInvoice1.balanceDue + unpaidInvoice2.balanceDue;
  const totalAmountInDollars = formatByCurrency(totalCommercialAmount);

  function renderPage(
    paymentMethods: PaymentMethod[] = [defaultPaymentMethod],
    invoices: BillingInvoice[] = [invoice],
    totalBalance: number = totalBalanceAmount,
    initialCustomer: Customer = customer
  ) {
    return renderWithLegacyProvidersAndState(<LegacyMakePaymentPage />, {
      billingState: {
        ...initialBillingState,
        paymentMethods: paymentMethods,
        billingInformation: {
          ...createBillingInformation({
            invoices: invoices,
            totalBalance: totalBalance
          })
        }
      },
      initialCustomer: initialCustomer
    });
  }

  beforeEach(() => {
    user = userEvent.setup();
  });

  describe('Residential Customer', () => {
    it('shows page elements', () => {
      renderPage();

      expect(views.backLink()).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: Tx.HEADER }));
      expect(screen.getByText(Tx.INPUT_LABEL)).toBeInTheDocument();
      expect(screen.getByText(Tx.SELECT_LABEL)).toBeInTheDocument();
      expect(views.submitButton()).toBeInTheDocument();
    });

    it('makes a payment', async () => {
      vi.useFakeTimers({ shouldAdvanceTime: true });
      renderPage();

      expect(views.submitButton()).toBeEnabled();

      await waitFor(() => user.click(views.submitButton()));

      expect(mockLegacyPayInvoices).toHaveBeenCalledWith({
        totalPaymentAmount: totalBalanceAmount,
        invoices: [
          {
            invoiceNumber: invoice.invoiceNumber,
            paymentAmount: invoice.balanceDue,
            dueDate: invoice.dueDate
          }
        ],
        paymentMethodId: defaultPaymentMethod.paymentMethodId
      });
      await act(() => vi.advanceTimersByTimeAsync(35 * 1000));

      expect(screen.getByText(Tx.Notifications.SUCCESS)).toBeInTheDocument();
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.BILLING);
    });

    it('resets button text when payment API call fails', async () => {
      vi.useFakeTimers({ shouldAdvanceTime: true });
      renderPage();

      mockLegacyPayInvoices.mockRejectedValueOnce(
        new AxiosError('Payment failed', '400', undefined, undefined, {
          data: { status: 'DECLINED' },
          status: 400
        } as AxiosResponse)
      );

      expect(views.submitButton()).toBeEnabled();
      expect(screen.getByText(Tx.SUBMIT_BUTTON)).toBeInTheDocument();

      await waitFor(() => user.click(views.submitButton()));

      await act(() => vi.advanceTimersByTimeAsync(3 * 1000));

      expect(await screen.findByText(Tx.Error.Declined.DECLINED)).toBeInTheDocument();
      expect(screen.getByText(Tx.SUBMIT_BUTTON)).toBeInTheDocument();

      await act(() => vi.advanceTimersByTimeAsync(7 * 1000));

      expect(screen.getByText(Tx.SUBMIT_BUTTON)).toBeInTheDocument();
      expect(screen.queryByText(Tx.SUBMIT_BUTTON_SEVEN)).not.toBeInTheDocument();

      await act(() => vi.advanceTimersByTimeAsync(7 * 1000));

      expect(screen.getByText(Tx.SUBMIT_BUTTON)).toBeInTheDocument();
      expect(screen.queryByText(Tx.SUBMIT_BUTTON_FOURTEEN)).not.toBeInTheDocument();

      vi.useRealTimers();
    });

    it('makes a custom payment', async () => {
      renderPage();

      expect(views.customPaymentButtonSelected()).not.toBeInTheDocument();
      expect(views.fullPaymentButtonSelected()).toBeInTheDocument();
      expect(views.amountField()).toBeInTheDocument();

      await act(async () => {
        await user.click(views.customPaymentButton()!);
      });

      expect(views.customPaymentButtonSelected()).toBeInTheDocument();
      expect(views.fullPaymentButtonSelected()).not.toBeInTheDocument();
      expect(views.amountField().value).toBe('');
      expect(views.amountField()).toHaveFocus();

      await act(async () => {
        await user.type(views.amountField(), '100[Tab]');
      });

      expect(views.submitButton()).toBeEnabled();

      await act(async () => {
        await user.click(views.submitButton());
      });

      expect(mockLegacyPayInvoices).toHaveBeenCalledWith({
        totalPaymentAmount: 100,
        invoices: [
          {
            invoiceNumber: invoice.invoiceNumber,
            paymentAmount: invoice.balanceDue,
            dueDate: invoice.dueDate
          }
        ],
        paymentMethodId: defaultPaymentMethod.paymentMethodId
      });
    });

    it('should allow user to select a non-default payment method to make payment', async () => {
      const lastFour = '2222';
      const paymentMethodId = '111222333';
      const notDefaultPaymentMethod = createPaymentMethod({
        isPrimary: false,
        paymentMethodId: paymentMethodId,
        cardNumberLastFourDigits: lastFour
      });
      renderPage([notDefaultPaymentMethod]);

      await act(async () => {
        await userEvent.click(views.paymentMethodDropdown()!);
      });
      await act(async () => {
        await userEvent.click(
          screen.getByText(`${TranslationKeys.MakePaymentsPage.SELECT_OPTION_LABEL}[${lastFour}]`)
        );
      });
      await act(async () => {
        await userEvent.click(views.submitButton());
      });

      const expectedInvoiceRequest: PayInvoicesRequest = {
        invoices: [
          {
            dueDate: invoice.dueDate,
            invoiceNumber: invoice.invoiceNumber,
            paymentAmount: invoice.balanceDue
          }
        ],
        paymentMethodId: paymentMethodId,
        totalPaymentAmount: totalBalanceAmount
      };
      expect(mockLegacyPayInvoices).toHaveBeenCalledWith(expectedInvoiceRequest);
    });

    it('clicking the full amount radio card sets the amount field to the total balance', async () => {
      renderPage();

      await act(async () => {
        await user.click(views.customPaymentButton()!);
      });

      expect(views.amountField().value).toBe('');

      await act(async () => {
        await user.click(views.fullPaymentButton()!);
      });

      expect(views.amountField().value).toBe(totalBalanceAmount.toString() + '.00');
    });

    it('disabled submit with an invalid custom payment', async () => {
      renderPage();

      await act(async () => {
        await user.click(views.customPaymentButton()!);
      });

      await act(async () => {
        await user.clear(views.amountField());
      });

      await act(async () => {
        await user.type(views.amountField(), '10000000');
      });

      expect(views.submitButton()).toBeInTheDocument();
      expect(views.submitButton()).toBeDisabled();

      await act(async () => {
        await user.type(views.amountField(), '0');
      });

      expect(views.submitButton()).toBeDisabled();
    });

    it('displays an error alert when the payment is unsuccessful', async () => {
      mockLegacyPayInvoices.mockRejectedValue('test error');
      renderPage();

      await waitFor(() => user.click(views.submitButton()));

      expect(
        screen.getByText(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE)
      ).toBeInTheDocument();
    });

    it('displays more specific error alert when a card is declined', async () => {
      const status: PaymentDeclinedErrors = 'FRAUD_SUSPECTED';
      const response: AxiosResponse = {
        data: { status },
        status: 400
      } as AxiosResponse;

      mockLegacyPayInvoices.mockRejectedValue(
        new AxiosError('message', '400', undefined, undefined, response)
      );
      renderPage();

      await waitFor(() => user.click(views.submitButton()));

      expectNotificationAlertContainsTitle(
        TranslationKeys.MakePaymentsPage.Error.Declined.FRAUD_SUSPECTED
      );
    });

    it('when user input amount does not match the total amount it should select the custom amount radio option', async () => {
      renderPage();

      expect(views.fullPaymentButtonSelected()).toBeInTheDocument();
      expect(views.customPaymentButtonSelected()).not.toBeInTheDocument();

      await act(async () => {
        await user.clear(views.amountField());
        await user.type(views.amountField(), '101');
      });

      expect(views.fullPaymentButtonSelected()).not.toBeInTheDocument();
      expect(views.customPaymentButtonSelected()).toBeInTheDocument();
    });

    it('when user input amount does match the total amount it should select the full amount radio option', async () => {
      renderPage();

      await act(async () => {
        await user.clear(views.amountField());
      });

      expect(views.fullPaymentButtonSelected()).not.toBeInTheDocument();
      expect(views.customPaymentButtonSelected()).toBeInTheDocument();

      await act(async () => {
        await user.type(views.amountField(), totalBalanceAmount.toString());
      });

      expect(views.fullPaymentButtonSelected()).toBeInTheDocument();
      expect(views.customPaymentButtonSelected()).not.toBeInTheDocument();
    });

    it('should render Add Payment link in the payment method dropdown', async () => {
      renderPage();

      await act(async () => {
        await user.click(views.paymentMethodDropdown()!);
      });

      expect(views.addPaymentMethodDropdown()).toBeInTheDocument();
      expect(views.addPaymentMethodDropdown()?.getAttribute('href')).toEqual(
        ROUTES.MANAGE_PAYMENT_METHOD
      );
    });

    it('should render only the Add Payment link when no payment methods present', async () => {
      renderPage([]);

      expect(views.addPaymentMethodDropdown()).toBeInTheDocument();
      expect(views.paymentMethodDropdown()).not.toBeInTheDocument();
    });
  });

  describe('Commercial Customer', () => {
    it('should render more than 1 unpaid invoices with total balance', async () => {
      renderPage(
        [defaultPaymentMethod],
        [unpaidInvoice1, unpaidInvoice2],
        totalCommercialAmount,
        commercialCustomer
      );

      await waitFor(() => user.click(views.nextButton()));

      // render invoices and total due
      expect(views.invoiceLabel(unpaidInvoice1.invoiceNumber!)).toBeInTheDocument();
      expect(screen.getByText(invoice1InDollars)).toBeInTheDocument();
      expect(views.invoiceLabel(unpaidInvoice2.invoiceNumber!)).toBeInTheDocument();
      expect(screen.getByText(invoice2InDollars)).toBeInTheDocument();
      expect(views.totalLabel()).toBeInTheDocument();
      // render Payment Amount as read-only
      expect(screen.getAllByText(totalAmountInDollars).length).toBe(2);
      expect(screen.getByText(Tx.INPUT_LABEL)).toBeInTheDocument();
      expect(screen.getByText(Tx.PAYMENT_AMOUNT_DISCLAIMER)).toBeInTheDocument();
    });

    it('should render only 1 unpaid invoice', async () => {
      renderPage(
        [defaultPaymentMethod],
        [unpaidInvoice1],
        unpaidInvoice1.balanceDue,
        commercialCustomer
      );

      await waitFor(() => user.click(views.nextButton()));

      // render invoice
      expect(views.invoiceLabel(unpaidInvoice1.invoiceNumber!)).toBeInTheDocument();
      expect(views.totalLabel()).not.toBeInTheDocument();

      // render Payment Amount as editable
      expect(screen.getByText(Tx.INPUT_LABEL)).toBeInTheDocument();
      expect(screen.queryByText(Tx.PAYMENT_AMOUNT_DISCLAIMER)).not.toBeInTheDocument();
      expect(views.amountField()).toBeInTheDocument();
      expect(views.fullPaymentButtonSelected()).toBeInTheDocument();
      expect(views.customPaymentButtonSelected()).not.toBeInTheDocument();
    });

    it('makes a payment', async () => {
      vi.useFakeTimers({ shouldAdvanceTime: true });
      renderPage(
        [defaultPaymentMethod],
        [unpaidInvoice1, unpaidInvoice2],
        totalCommercialAmount,
        commercialCustomer
      );

      await waitFor(() => user.click(views.nextButton()));
      await waitFor(() => user.click(views.submitButton()));

      expect(mockLegacyPayInvoices).toHaveBeenCalledWith({
        totalPaymentAmount: totalCommercialAmount,
        invoices: [
          {
            invoiceNumber: unpaidInvoice1.invoiceNumber,
            paymentAmount: unpaidInvoice1.balanceDue,
            dueDate: unpaidInvoice1.dueDate
          },
          {
            invoiceNumber: unpaidInvoice2.invoiceNumber,
            paymentAmount: unpaidInvoice2.balanceDue,
            dueDate: unpaidInvoice2.dueDate
          }
        ],
        paymentMethodId: defaultPaymentMethod.paymentMethodId
      });
      await act(() => vi.advanceTimersByTimeAsync(35 * 1000));

      expect(screen.getByText(Tx.Notifications.SUCCESS)).toBeInTheDocument();
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.BILLING);
    });
  });

  describe('Commercial Select Invoices Page', () => {
    it('should render all elements', () => {
      renderPage(
        [defaultPaymentMethod],
        [unpaidInvoice1, unpaidInvoice2],
        totalCommercialAmount,
        commercialCustomer
      );

      expect(views.backLink()).toBeInTheDocument();
      expect(
        screen.getByText(`${TranslationKeys.CommonComponents.PROGRESS_COUNTER}[1,2]`)
      ).toBeInTheDocument();

      expect(views.invoiceLabel(unpaidInvoice1.invoiceNumber!)).toBeInTheDocument();
      expect(screen.getByText(invoice1InDollars)).toBeInTheDocument();

      expect(views.invoiceLabel(unpaidInvoice2.invoiceNumber!)).toBeInTheDocument();
      expect(screen.getByText(invoice2InDollars)).toBeInTheDocument();

      const invoiceCheckboxes = screen.getAllByRole('checkbox');
      expect(invoiceCheckboxes).toHaveLength(3);
      const [allInvoice, invoice1Checkbox, invoice2Checkbox] = invoiceCheckboxes;
      expect(screen.getByText(Tx.SelectInvoice.ALL_INVOICES)).toBeInTheDocument();
      expect(allInvoice).toBeChecked();
      expect(invoice1Checkbox).toBeChecked();
      expect(invoice2Checkbox).toBeChecked();

      expect(views.nextButton()).toBeEnabled();
    });

    it('should navigate to back to the billing page on back click', async () => {
      renderPage();

      await act(async () => {
        await userEvent.click(views.backLink());
      });

      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.BILLING);
    });

    it('should update All Invoices checkbox when selecting and deselecting invoices', async () => {
      renderPage(
        [defaultPaymentMethod],
        [unpaidInvoice1, unpaidInvoice2],
        totalCommercialAmount,
        commercialCustomer
      );

      const allCheckboxes = screen.getAllByRole('checkbox');

      // uncheck the first invoice
      await act(async () => {
        await userEvent.click(allCheckboxes[1]);
      });

      // Expect the All Invoices checkbox to be unchecked
      expect(allCheckboxes[0]).not.toBeChecked();

      // check the first invoice again
      await act(async () => {
        await userEvent.click(allCheckboxes[1]);
      });

      // Expect the All Invoices checkbox to be checked again
      expect(allCheckboxes[0]).toBeChecked();
    });

    it('should disable Next button when no invoices are selected', async () => {
      renderPage(
        [defaultPaymentMethod],
        [unpaidInvoice1, unpaidInvoice2],
        totalCommercialAmount,
        commercialCustomer
      );

      const allCheckboxes = screen.getAllByRole('checkbox');

      // uncheck all invoices
      await act(async () => {
        await userEvent.click(allCheckboxes[0]);
      });

      expect(views.nextButton()).toBeDisabled();
    });

    it('should redirect us to 2nd page of Make Payment with correct invoices when next is clicked', async () => {
      renderPage(
        [defaultPaymentMethod],
        [unpaidInvoice1, unpaidInvoice2],
        totalCommercialAmount,
        commercialCustomer
      );

      const allCheckboxes = screen.getAllByRole('checkbox');

      // uncheck first invoice
      await act(async () => {
        await userEvent.click(allCheckboxes[1]);
      });

      // click next
      await act(async () => {
        await userEvent.click(views.nextButton());
      });

      // total should be 2nd invoice balance
      expect(views.invoiceLabel(unpaidInvoice1.invoiceNumber!)).not.toBeInTheDocument();
      expect(views.invoiceLabel(unpaidInvoice2.invoiceNumber!)).toBeInTheDocument();

      // Payment Amount has invoice2 balance
      expect(views.amountField().value).toEqual(unpaidInvoice2.balanceDue.toString());
    });

    it('should preserve invoice selection when return to the Selection page after going forward', async () => {
      renderPage(
        [defaultPaymentMethod],
        [unpaidInvoice1, unpaidInvoice2],
        totalCommercialAmount,
        commercialCustomer
      );

      // uncheck first invoice
      await act(async () => {
        await userEvent.click(screen.getAllByRole('checkbox')[1]);
      });

      // click next
      await act(async () => {
        await userEvent.click(views.nextButton());
      });

      // click back link
      await act(async () => {
        await userEvent.click(views.backLink());
      });

      // renders CommercialSelectInvoicesPage
      expect(
        screen.getByText(`${TranslationKeys.CommonComponents.PROGRESS_COUNTER}[1,2]`)
      ).toBeInTheDocument();

      const allCheckboxes = screen.getAllByRole('checkbox');

      // preserves invoice selection
      expect(allCheckboxes[0]).not.toBeChecked();
      expect(allCheckboxes[1]).not.toBeChecked();
      expect(allCheckboxes[2]).toBeChecked();
    });
  });
});
