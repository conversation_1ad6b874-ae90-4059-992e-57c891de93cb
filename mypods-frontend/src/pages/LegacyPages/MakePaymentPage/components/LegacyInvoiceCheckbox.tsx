import { useTranslation } from 'react-i18next';
import React from 'react';
import { Checkbox, FormControlLabel, Grid, Typography } from '@mui/material';
import Button from '@mui/material/Button';
import { formatDateWithSuffix } from '../../../../helpers/dateHelpers';
import { formatByCurrency } from '../../../../networkRequests/responseEntities/BillingEntities';
import { Design } from '../../../../helpers/Design';
import { TranslationKeys } from '../../../../locales/TranslationKeys';

type InvoiceCheckboxProps = {
  invoiceNumber: string;
  dueDate?: string;
  balanceDue: number;
  checked: boolean;
  onCheckboxChange: () => void;
};

const Tx = TranslationKeys.MakePaymentsPage;

export const LegacyInvoiceCheckbox = ({
  invoiceNumber,
  dueDate,
  balanceDue,
  checked,
  onCheckboxChange
}: InvoiceCheckboxProps) => {
  const { t: translate } = useTranslation();

  const styles = invoiceCheckboxStyles(checked);
  return (
    <Button
      onClick={(e) => {
        e.preventDefault();
        onCheckboxChange();
      }}
      {...styles.checkboxButton}
      disableRipple>
      <Grid item {...styles.container}>
        <FormControlLabel
          control={<Checkbox checked={checked} onChange={onCheckboxChange} color="secondary" />}
          label={
            <Grid container flexDirection="column">
              <Typography {...styles.invoiceNumber}>
                {translate(Tx.INVOICE_PREFIX)}
                {invoiceNumber}
              </Typography>
              <Typography variant="subtitle1" {...styles.dueBy}>
                {translate(Tx.SelectInvoice.DUE_ON_PREFIX)}
                {formatDateWithSuffix(dueDate ?? '')}
              </Typography>
            </Grid>
          }
          labelPlacement="end"
        />
        <Typography variant="body1" {...styles.amount}>
          {formatByCurrency(balanceDue)}
        </Typography>
      </Grid>
    </Button>
  );
};

type SelectAllInvoiceCheckboxProps = {
  checked: boolean;
  totalBalance: number;
  onCheckboxChange: (checkedStatus: boolean) => void;
};

export const SelectAllInvoiceCheckbox = ({
  checked,
  totalBalance,
  onCheckboxChange
}: SelectAllInvoiceCheckboxProps) => {
  const { t: translate } = useTranslation();
  const styles = invoiceCheckboxStyles(checked);

  const handleClick = () => {
    onCheckboxChange(!checked);
  };

  return (
    <Button
      onClick={(e) => {
        e.preventDefault();
        handleClick();
      }}
      {...styles.checkboxButton}
      disableRipple>
      <Grid item {...styles.container}>
        <FormControlLabel
          control={<Checkbox checked={checked} onChange={handleClick} color="secondary" />}
          label={
            <Typography {...styles.allInvoice}>
              {translate(Tx.SelectInvoice.ALL_INVOICES)}
            </Typography>
          }
          labelPlacement="end"
        />
        <Typography variant="body1" {...styles.amount}>
          {formatByCurrency(totalBalance)}
        </Typography>
      </Grid>
    </Button>
  );
};

const invoiceCheckboxStyles = (checked: boolean) => ({
  checkboxButton: {
    sx: {
      padding: 0,
      textTransform: 'unset'
    }
  },
  container: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderRadius: '8px',
      border: checked
        ? `2px solid ${Design.Alias.Color.secondary500}`
        : `2px solid ${Design.Alias.Color.neutral300}`,
      background: checked ? Design.Alias.Color.secondary100 : Design.Alias.Color.neutral100,
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)',
      padding: '16px',
      minWidth: '100%',
      textAlign: 'left'
    }
  },
  invoiceNumber: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.MdBold,
      color: Design.Alias.Color.accent900,
      alignSelf: 'start'
    }
  },
  allInvoice: {
    ...Design.Alias.Text.BodyUniversal.MdBold,
    color: Design.Alias.Color.accent900,
    alignSelf: 'center'
  },
  dueBy: {
    sx: {
      color: 'rgb(0,0,0,0.6)'
    }
  },
  amount: {
    sx: {
      color: Design.Alias.Color.accent900
    }
  }
});
