import React, { ChangeEvent, Dispatch, SetStateAction, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, InputAdornment, TextField, Typography, useMediaQuery } from '@mui/material';
import { CardRadioButton } from '../../../../components/buttons/CardRadioButton';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { Design } from '../../../../helpers/Design';
import { formatByCurrency } from '../../../../networkRequests/responseEntities/BillingEntities';
import { theme } from '../../../../PodsTheme';
import { CurrencyMask } from '../../../MakePaymentPage/components/CurrencyMask';

// -- types --
interface Props {
  totalAmount: number;
  setCustomPaymentAmount: Dispatch<SetStateAction<number>>;
  setIsFormValid: (value: boolean) => void;
}

const Tx = TranslationKeys.MakePaymentsPage;

// -- impls --
export const LegacyPaymentAmountButtonGroup: React.FC<Props> = ({
  totalAmount,
  setCustomPaymentAmount,
  setIsFormValid
}: Props) => {
  const { t: translate } = useTranslation();

  const [isCustomPayment, setIsCustomPayment] = useState(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [userInput, setUserInput] = useState<string>(totalAmount.toString());
  const [errorMessage, setErrorMessage] = useState<string>('');

  const inputRef = useRef<HTMLInputElement>(null);

  const styles = componentStyles(isMobile);

  const setError = (message: string) => {
    setIsFormValid(!message);
    setErrorMessage(message);
  };

  const parseFloatFromString = (input: string) => parseFloat(input.replace(/,/g, '')) || 0;

  const labelWithCurrency = (text: string, dollarAmount?: number) => (
    <Grid container {...styles.customLabel}>
      {dollarAmount && (
        <Typography {...styles.textLabel}>{formatByCurrency(dollarAmount, 'USD')}</Typography>
      )}
      <Typography {...styles.currencyLabel}>{text}</Typography>
    </Grid>
  );

  const handlePaymentFieldOnChange = (
    event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>
  ) => {
    setUserInput(event.target.value);
    const valueInput = parseFloatFromString(event.target.value);

    if (valueInput !== totalAmount) {
      setIsCustomPayment(true);
      if (valueInput > totalAmount) {
        setError(translate(Tx.Error.EXCEEDS_TOTAL_AMOUNT_DUE));
      } else if (valueInput <= 0) {
        setIsFormValid(false);
      } else {
        setError('');
      }
    } else {
      setIsCustomPayment(false);
      setError('');
    }
  };

  const handlePaymentFieldBlur = () => {
    if (isCustomPayment) {
      const value = parseFloatFromString(userInput);

      let amount: number;
      if (value <= 0) {
        amount = 1.0;
      } else if (value > totalAmount) {
        amount = totalAmount;
      } else {
        amount = value;
      }
      setCustomPaymentAmount(amount);
      setUserInput(amount.toString());
    }
  };

  const handleFullAmountClick = () => {
    setUserInput(totalAmount.toString());
    setCustomPaymentAmount(totalAmount);
    setIsCustomPayment(false);
  };

  const handleCustomAmountClick = () => {
    setUserInput('');
    if (inputRef.current) {
      inputRef.current.focus();
    }
    setIsCustomPayment(true);
  };

  return (
    <>
      <TextField
        label={translate(Tx.INPUT_LABEL)}
        value={userInput.toString()}
        onBlur={handlePaymentFieldBlur}
        onChange={handlePaymentFieldOnChange}
        error={!!errorMessage}
        helperText={errorMessage}
        variant="standard"
        type="text"
        inputProps={{ inputMode: 'decimal', pattern: '[0-9.]*' }}
        inputRef={inputRef}
        InputProps={{
          startAdornment: <InputAdornment position="start">$</InputAdornment>,
          inputComponent: CurrencyMask as any
        }}
        {...styles.paymentAmountInput}
      />
      <Grid container {...styles.container}>
        <CardRadioButton
          data-testid="full-amount-button"
          label={labelWithCurrency(translate(Tx.RadioOptions.FULL_AMOUNT), totalAmount)}
          value={totalAmount}
          isSelected={!isCustomPayment}
          onClick={handleFullAmountClick}
        />
        <CardRadioButton
          data-testid="custom-amount-button"
          disabled
          label={labelWithCurrency(
            translate(Tx.RadioOptions.CUSTOM_AMOUNT),
            parseFloatFromString(userInput)
          )}
          value={parseFloatFromString(userInput)}
          isSelected={isCustomPayment}
          onClick={handleCustomAmountClick}
        />
      </Grid>
    </>
  );
};

// -- styles --
const componentStyles = (isMobile: boolean) => ({
  container: {
    sx: {
      display: 'flex',
      flexDirection: isMobile ? 'column' : 'row',
      gap: '16px'
    }
  },
  customLabel: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'flex-start'
    }
  },
  textLabel: {
    ...Design.Alias.Text.BodyUniversal.MdBold,
    color: Design.Alias.Color.accent900
  },
  currencyLabel: {
    ...Design.Alias.Text.BodyUniversal.Sm,
    color: Design.Alias.Color.neutral700
  },
  paymentAmountInput: {
    sx: {
      '.MuiInputBase-root': {
        ...Design.Alias.Text.Heading.Desktop.Xl,
        color: Design.Alias.Color.accent900
      },
      '.MuiInputBase-root.Mui-focused': {
        ...Design.Alias.Text.Heading.Desktop.Xl,
        color: Design.Alias.Color.secondary700
      },
      '.MuiInputBase-root::before': {
        borderColor: Design.Alias.Color.accent900
      },
      '.MuiInputBase-root::after': {
        borderColor: Design.Alias.Color.secondary700
      },
      '.MuiFormLabel-root': {
        color: Design.Alias.Color.accent900
      },
      '.MuiFormLabel-root.Mui-focused': {
        color: Design.Alias.Color.accent900
      },
      '.MuiInputBase-root > .MuiInputAdornment-root > .MuiTypography-root': {
        ...Design.Alias.Text.Heading.Desktop.Xl,
        color: Design.Alias.Color.accent900
      },
      '.MuiInputBase-root.Mui-focused > .MuiInputAdornment-root > .MuiTypography-root': {
        color: Design.Alias.Color.secondary700
      },
      '.MuiInputBase-root.Mui-disabled > .MuiInputAdornment-root > .MuiTypography-root': {
        color: Design.Alias.Color.neutral500
      }
    }
  }
});
