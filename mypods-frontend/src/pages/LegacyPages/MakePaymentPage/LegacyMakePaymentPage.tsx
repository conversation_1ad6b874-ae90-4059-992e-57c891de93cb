import React from 'react';
import { CustomerType } from '../../../networkRequests/responseEntities/CustomerEntities';
import { LegacyResidentialMakePaymentPage } from './LegacyResidentialMakePaymentPage';
import { LegacyCommercialMakePaymentFlow } from './LegacyCommercialMakePaymentFlow';
import { useLegacyGetCustomer } from '../../../networkRequests/legacy/queries/useLegacyGetCustomer';

export const LegacyMakePaymentPage = () => {
  const { customer } = useLegacyGetCustomer();

  if (customer.customerType === CustomerType.COMMERCIAL) {
    return <LegacyCommercialMakePaymentFlow />;
  }
  return <LegacyResidentialMakePaymentPage />;
};
