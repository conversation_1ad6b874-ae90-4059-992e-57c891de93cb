import { screen, waitFor, within } from '@testing-library/react';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { renderWithLegacyProvidersAndState } from '../../../../testUtils/RenderHelpers';
import {
  createPaymentMethod,
  createRefreshSessionClaims
} from '../../../../testUtils/MyPodsFactories';
import {
  mockLegacySetDefaultPaymentMethod,
  mockNavigate,
  mockRefreshSession
} from '../../../../../setupTests';
import { ROUTES } from '../../../../Routes';
import { CardIcon } from '../../../../components/icons/PaymentTypeIcon';
import { PaymentMethod } from '../../../../networkRequests/responseEntities/PaymentEntities';
import { LegacyPaymentMethodsPage } from '../LegacyPaymentMethodsPage';
import { initialBillingState } from '../../../../context/BillingContext';
import React, { act } from 'react';
import { vi } from 'vitest';
import { Typography } from '@mui/material';
import { CallToChangeModalProps } from '../../../PaymentMethodsPage/CallToChangeModal';

const Tx = TranslationKeys.PaymentMethodsPage;

vi.mock('../../../PaymentMethodsPage/CallToChangeModal', () => ({
  CallToChangeModal: ({ open }: CallToChangeModalProps) => {
    if (!open) return <Typography>Not Open</Typography>;
    return <Typography>Call To Change Modal Is Open</Typography>;
  }
}));

describe('Payment Methods Page', () => {
  let user: UserEvent;
  const secondaryPaymentMethod = createPaymentMethod({
    paymentMethodId: 'secondary',
    cardType: CardIcon.MASTERCARD,
    isPrimary: false
  });
  let primaryPaymentMethod = createPaymentMethod({
    paymentMethodId: 'primary',
    cardType: CardIcon.VISA,
    isPrimary: true
  });
  const expectedPaymentMethod: PaymentMethod[] = [primaryPaymentMethod, secondaryPaymentMethod];

  const makeSecondaryCardPrimary = async () => {
    const secondaryRow = await screen.findByTestId(
      `payment-method-${secondaryPaymentMethod.paymentMethodId}`
    );
    const makeDefaultButton = await within(secondaryRow).findByText(`${Tx.MAKE_DEFAULT}[default]`);
    await waitFor(() => user.click(makeDefaultButton));
  };

  beforeEach(() => {
    user = userEvent.setup();
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
  });

  const renderPaymentMethodsPage = (paymentMethods: PaymentMethod[] = expectedPaymentMethod) => {
    return renderWithLegacyProvidersAndState(<LegacyPaymentMethodsPage />, {
      billingState: {
        ...initialBillingState,
        paymentMethods: paymentMethods
      }
    });
  };

  it('should show a list of payment methods beneath the header', async () => {
    renderPaymentMethodsPage();

    expect(await screen.findByText(Tx.HEADER)).toBeInTheDocument();

    expect(
      await screen.findByTestId(`payment-method-${secondaryPaymentMethod.paymentMethodId}`)
    ).toBeInTheDocument();
  });

  it('should display default chip only on the primary payment methods', async () => {
    renderPaymentMethodsPage();

    const visa = await screen.findByTestId(
      `payment-method-${primaryPaymentMethod.paymentMethodId}`
    );
    const mastercard = await screen.findByTestId(
      `payment-method-${secondaryPaymentMethod.paymentMethodId}`
    );
    expect(within(visa).queryByText(Tx.DEFAULT)).toBeInTheDocument();
    expect(within(mastercard).queryByText(Tx.DEFAULT)).not.toBeInTheDocument();
  });

  it('should update default payment method, and refresh the payment methods', async () => {
    renderPaymentMethodsPage();
    await makeSecondaryCardPrimary();

    expect(await screen.findByText(Tx.Messages.SUCCESS)).toBeInTheDocument();
  });

  it('should display loading indicator when setting default payment', async () => {
    // Mock return an unresolved promise to simulate a loading state
    mockLegacySetDefaultPaymentMethod.mockImplementation(() => new Promise(() => {}));
    renderPaymentMethodsPage();

    await makeSecondaryCardPrimary();

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it("should show an error notification if the default payment method can't be set", async () => {
    mockLegacySetDefaultPaymentMethod.mockRejectedValue('test error');
    renderPaymentMethodsPage();

    await makeSecondaryCardPrimary();

    expect(screen.getByText(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE));
  });

  it('should navigate back to the billing page', async () => {
    renderPaymentMethodsPage();

    const link = await screen.findByTestId('back-link');
    await waitFor(async () => await user.click(link));

    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.BILLING);
  });

  it('should navigate to the manage payments page', async () => {
    renderPaymentMethodsPage();

    const addPaymentButton = await screen.findByRole('button', {
      name: Tx.ADD_PAYMENT_METHOD_BUTTON
    });
    await act(() => user.click(addPaymentButton)); // Wrap in an act to get around button ripple warning

    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.MANAGE_PAYMENT_METHOD);
  });

  it('should display a payment card for an Upgrade loan', async () => {
    const methods = [
      createPaymentMethod({
        cardType: 'LineOfCredit',
        locAmount: 5185.0,
        locApplicationId: 1111471,
        locBalance: 2000.97,
        locIsActive: true,
        locLender: 'Upgrade Indirect',
        locOriginationDate: new Date('08-20-2023'),
        locProviderName: 'Acorn Financial',
        locTerminationDate: new Date('08-10-2023')
      })
    ];
    renderPaymentMethodsPage(methods);

    expect(
      await screen.findByText(TranslationKeys.PaymentMethodsPage.PaymentType.MOVE_LOAN_UPGRADE_TEXT)
    ).toBeInTheDocument();
    expect(
      await screen.findByText(TranslationKeys.PaymentMethodsPage.PaymentType.PERSONAL_LOAN)
    ).toBeInTheDocument();
    expect(
      screen.queryByText(`${TranslationKeys.PaymentMethodsPage.MAKE_DEFAULT}[default]`)
    ).not.toBeInTheDocument();
  });

  it('should display a payment card for an CitiPay loan', async () => {
    const methods = [
      createPaymentMethod({
        cardType: 'LineOfCredit',
        locIsActive: true,
        locLender: 'Citi Bank'
      })
    ];
    renderPaymentMethodsPage(methods);

    expect(
      await screen.findByText(
        TranslationKeys.PaymentMethodsPage.PaymentType.MOVE_LOAN_CITI_BANK_TEXT
      )
    ).toBeInTheDocument();
    expect(
      screen.queryByText(TranslationKeys.PaymentMethodsPage.PaymentType.PERSONAL_LOAN)
    ).not.toBeInTheDocument();
    expect(
      screen.queryByText(`${TranslationKeys.PaymentMethodsPage.MAKE_DEFAULT}[default]`)
    ).not.toBeInTheDocument();
  });

  describe('when the primary payment method is line of credit and Citi is the lender', () => {
    it('should display all other payment cards with make default button that opens a modal to call', async () => {
      const methods = [
        createPaymentMethod({
          cardType: 'LineOfCredit',
          locLender: 'Citi Bank',
          isPrimary: true
        }),
        secondaryPaymentMethod
      ];
      renderPaymentMethodsPage(methods);

      await makeSecondaryCardPrimary();

      expect(await screen.findByText('Call To Change Modal Is Open')).toBeInTheDocument();
    });
  });

  describe('when the primary payment method is a line of credit with other lenders', () => {
    it('should display all other payment cards with info icon', async () => {
      const methods = [
        createPaymentMethod({
          cardType: 'LineOfCredit',
          isPrimary: true
        }),
        secondaryPaymentMethod
      ];
      renderPaymentMethodsPage(methods);

      const mastercard = await screen.findByTestId(
        `payment-method-${secondaryPaymentMethod.paymentMethodId}`
      );
      expect(await within(mastercard).findByTestId('payment-info-icon')).toBeInTheDocument();
      expect(
        within(mastercard).queryByText(
          `${TranslationKeys.PaymentMethodsPage.MAKE_DEFAULT}[default]`
        )
      ).not.toBeInTheDocument();
    });
  });
});
