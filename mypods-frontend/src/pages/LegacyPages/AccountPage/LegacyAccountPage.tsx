import React from 'react';
import { Grid } from '@mui/material';
import { LegacyAccountInfoSection } from './LegacyAccountInfoSection';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { LegacyContactInfoSection } from './LegacyContactInfoSection';
import { PageLayout } from '../../../components/PageLayout';
import { LegacyAddressInfoSection } from './LegacyAddressInfoSection';
import { LegacyCustomerNameSection } from './LegacyCustomerNameSection';
import { LegacySmsPreferencesField } from './AccountFields/SmsPreferencesField/LegacySmsPreferencesField';
import { Design } from '../../../helpers/Design';
import { LegacySupportSection } from './AccountFields/SecurityField/LegacySupportSection';
import { Section } from '../../AccountPage/Section';

export const LegacyAccountPage = () => (
  <PageLayout columnsLg={6}>
    <LegacyCustomerNameSection />
    <Grid container flexDirection="column" rowGap={Design.Primitives.Spacing.lgPlus}>
      <Section translationKey={TranslationKeys.AccountPage.AccountInfo.HEADER}>
        <LegacyAccountInfoSection />
      </Section>
      <Section translationKey={TranslationKeys.AccountPage.ContactInfo.HEADER}>
        <LegacyContactInfoSection />
      </Section>
      <Section translationKey={TranslationKeys.AccountPage.Communication.HEADER}>
        <LegacySmsPreferencesField />
      </Section>
      <Section translationKey={TranslationKeys.AccountPage.AddressInfo.HEADER}>
        <LegacyAddressInfoSection />
      </Section>
      <Section translationKey={TranslationKeys.AccountPage.SupportInfo.HEADER}>
        <LegacySupportSection />
      </Section>
    </Grid>
  </PageLayout>
);
