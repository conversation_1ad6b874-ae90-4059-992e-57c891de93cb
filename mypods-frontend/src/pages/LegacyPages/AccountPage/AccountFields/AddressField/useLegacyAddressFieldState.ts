import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CustomerAddress } from '../../../../../networkRequests/responseEntities/CustomerEntities';
import {
  isNullOrEmpty,
  isValidCountryCode,
  isValidPostalCode,
  isValidState
} from '../../../../../helpers/validation/Validators';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { PlaceAddress } from '../../../../../helpers/googlePlace/getAddressFromPlace';
import { NEW_IDENTITY } from '../../../../../networkRequests/MyPodsConstants';

export interface IAddressFieldState {
  getValueFromPlaceAddress: (placeAddress: PlaceAddress) => CustomerAddress;
  handleAddress1Change: (updatedValue: string) => void;
  handleAddress2Change: (updatedValue: string) => void;
  handlePostalCodeChange: (updatedValue: string) => void;
  handleCityChange: (updatedValue: string) => void;
  handleStateChange: (updatedValue: string) => void;
  handleCountryCodeChange: (updatedValue: string) => void;
  displayStreetAddressError: () => void;
  displayPostalCodeError: () => void;
  displayCityError: () => void;
  displayStateError: () => void;
  displayErrorForAllFields: () => void;
  clearErrors: () => void;
  value: CustomerAddress;
  setValue: (value: ((prevState: CustomerAddress) => CustomerAddress) | CustomerAddress) => void;
  isValid: () => boolean;
  error: {
    streetAddress: string | undefined;
    city: string | undefined;
    postalCode: string | undefined;
    state: string | undefined;
  };
}

const blankAddress: CustomerAddress = {
  address1: '',
  address2: '',
  city: '',
  postalCode: '',
  state: '',
  regionCode: '',
  id: NEW_IDENTITY,
  addressType: ''
};

const cleanupAddress = (address?: CustomerAddress): CustomerAddress => {
  if (!address) {
    return blankAddress;
  }
  return {
    address1: address.address1 ?? '',
    address2: address.address2 ?? '',
    city: address.city ?? '',
    postalCode: address.postalCode ?? '',
    state: address.state ?? '',
    regionCode: address.regionCode,
    id: address.id,
    addressType: address.addressType
  };
};

export const useLegacyAddressFieldState = (initialValue?: CustomerAddress) => {
  const { t: translate } = useTranslation();
  const [value, setValue] = useState<CustomerAddress>(cleanupAddress(initialValue));

  const getValueFromPlaceAddress = (placeAddress: PlaceAddress) => {
    const result: CustomerAddress = { ...value };
    if (placeAddress?.streetAddress != null) {
      result.address1 = placeAddress.streetAddress;
    }
    if (placeAddress?.city != null) {
      result.city = placeAddress.city;
    }
    if (placeAddress?.state != null) {
      result.state = placeAddress.state;
    }
    if (placeAddress?.postalCode != null) {
      result.postalCode = placeAddress.postalCode;
    }
    if (placeAddress?.country != null) {
      result.regionCode = placeAddress.country;
    }
    return result;
  };

  const handleAddress1Change = (updatedValue: string) => {
    setValue({ ...value, address1: updatedValue });
  };
  const handleAddress2Change = (updatedValue: string) => {
    setValue({ ...value, address2: updatedValue });
  };
  const handlePostalCodeChange = (updatedValue: string) => {
    setValue({
      ...value,
      postalCode: updatedValue,
      regionCode: getCountryFromPostalCode(updatedValue)
    });
  };
  const handleCityChange = (updatedValue: string) => {
    setValue({ ...value, city: updatedValue });
  };
  const handleStateChange = (updatedValue: string) => {
    setValue({ ...value, state: updatedValue });
  };

  const getCountryFromPostalCode = (postalCode: string): string => {
    // Check for standard 5-digit US ZIP code or ZIP+4 format (e.g., 12345 or 12345-6789)
    if (
      (postalCode.length === 5 && /^\d+$/.test(postalCode)) ||
      (postalCode.length === 10 && /^\d{5}-\d{4}$/.test(postalCode))
    ) {
      return 'US';
    }

    // Check for Canadian postal codes in multiple formats (e.g., A1A 1A1, A1A1A1, or A1A)
    if (
      (postalCode.length === 3 && /^[ABCEGHJ-NPRSTVXY][0-9][ABCEGHJ-NPRSTV-Z]$/.test(postalCode)) ||
      (postalCode.length === 7 &&
        /^[ABCEGHJ-NPRSTVXY][0-9][ABCEGHJ-NPRSTV-Z] ?[0-9][ABCEGHJ-NPRSTV-Z][0-9]$/.test(
          postalCode
        )) ||
      (postalCode.length === 6 &&
        /^[ABCEGHJ-NPRSTVXY][0-9][ABCEGHJ-NPRSTV-Z]?[0-9][ABCEGHJ-NPRSTV-Z][0-9]$/.test(postalCode))
    ) {
      return 'CA';
    }
    // returning default countryCode as US since user cant update it.
    return 'US';
  };

  // region Validation Errors

  const [streetAddressError, setStreetAddressError] = useState<string | undefined>();
  const [postalCodeError, setPostalCodeError] = useState<string | undefined>();
  const [cityError, setCityError] = useState<string | undefined>();
  const [stateError, setStateError] = useState<string | undefined>();

  const displayStreetAddressError = () => {
    if (isNullOrEmpty(value.address1))
      setStreetAddressError(translate(TranslationKeys.CommonComponents.Input.Error.REQUIRED));
    else {
      setStreetAddressError(undefined);
    }
  };

  const displayPostalCodeError = () => {
    if (isNullOrEmpty(value.postalCode))
      setPostalCodeError(translate(TranslationKeys.CommonComponents.Input.Error.REQUIRED));
    else if (!isValidPostalCode(value.postalCode)) {
      setPostalCodeError(
        translate(TranslationKeys.CommonComponents.Input.Error.INVALID_POSTAL_CODE)
      );
    } else {
      setPostalCodeError(undefined);
    }
  };

  const displayCityError = () => {
    if (isNullOrEmpty(value.city))
      setCityError(translate(TranslationKeys.CommonComponents.Input.Error.REQUIRED));
    else {
      setCityError(undefined);
    }
  };

  const displayStateError = () => {
    if (isNullOrEmpty(value.state))
      setStateError(translate(TranslationKeys.CommonComponents.Input.Error.REQUIRED));
    else if (!isValidState(value.state)) {
      setStateError(translate(TranslationKeys.CommonComponents.Input.Error.STATE_LENGTH));
    } else {
      setStateError(undefined);
    }
  };

  const displayErrorForAllFields = () => {
    displayStreetAddressError();
    displayPostalCodeError();
    displayCityError();
    displayStateError();
  };

  const clearErrors = () => {
    setStreetAddressError(undefined);
    setPostalCodeError(undefined);
    setCityError(undefined);
    setStateError(undefined);
  };

  // endregion Validation Errors

  const isValid = () => {
    if (isNullOrEmpty(value.address1)) return false;
    if (isNullOrEmpty(value.postalCode)) return false;
    if (isNullOrEmpty(value.city)) return false;
    if (!isValidState(value.state)) return false;
    if (!isValidCountryCode(value.regionCode)) return false;
    return true;
  };

  return <IAddressFieldState>{
    getValueFromPlaceAddress,
    handleAddress1Change,
    handleAddress2Change,
    handlePostalCodeChange,
    handleCityChange,
    handleStateChange,
    displayStreetAddressError,
    displayPostalCodeError,
    displayCityError,
    displayStateError,
    displayErrorForAllFields,
    clearErrors,
    value,
    setValue,
    isValid,
    error: {
      streetAddress: streetAddressError,
      postalCode: postalCodeError,
      city: cityError,
      state: stateError
    }
  };
};
