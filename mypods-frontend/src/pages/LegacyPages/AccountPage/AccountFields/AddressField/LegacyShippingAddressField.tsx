import React from 'react';
import { Grid } from '@mui/material';
import {
  CustomerAddress,
  UpdateShippingAddressRequest
} from '../../../../../networkRequests/responseEntities/CustomerEntities';
import {
  EditableAddressFieldProps,
  LegacyEditableAddressField
} from './LegacyEditableAddressField';
import {
  NEW_IDENTITY,
  SHIPPING_ADDRESS_TYPE,
  US_REGION_CODE
} from '../../../../../networkRequests/MyPodsConstants';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { useGtmEvents } from '../../../../../config/google/useGtmEvents';
import { GtmAccountDetailType } from '../../../../../config/google/GoogleEntities';
import {
  createGtmErrorRequest,
  GA_GENERIC_BACKEND_MESSAGE
} from '../../../../../config/google/googleAnalyticsUtils';
import { useLegacyGetCustomer } from '../../../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { useLegacyUpdateShippingAddress } from '../../../../../networkRequests/legacy/mutations/useLegacyUpdateShippingAddress';

export const LegacyShippingAddressField = () => {
  const { customer, updateCustomer } = useLegacyGetCustomer();
  const gtmEvents = useGtmEvents();
  const detailType: GtmAccountDetailType = 'address_shipping';
  const updateShippingAddress = useLegacyUpdateShippingAddress();

  const initializeAddress = (address: CustomerAddress | undefined): CustomerAddress => {
    if (address == null)
      return {
        id: NEW_IDENTITY,
        addressType: SHIPPING_ADDRESS_TYPE,
        regionCode: US_REGION_CODE, // never canada for now
        address1: '',
        postalCode: '',
        city: '',
        state: ''
      };
    return { ...address };
  };

  const getRequest = (updatedAddress: CustomerAddress): UpdateShippingAddressRequest => ({
    address: updatedAddress
  });

  const onSuccessfulUpdate = (request: UpdateShippingAddressRequest): void => {
    const updatedCustomer = { ...customer };
    if (request.address != null) updatedCustomer.shippingAddress = request.address;
    updateCustomer(updatedCustomer);
  };

  const update = (value: CustomerAddress, onSuccess: () => void, onError: () => void) => {
    const updateRequest = getRequest(value);
    gtmEvents.submitAccountDetail(detailType);
    updateShippingAddress.mutate(updateRequest, {
      onSuccess: (_, request) => {
        gtmEvents.successAccountDetail(detailType);
        onSuccessfulUpdate(request);
        onSuccess();
      },
      onError: () => {
        gtmEvents.errorEvent(
          createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, detailType, 'backend')
        );
        onError();
      }
    });
  };

  const getEditableFieldProps = (): EditableAddressFieldProps => ({
    currentAddress: customer.shippingAddress,
    initializedAddress: initializeAddress(customer.shippingAddress),
    update,
    isPendingUpdate: updateShippingAddress.isPending,
    labelKey: TranslationKeys.AccountPage.AddressInfo.Labels.SHIPPING_ADDRESS,
    gtmDetailType: detailType,
    successNotificationKey:
      TranslationKeys.CommonComponents.Notification.SHIPPING_ADDRESS_SAVE_SUCCEEDED
  });

  return (
    <Grid data-testid="shipping-address-field">
      <LegacyEditableAddressField {...getEditableFieldProps()} />
    </Grid>
  );
};
