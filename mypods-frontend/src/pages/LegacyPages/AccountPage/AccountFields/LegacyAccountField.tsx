import React from 'react';
import { Chip, Grid, Typography } from '@mui/material';
import { Design } from '../../../../helpers/Design';
import { Txt } from '../../../../components/Txt';
import { EditButton } from '../../../../components/buttons/EditButton';
import { WarningIcon } from '../../../../components/icons/WarningIcon';

// -- types --
interface Props {
  label: string;
  value: string;
  onEditClick?: () => void;
  isWarning?: boolean;
}

// -- impls --
export const LegacyAccountField = ({ label, value, onEditClick, isWarning }: Props) => {
  const styles = accountFieldStyles();
  return (
    <Grid container data-testid="account-field" {...styles.accountField}>
      <Grid item container {...styles.fieldLabel}>
        <Grid item flexGrow={1}>
          <Txt {...styles.fieldLabelText}>{label}</Txt>
        </Grid>
        {onEditClick && (
          <Grid item flexGrow={0}>
            <EditButton dataTestId="account" onClick={onEditClick} />
          </Grid>
        )}
      </Grid>
      <Grid container item {...styles.fieldValue}>
        {isWarning && (
          <Grid item {...styles.warning}>
            <WarningIcon />
          </Grid>
        )}
        <Grid item {...styles.fieldValueText}>
          <Typography>{value}</Typography>
        </Grid>
        {isWarning && (
          <Grid item {...styles.warning}>
            <Chip
              label="Warning"
              size="small"
              color="warning"
              sx={{ backgroundColor: '#FF7E38' }}
            />
          </Grid>
        )}
      </Grid>
    </Grid>
  );
};

const accountFieldStyles = () => ({
  accountField: {
    sx: {
      paddingY: Design.Primitives.Spacing.sm,
      borderBottom: `1px solid ${Design.Alias.Color.neutral300}`,
      gap: Design.Primitives.Spacing.xxs,
      flexDirection: 'column'
    }
  },
  fieldLabel: { sx: { flexDirection: 'row' } },
  fieldLabelText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      color: Design.Alias.Color.neutral700
    }
  },
  fieldValue: {
    maxWidth: 'inherit'
  },
  warning: {
    sx: { paddingRight: Design.Primitives.Spacing.xxs }
  },
  fieldValueText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Lg,
      color: Design.Alias.Color.accent900,
      overflowWrap: 'break-word',
      paddingRight: Design.Primitives.Spacing.xxs
    }
  }
});
