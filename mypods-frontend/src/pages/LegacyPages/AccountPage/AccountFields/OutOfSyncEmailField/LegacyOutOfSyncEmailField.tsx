import React, { useContext, useEffect, useState } from 'react';
import { FormControlLabel, Grid, Radio, RadioGroup, TextField, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { AccountFieldAlertProps, LegacyAccountFieldAlert } from '../LegacyAccountFieldAlert';
import { useGtmEvents } from '../../../../../config/google/useGtmEvents';
import { LegacyAccountField } from '../LegacyAccountField';
import { Design } from '../../../../../helpers/Design';
import { CancelButton } from '../../../../../components/buttons/CancelButton';
import { SaveButton } from '../../../../../components/buttons/SaveButton';
import { NotificationContext } from '../../../../../components/notifications/NotificationContext';
import { useLegacyEmailState } from '../useLegacyEmailState';
import { useLegacyGetCustomer } from '../../../../../networkRequests/legacy/queries/useLegacyGetCustomer';

type RadioChoices = 'OKTA_USERNAME' | 'OTHER';
const Tx = TranslationKeys.AccountPage.AccountInfo.Email;

export const LegacyOutOfSyncEmailField = () => {
  const gtmEvents = useGtmEvents();
  const { t: translate } = useTranslation();
  const { customer } = useLegacyGetCustomer();
  const { editableFieldProps } = useLegacyEmailState();
  const { setNotification } = useContext(NotificationContext);

  const [value, setValue] = useState<string>(editableFieldProps.displayValue);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [error, setError] = useState<string | undefined>();
  const [inputGroupAlert, setInputGroupAlert] = useState<AccountFieldAlertProps | undefined>();
  const [radioChoice, setRadioChoice] = useState<RadioChoices>('OKTA_USERNAME');
  const label = translate(editableFieldProps.labelKey);

  useEffect(() => {
    setError(undefined);
  }, [isEditing]);

  const inputIsInvalidOrUnchanged = () => {
    if (value === editableFieldProps.displayValue) return true;
    return !editableFieldProps.isInputValid(value);
  };

  const radioIsOktaUsername = () => radioChoice === 'OKTA_USERNAME';

  const setErrorIfInvalid = () => {
    if (!editableFieldProps.isInputValid(value)) {
      setError(translate(editableFieldProps.validationErrorKey));
    } else {
      setError(undefined);
    }
  };

  const handleValueChange = (newValue: string) => {
    setValue(newValue);
    setError(undefined);
    setInputGroupAlert(undefined);
  };

  const handleCancel = () => {
    setValue(editableFieldProps.displayValue);
    setIsEditing(false);
    setError(undefined);
    setInputGroupAlert(undefined);
  };

  const handleEditClick = () => {
    gtmEvents.startEditAccountDetail(editableFieldProps.gtmDetailType);
    setIsEditing(true);
  };

  const handleServerError = (errorHelperText: string) => {
    if (radioIsOktaUsername()) {
      setInputGroupAlert({
        title: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
        message: errorHelperText
      });
      return;
    }
    setError(errorHelperText);
  };

  const handleServerAlert = (alertKey: AccountFieldAlertProps) => {
    setInputGroupAlert({
      title: alertKey.title,
      message: alertKey.message
    });
  };

  const onSuccess = () => {
    setNotification({
      message: translate(editableFieldProps.successNotificationKey)
    });
    setIsEditing(false);
  };

  const onError = (errorHelperText?: string, alertProps?: AccountFieldAlertProps) => {
    if (errorHelperText) handleServerError(errorHelperText);
    if (alertProps) handleServerAlert(alertProps);
    if (!alertProps && !errorHelperText) {
      setNotification({
        message: translate(TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE),
        isError: true
      });
    }
  };

  const handleSave = () => {
    setErrorIfInvalid();
    if (radioIsOktaUsername() && customer.username != null) {
      editableFieldProps.update(customer.username!, onSuccess, onError);
    } else {
      if (inputIsInvalidOrUnchanged()) return;
      editableFieldProps.update(value, onSuccess, onError);
    }
  };

  return (
    <Grid data-testid="out-of-sync-email-field">
      {!isEditing ? (
        <LegacyAccountField
          label={label}
          value={editableFieldProps.displayValue}
          onEditClick={handleEditClick}
          isWarning
        />
      ) : (
        <Grid
          container
          flexDirection="column"
          gap={Design.Primitives.Spacing.xxs}
          sx={{
            paddingY: Design.Primitives.Spacing.sm,
            borderBottom: `1px solid ${Design.Alias.Color.neutral300}`
          }}>
          <Grid container flexDirection="column" gap={Design.Primitives.Spacing.xxs}>
            <Grid container flexDirection="row" justifyContent="space-between">
              <Typography
                sx={{
                  ...Design.Alias.Text.BodyUniversal.Md,
                  color: Design.Alias.Color.neutral700
                }}>
                {label}
              </Typography>
            </Grid>
            <Grid container flexDirection="row" justifyContent="space-between">
              <Typography sx={{ ...Design.Alias.Text.BodyUniversal.Lg }}>
                {editableFieldProps.displayValue}
              </Typography>
            </Grid>
            <Grid container flexDirection="row" justifyContent="space-between">
              <Typography
                sx={{
                  ...Design.Alias.Text.BodyUniversal.Md,
                  color: Design.Alias.Color.neutral700
                }}>
                {translate(Tx.OutOfSync.LABEL)}
              </Typography>
            </Grid>
            {inputGroupAlert && (
              <LegacyAccountFieldAlert
                title={inputGroupAlert.title}
                message={inputGroupAlert.message}
              />
            )}
            <RadioGroup
              value={radioChoice}
              onChange={(event) => setRadioChoice(event.target.value as any)}
              {...styles.emailRadioGroup}>
              <FormControlLabel
                value="OKTA_USERNAME"
                control={<Radio color="secondary" />}
                label={customer.username}
              />
              <FormControlLabel
                value="OTHER"
                control={<Radio color="secondary" />}
                label={translate(Tx.OutOfSync.OTHER)}
              />
            </RadioGroup>
            {radioChoice === 'OTHER' && (
              <TextField
                autoFocus
                fullWidth
                color="secondary"
                inputRef={(input) => input && input.focus()}
                label={label}
                value={value}
                onBlur={setErrorIfInvalid}
                helperText={error}
                error={!!error}
                onChange={(event) => handleValueChange(event.target.value)}
                onKeyDown={(event) => event.key === 'Enter' && handleSave()}
                sx={{ marginBottom: Design.Primitives.Spacing.xxs }}
                {...{ type: 'email' }}
              />
            )}
          </Grid>
          <Grid container columnGap={Design.Primitives.Spacing.xxs} justifyContent="flex-end">
            <CancelButton onClick={handleCancel} disabled={editableFieldProps.isPendingUpdate} />
            <SaveButton
              onClick={handleSave}
              isLoading={editableFieldProps.isPendingUpdate}
              disabled={!radioIsOktaUsername() && inputIsInvalidOrUnchanged()}
            />
          </Grid>
        </Grid>
      )}
    </Grid>
  );
};

const styles = {
  emailRadioGroup: {
    sx: {
      paddingLeft: '9px'
    }
  }
};
