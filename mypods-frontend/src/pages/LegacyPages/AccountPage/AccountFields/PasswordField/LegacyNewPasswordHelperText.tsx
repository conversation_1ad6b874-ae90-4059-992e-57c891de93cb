import React from 'react';
import List from '@mui/material/List';
import { useTranslation } from 'react-i18next';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import { PasswordValidationRule } from '../../../../../helpers/validation/Validators';
import { Txt } from '../../../../../components/Txt';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { Design } from '../../../../../helpers/Design';

// -- types --
interface Props {
  rules: PasswordValidationRule[];
}

// -- impls --

export const LegacyNewPasswordHelperText: React.FC<Props> = ({ rules }: Props) => {
  const { t: translate } = useTranslation();

  const styles = helperTextStyles();

  return (
    <>
      <Txt
        i18nKey={TranslationKeys.CommonComponents.Input.Error.Validation.BASE}
        {...styles.title}
      />
      <List disablePadding {...styles.list}>
        {rules.map((rule) => {
          if (rule.validity !== 'invalid') {
            return;
          }

          return (
            <ListItem
              disableGutters
              disablePadding
              {...styles.listItem}
              data-testid="error-message"
              key={rule.errorI18nKey}>
              <ListItemText {...styles.listItemText}>{translate(rule.errorI18nKey)}</ListItemText>
            </ListItem>
          );
        })}
      </List>
    </>
  );
};

const helperTextStyles = () => ({
  title: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Primitives.Color.Semantic.error,
      paddingTop: '3px',
      paddingX: '14px'
    }
  },
  list: { sx: { paddingLeft: Design.Primitives.Spacing.sm } },
  listItem: {
    sx: {
      color: Design.Primitives.Color.Semantic.error,
      '.MuiListItemText-root': {
        marginTop: 0,
        marginBottom: 0
      }
    }
  },
  listItemText: {
    sx: {
      paddingLeft: '6px',
      '&.MuiListItemText-root > .MuiTypography-root': {
        ...Design.Alias.Text.BodyUniversal.Xs,
        color: Design.Primitives.Color.Semantic.error
      }
    }
  }
});
