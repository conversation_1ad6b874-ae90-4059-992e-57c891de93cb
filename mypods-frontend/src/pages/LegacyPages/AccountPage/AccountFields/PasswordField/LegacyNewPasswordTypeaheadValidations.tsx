import React from 'react';
import List from '@mui/material/List';
import { Grid } from '@mui/material';
import { PasswordValidationRule } from '../../../../../helpers/validation/Validators';
import { Txt } from '../../../../../components/Txt';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { Design } from '../../../../../helpers/Design';
import { LegacyPasswordValidationItem } from './LegacyPasswordValidationItem';

// -- types --
interface Props {
  rules: PasswordValidationRule[];
}

const Tx = TranslationKeys.AccountPage.AccountInfo.Password;

// -- impls --
export const LegacyNewPasswordTypeaheadValidations: React.FC<Props> = ({ rules }: Props) => (
  <Grid>
    <Txt i18nKey={Tx.VALIDATION_HEADER} sx={{ ...Design.Alias.Text.BodyUniversal.Sm }} />
    <List dense sx={{ paddingLeft: '1rem' }}>
      {rules.map((rule) => (
        <LegacyPasswordValidationItem
          validity={rule.validity}
          i18nKey={rule.i18nKey}
          key={rule.i18nKey}
        />
      ))}
    </List>
  </Grid>
);

export class NewPasswordHelperText {}
