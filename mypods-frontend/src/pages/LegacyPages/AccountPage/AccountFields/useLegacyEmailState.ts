import { AxiosError } from 'axios';
import { useTranslation } from 'react-i18next';
import { EditableAccountFieldProps } from '../../../AccountPage/AccountFields/EditableAccountField/EditableAccountField';
import { isValidEmail } from '../../../../helpers/validation/Validators';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { AccountFieldAlertProps } from './LegacyAccountFieldAlert';
import { ErrorResponse } from '../../../../networkRequests/responseEntities/ErrorEntities';
import {
  Email,
  UpdateEmailErrorStatus,
  UpdateEmailRequest
} from '../../../../networkRequests/responseEntities/CustomerEntities';
import { GtmAccountDetailType } from '../../../../config/google/GoogleEntities';
import { NEW_IDENTITY } from '../../../../networkRequests/MyPodsConstants';
import { useGtmEvents } from '../../../../config/google/useGtmEvents';
import { useLegacyGetCustomer } from '../../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { useLegacyUpdateEmail } from '../../../../networkRequests/legacy/mutations/useLegacyUpdateEmail';

const Tx = TranslationKeys.AccountPage.AccountInfo.Email;

export const useLegacyEmailState = () => {
  const gtmEvents = useGtmEvents();
  const { customer, updateCustomer } = useLegacyGetCustomer();
  const updateEmail = useLegacyUpdateEmail();
  const { t: translate } = useTranslation();
  const detailType: GtmAccountDetailType = 'email';

  const getUpdateEmailRequest = (updateValue: string): UpdateEmailRequest => {
    const updatedEmail: Email = {
      id: customer.email?.id ?? NEW_IDENTITY,
      address: updateValue
    };
    return { email: updatedEmail };
  };

  const displayValue = () => customer.email?.address ?? 'N/A';

  const onSuccessfulUpdate = (request: UpdateEmailRequest): void => {
    if (request.email != null)
      updateCustomer({ email: request.email, username: request.email.address });
  };

  const update = (
    value: string,
    onSuccess: () => void,
    onError: (errorTranslationKey?: string, alertKey?: AccountFieldAlertProps) => void
  ) => {
    const updateRequest = getUpdateEmailRequest(value);
    gtmEvents.startEditAccountDetail(detailType);
    updateEmail.mutate(updateRequest, {
      onSuccess: (_, request) => {
        gtmEvents.successAccountDetail(detailType);
        onSuccessfulUpdate(request);
        onSuccess();
      },
      onError: (error: unknown) => {
        if (error instanceof AxiosError && error.response?.data?.status) {
          const errorResponse = error.response.data as ErrorResponse;
          const status = errorResponse.status as UpdateEmailErrorStatus;
          switch (status) {
            case UpdateEmailErrorStatus.EMAIL_ALREADY_IN_USE:
            case UpdateEmailErrorStatus.INVALID_EMAIL: {
              onError(translate(Tx.HelperText[status]), undefined);
              break;
            }

            case UpdateEmailErrorStatus.ACCOUNT_UNDER_MAINTENANCE:
            case UpdateEmailErrorStatus.ERROR:
            case UpdateEmailErrorStatus.NO_ACCOUNT_FOUND:
            case UpdateEmailErrorStatus.TOKEN_EXPIRED:
            case UpdateEmailErrorStatus.TOKEN_INVALID: {
              onError(undefined, {
                title: translate(Tx.Notifications.Title[status]),
                message: translate(Tx.Notifications.Message[status])
              });
              break;
            }
            default: {
              // The Page-Level alert is rendered on the EditableAccountField
              onError();
            }
          }
        } else {
          onError();
        }
      }
    });
  };

  const getEditableFieldProps = (): EditableAccountFieldProps => ({
    displayValue: displayValue(),
    isInputValid: isValidEmail,
    update,
    isPendingUpdate: updateEmail.isPending,
    labelKey: TranslationKeys.AccountPage.AccountInfo.Email.LABEL,
    gtmDetailType: detailType,
    validationErrorKey: TranslationKeys.CommonComponents.Input.Error.INVALID_EMAIL,
    successNotificationKey: TranslationKeys.CommonComponents.Notification.EMAIL_SAVE_SUCCEEDED,
    textFieldOverrides: {
      type: 'email'
    }
  });

  return {
    editableFieldProps: getEditableFieldProps()
  };
};
