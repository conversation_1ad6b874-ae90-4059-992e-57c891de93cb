import { Grid } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../../../locales/TranslationKeys';
import { LegacySecurityQuestionAnswerField } from './LegacySecurityQuestionAnswerField';
import { UpdatePinRequest } from '../../../../../networkRequests/responseEntities/CustomerEntities';
import { isValidPin } from '../../../../../helpers/validation/Validators';
import { EditablePinFieldProps, LegacyEditablePinField } from '../PinField/LegacyEditablePinField';
import { useLegacyGetCustomer } from '../../../../../networkRequests/legacy/queries/useLegacyGetCustomer';

export const LegacySupportSection = () => {
  const { t: translate } = useTranslation();
  const { customer } = useLegacyGetCustomer();

  function getUpdatePinRequest(updateValue: string): UpdatePinRequest {
    return { pin: updateValue, securityAnswer: customer.securityQuestionAnswer?.answer ?? '' };
  }

  const getEditableFieldProps = (): EditablePinFieldProps => ({
    displayValue: '****',
    getUpdatePinRequest,
    isInputValid: isValidPin,
    labelKey: TranslationKeys.AccountPage.SupportInfo.Labels.SUPPORT_PIN,
    successNotificationKey: TranslationKeys.CommonComponents.Notification.PIN_SAVE_SUCCEEDED
  });

  return (
    <Grid data-testid="support-section">
      <LegacyEditablePinField {...getEditableFieldProps()} />
      <LegacySecurityQuestionAnswerField
        questionLabel={translate(TranslationKeys.AccountPage.SupportInfo.Labels.SECURITY_QUESTION)}
        answerLabel={translate(TranslationKeys.AccountPage.SupportInfo.Labels.SECURITY_ANSWER)}
        questionValue={customer.securityQuestionAnswer?.question ?? 'N/A'}
        answerValue={customer.securityQuestionAnswer?.answer ?? 'N/A'}
      />
    </Grid>
  );
};
