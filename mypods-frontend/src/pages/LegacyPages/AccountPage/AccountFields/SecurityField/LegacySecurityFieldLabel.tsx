import { Grid } from '@mui/material';
import React from 'react';
import { Txt } from '../../../../../components/Txt';
import { Design } from '../../../../../helpers/Design';

export const LegacySecurityFieldLabel = (props: { label: string }) => (
  <Grid item flexGrow={1}>
    <Txt style={Design.Alias.Text.BodyUniversal.Md} sx={{ color: Design.Alias.Color.neutral700 }}>
      {props.label}
    </Txt>
  </Grid>
);
