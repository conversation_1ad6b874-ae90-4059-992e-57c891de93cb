import React from 'react';
import { customerEmailOutOfSync } from '../../../../networkRequests/responseEntities/CustomerEntities';
import { LegacyOutOfSyncEmailField } from './OutOfSyncEmailField/LegacyOutOfSyncEmailField';
import { useLegacyGetCustomer } from '../../../../networkRequests/legacy/queries/useLegacyGetCustomer';
import { LegacyEditableEmailField } from './EditableAccountField/LegacyEditableEmailField';

export const LegacyEmailField = () => {
  const { customer } = useLegacyGetCustomer();
  return customerEmailOutOfSync(customer) ? (
    <LegacyOutOfSyncEmailField />
  ) : (
    <LegacyEditableEmailField />
  );
};
