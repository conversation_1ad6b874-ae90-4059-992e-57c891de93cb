import { Grid, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { Design } from '../../../helpers/Design';
import { formatFullName } from '../../../networkRequests/responseEntities/CustomerEntities';
import { theme } from '../../../PodsTheme';
import { useLegacyGetCustomer } from '../../../networkRequests/legacy/queries/useLegacyGetCustomer';

export const LegacyCustomerNameSection = () => {
  const { customer } = useLegacyGetCustomer();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = customerNameSectionStyles(isMobile);

  const getInitials = () =>
    customer.firstName.charAt(0).toUpperCase() + customer.lastName.charAt(0).toUpperCase();
  return (
    <Grid container {...styles.customerNameSection}>
      <Grid item container {...styles.initialsCircle}>
        <Typography {...styles.initials}>{getInitials()}</Typography>
      </Grid>
      <Grid item {...styles.nameHeader}>
        {formatFullName(customer)}
      </Grid>
      <Grid item {...styles.customerId}>
        Customer ID #{customer.id}
      </Grid>
    </Grid>
  );
};

const customerNameSectionStyles = (isMobile: boolean) => ({
  customerNameSection: {
    sx: {
      padding: `${Design.Primitives.Spacing.sm} 0`,
      marginBottom: Design.Primitives.Spacing.md,
      gap: '8px',
      alignItems: 'center',
      flexDirection: 'column'
    }
  },
  initialsCircle: {
    sx: {
      width: '64px',
      height: '64px',
      padding: '12px',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: '50%',
      backgroundColor: Design.Alias.Color.infoLight
    }
  },
  initials: {
    sx: {
      ...Design.Alias.Text.Heading.Desktop.Lg,
      color: Design.Primitives.Color.NeutralDark.midnight
    }
  },
  nameHeader: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Md : Design.Alias.Text.Heading.Desktop.Lg),
      color: Design.Primitives.Color.NeutralDark.midnight
    }
  },
  customerId: {
    sx: {
      ...(isMobile ? Design.Alias.Text.BodyUniversal.Md : Design.Alias.Text.BodyUniversal.Lg),
      color: Design.Primitives.Color.NeutralDark.midnight
    }
  }
});
