import React from 'react';
import { Grid } from '@mui/material';
import { LegacyEditablePasswordField } from './AccountFields/PasswordField/LegacyEditablePasswordField';
import { LegacyEmailField } from './AccountFields/LegacyEmailField';
import { useLegacyGetCustomer } from '../../../networkRequests/legacy/queries/useLegacyGetCustomer';

export const LegacyAccountInfoSection: React.FC = () => {
  const { customer } = useLegacyGetCustomer();

  return (
    <Grid data-testid="account-info-section">
      <LegacyEmailField />
      {customer.isConverted && customer.email && (
        <LegacyEditablePasswordField displayValue="********" customerEmail={customer.email} />
      )}
    </Grid>
  );
};
