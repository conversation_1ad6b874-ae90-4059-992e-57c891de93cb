import { Grid, useMediaQuery } from '@mui/material';
import React, { useEffect, useState } from 'react';
import {
  BraintreeError,
  client,
  HostedFields,
  hostedFields,
  HostedFieldsEvent,
  HostedFieldsTokenizePayload
} from 'braintree-web';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { BraintreeFormField } from './BraintreeFormField';
import { ENV_VARS } from '../../../environment';
import { Design } from '../../../helpers/Design';
import { theme } from '../../../PodsTheme';
import { PaymentMethodPayload } from './PaymentMethodPayload';

// -- constants --
const Tx = TranslationKeys.ManagePaymentMethodsPage;

// -- types --

export interface PaymentErrorResponse {
  error: string;
  reasons: ErrorReason[];
}

export interface ErrorReason {
  code: string;
  message: string;
}

export interface BraintreeErrorDetails {
  invalidFieldKeys: BraintreeFormFields[];
  invalidFields: any[];
}

type BraintreeFormFields = 'cardholderName' | 'number' | 'expirationDate' | 'cvv';
type Config = {
  id: string;
  labelTranslationKey: string;
  placeholderTranslationKey: string;
  errorMessageTranslationKey: string;
};

const BRAINTREE_FORM_FIELDS_CONFIG: Record<BraintreeFormFields, Config> = {
  cardholderName: {
    id: 'cardholder-name-container',
    labelTranslationKey: Tx.PaymentInfo.CardholderName.LABEL,
    placeholderTranslationKey: Tx.PaymentInfo.CardholderName.PLACEHOLDER,
    errorMessageTranslationKey: Tx.Errors.CARDHOLDER_NAME_INVALID
  },
  number: {
    id: 'card-number',
    labelTranslationKey: Tx.PaymentInfo.CardNumber.LABEL,
    placeholderTranslationKey: Tx.PaymentInfo.CardNumber.PLACEHOLDER,
    errorMessageTranslationKey: Tx.Errors.CREDIT_CARD_INVALID
  },
  expirationDate: {
    id: 'expiration-date',
    labelTranslationKey: Tx.PaymentInfo.ExpirationDate.LABEL,
    placeholderTranslationKey: Tx.PaymentInfo.ExpirationDate.PLACEHOLDER,
    errorMessageTranslationKey: Tx.Errors.EXPIRATION_INVALID
  },
  cvv: {
    id: 'cvv',
    labelTranslationKey: Tx.PaymentInfo.Cvv.LABEL,
    placeholderTranslationKey: Tx.PaymentInfo.Cvv.PLACEHOLDER,
    errorMessageTranslationKey: Tx.Errors.CVV_INVALID
  }
};

type BraintreeFormErrorMap = {
  [key in BraintreeFormFields]: boolean;
};

export const paymentMethodPayload = (payload: HostedFieldsTokenizePayload) => ({
  cardholderName: payload.details.cardholderName,
  expirationMonth: payload.details.expirationMonth,
  expirationYear: payload.details.expirationYear,
  cardType: payload.details.cardType,
  nonceToken: payload.nonce,
  cardLastFourDigits: payload.details.lastFour,
  cardNumber: `${payload.details.bin}****${payload.details.lastFour}`
});

// -- impls --
interface Props {
  postalCode: string;
  onSuccess: (payload: PaymentMethodPayload) => void;
  onFailure: (response: PaymentErrorResponse) => void;
  formRef: React.Ref<HTMLFormElement> | null;
  setIsBraintreeFormComplete: (isBraintreeFormComplete: boolean) => void;
  onFocus: () => void;
}

export const BraintreeForm = ({
  postalCode,
  onSuccess,
  onFailure,
  formRef,
  setIsBraintreeFormComplete,
  onFocus
}: Props) => {
  const braintreeTokenizationKey = ENV_VARS.BRAINTREE_TOKENIZATION_KEY;
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = braintreeStyles(isMobile);
  const { t: translate } = useTranslation();

  // -- state --
  const [instance, setInstance] = useState<HostedFields>();
  const [formFieldErrors, setFormFieldErrors] = useState<BraintreeFormErrorMap>({
    cardholderName: false,
    cvv: false,
    expirationDate: false,
    number: false
  });
  const [cardType, setCardType] = useState('');

  // -- events --
  const onFormError = (error: BraintreeError): void => {
    let reasons = [
      {
        code: error.code,
        message: error.message
      }
    ];

    if (error.code === 'HOSTED_FIELDS_FIELDS_EMPTY') {
      reasons = [
        {
          code: error.code,
          message: translate(Tx.Errors.ALL_FIELDS_EMPTY)
        }
      ];
    }

    if (Array.isArray(error.details?.invalidFieldKeys)) {
      const invalidFields = error.details
        ?.invalidFieldKeys as BraintreeErrorDetails['invalidFieldKeys'];

      const nextFormErrors = {
        cardholderName: false,
        cvv: false,
        expirationDate: false,
        number: false
      };
      for (let i = 0; i < invalidFields.length; i++) {
        const fieldName = invalidFields[i];
        nextFormErrors[fieldName] = true;
      }

      setFormFieldErrors(nextFormErrors);
      reasons = invalidFields.map((fieldName) => ({
        code: error.code,
        message: translate(BRAINTREE_FORM_FIELDS_CONFIG[fieldName].errorMessageTranslationKey)
      }));
    }

    onFailure({
      error: error.message,
      reasons
    });
  };

  useEffect(() => {
    const init = async () => {
      try {
        const braintreeClient = await client.create({
          authorization: braintreeTokenizationKey
        });

        const hfInstance: HostedFields = await hostedFields.create({
          client: braintreeClient,
          styles: styles.braintree,
          fields: {
            cardholderName: {
              container: `#${BRAINTREE_FORM_FIELDS_CONFIG.cardholderName.id}`,
              placeholder: translate(
                BRAINTREE_FORM_FIELDS_CONFIG.cardholderName.placeholderTranslationKey
              )
            },
            number: {
              container: `#${BRAINTREE_FORM_FIELDS_CONFIG.number.id}`,
              placeholder: translate(BRAINTREE_FORM_FIELDS_CONFIG.number.placeholderTranslationKey)
            },
            expirationDate: {
              container: `#${BRAINTREE_FORM_FIELDS_CONFIG.expirationDate.id}`,
              placeholder: translate(
                BRAINTREE_FORM_FIELDS_CONFIG.expirationDate.placeholderTranslationKey
              )
            },
            cvv: {
              container: `#${BRAINTREE_FORM_FIELDS_CONFIG.cvv.id}`,
              placeholder: translate(BRAINTREE_FORM_FIELDS_CONFIG.cvv.placeholderTranslationKey)
            }
          }
        });
        hfInstance.on('empty', emptyEventHandler(setIsBraintreeFormComplete));
        hfInstance.on('notEmpty', notEmptyEventHandler(setIsBraintreeFormComplete));
        hfInstance.on('cardTypeChange', cardTypeEventHandler(setCardType));
        hfInstance.on('focus', onFocusEventHandler(setFormFieldErrors, onFocus));
        setInstance(hfInstance);
      } catch (error) {
        console.error(error);
        throw error;
      }
    };

    init();

    return () => {
      if (instance) {
        instance.teardown();
      }
    };
  }, []);

  const handleSubmit = async (e: { preventDefault: () => void }) => {
    e.preventDefault();

    if (instance === undefined) {
      throw new Error('Braintree instance not defined');
    }

    try {
      const payload = await instance.tokenize({ billingAddress: { postalCode } });
      onSuccess(paymentMethodPayload(payload));
    } catch (error: unknown) {
      if (isBraintreeError(error)) {
        onFormError(error);
      } else {
        throw error;
      }
    }
  };

  // -- view --
  return (
    <Grid data-testid="credit-card-fields">
      <form
        {...styles.paymentForm}
        id="payment-form"
        data-testid="payment-form"
        onSubmit={handleSubmit}
        ref={formRef}>
        <Grid container gap={Design.Primitives.Spacing.md}>
          <Grid {...styles.fullSize}>
            <BraintreeFormField
              {...BRAINTREE_FORM_FIELDS_CONFIG.cardholderName}
              isFieldInError={formFieldErrors.cardholderName}
            />
          </Grid>

          <Grid {...styles.fullSize}>
            <BraintreeFormField
              {...BRAINTREE_FORM_FIELDS_CONFIG.number}
              isFieldInError={formFieldErrors.number}
              extraStyle={cardType && styles.creditCardBackground(cardType)}
            />
          </Grid>

          <Grid {...styles.bottomGrid}>
            <Grid {...styles.fullSize}>
              <BraintreeFormField
                {...BRAINTREE_FORM_FIELDS_CONFIG.expirationDate}
                isFieldInError={formFieldErrors.expirationDate}
              />
            </Grid>
            <Grid {...styles.fullSize}>
              <BraintreeFormField
                {...BRAINTREE_FORM_FIELDS_CONFIG.cvv}
                isFieldInError={formFieldErrors.cvv}
              />
            </Grid>
          </Grid>
        </Grid>
      </form>
    </Grid>
  );
};

// -- helper functions --
function isBraintreeError(object: any): object is BraintreeError {
  // NB: there isn't a good way to verify the error.type property matches the Braintree union type `BraintreeErrorTypes`
  // best workaround I found is to copy the string enum values here -- Shavina
  const braintreeErrorTypes = ['CUSTOMER', 'MERCHANT', 'NETWORK', 'INTERNAL', 'UNKNOWN'];
  return (
    'code' in object &&
    'message' in object &&
    'type' in object &&
    braintreeErrorTypes.includes(object.type)
  );
}

export function emptyEventHandler(
  setIsBraintreeFormComplete: (isBraintreeFormComplete: boolean) => void
) {
  return () => {
    setIsBraintreeFormComplete(false);
  };
}

export function notEmptyEventHandler(
  setIsBraintreeFormComplete: (isBraintreeFormComplete: boolean) => void
) {
  return (event: HostedFieldsEvent) => {
    setIsBraintreeFormComplete(
      !event.fields.cardholderName.isEmpty &&
        !event.fields.number.isEmpty &&
        !event.fields.expirationDate.isEmpty &&
        !event.fields.cvv.isEmpty
    );
  };
}

export function cardTypeEventHandler(setCardType: (cardType: string) => void) {
  return (event: HostedFieldsEvent) => {
    if (event.cards.length === 1) {
      setCardType(event.cards[0].type);
    } else {
      setCardType('');
    }
  };
}

export function onFocusEventHandler(
  setFormFieldErrors: (errorMap: BraintreeFormErrorMap) => void,
  onFocus: Props['onFocus']
) {
  return (_event: HostedFieldsEvent) => {
    onFocus();
    setFormFieldErrors({
      cardholderName: false,
      cvv: false,
      expirationDate: false,
      number: false
    });
  };
}

// -- styles --
const braintreeStyles = (isMobile: boolean) => ({
  header: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Md : Design.Alias.Text.Heading.Desktop.Md),
      color: Design.Alias.Color.accent900,
      marginBottom: Design.Primitives.Spacing.sm
    }
  },
  loadingIndicator: {
    sx: {
      position: 'absolute',
      top: '0px',
      bottom: '0px',
      left: '0px',
      right: '0px'
    }
  },
  paymentForm: {
    style: {
      display: 'flex',
      // flexWrap: 'wrap',
      columnGap: Design.Primitives.Spacing.sm,
      rowGap: Design.Primitives.Spacing.xs
    }
  },
  bottomGrid: {
    sx: {
      display: 'flex',
      columnGap: Design.Primitives.Spacing.sm,
      rowGap: Design.Primitives.Spacing.md,
      flexWrap: isMobile ? 'wrap' : 'nowrap',
      width: '100%'
    }
  },
  fullSize: {
    sx: {
      width: '100%'
    }
  },
  creditCardBackground: (cardType: string | undefined) => ({
    style: {
      backgroundImage: `url(${ENV_VARS.RBF_ASSETS_BASE_URL}/creditCards/${cardType}.svg)`,
      backgroundSize: '40px',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: '98%'
    }
  }),
  braintree: {
    input: {
      'font-family': 'sans-serif',
      'font-size': '16px',
      'font-weight': 400,
      'letter-spacing': '0',
      'line-height': 1.5,
      'padding-left': '14px'
    }
  }
});
