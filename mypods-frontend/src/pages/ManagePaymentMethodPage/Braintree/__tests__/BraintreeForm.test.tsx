import { HostedFieldsEvent, HostedFieldsTokenizePayload } from 'braintree-web';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { vi } from 'vitest';
import React, { act } from 'react';
import {
  BraintreeForm,
  cardTypeEventHandler,
  emptyEventHandler,
  notEmptyEventHandler,
  onFocusEventHandler
} from '../BraintreeForm';
import { fireEvent, render, screen } from '@testing-library/react';
import { HostedFieldsHostedFieldsCard } from 'braintree-web/hosted-fields';

// -- constants --
const Tx = TranslationKeys.ManagePaymentMethodsPage;

// -- mocks --
const [mockClientCreate, mockHostedFieldsCreate] = vi.hoisted(() => [vi.fn(), vi.fn()]);
vi.mock('braintree-web', async () => ({
  ...(await vi.importActual('braintree-web')),
  client: {
    create: mockClientCreate
  },
  hostedFields: {
    create: mockHostedFieldsCreate
  }
}));

const tokenizedPayload: HostedFieldsTokenizePayload = {
  nonce: 'test-token',
  details: {
    bin: '11112',
    lastFour: '4444',
    lastTwo: '44',
    cardType: 'visa',
    cardholderName: 'test user',
    expirationMonth: '12',
    expirationYear: '2030'
  },
  type: 'CreditCard',
  description: 'test description'
};
const mockedTokenize = vi.fn().mockResolvedValue(tokenizedPayload);
const mockOnSuccess = vi.fn();
const mockOnFailure = vi.fn();

describe('BraintreeForm.tsx', () => {
  beforeEach(() => {
    mockClientCreate.mockResolvedValue({});
    mockHostedFieldsCreate.mockResolvedValue({ tokenize: mockedTokenize, on: vi.fn() });
    vi.clearAllMocks();
  });

  it('returns an error when credit card info is incomplete', async () => {
    const allFieldsEmptyError = {
      name: 'BraintreeError',
      code: 'HOSTED_FIELDS_FIELDS_EMPTY',
      message: 'All fields are empty. Cannot tokenize empty card fields.',
      type: 'CUSTOMER'
    };
    mockedTokenize.mockRejectedValue(allFieldsEmptyError);

    render(
      <BraintreeForm
        postalCode="11111"
        onSuccess={mockOnSuccess}
        onFailure={mockOnFailure}
        formRef={null}
        setIsBraintreeFormComplete={vi.fn()}
        onFocus={vi.fn()}
      />
    );
    await act(async () => {});

    fireEvent.submit(screen.getByTestId('payment-form'));
    await act(async () => {});

    expect(mockOnFailure).toHaveBeenCalledTimes(1);
    expect(mockOnFailure).toHaveBeenCalledWith({
      error: allFieldsEmptyError.message,
      reasons: [
        {
          code: allFieldsEmptyError.code,
          message: Tx.Errors.ALL_FIELDS_EMPTY
        }
      ]
    });
    expect(mockOnSuccess).not.toHaveBeenCalled();
  });

  it('returns multiple field level errors when there are multiple errors', async () => {
    const multipleFieldError = {
      name: 'BraintreeError',
      code: 'HOSTED_FIELDS_FIELDS_INVALID',
      details: {
        invalidFieldKeys: ['cardholderName', 'number', 'expirationDate', 'cvv'],
        invalidFields: [] // an array of the divs with given ids
      },
      message: 'Some payment input fields are invalid. Cannot tokenize invalid card fields.',
      type: 'CUSTOMER'
    };
    mockedTokenize.mockRejectedValue(multipleFieldError);

    render(
      <BraintreeForm
        postalCode="11111"
        onSuccess={mockOnSuccess}
        onFailure={mockOnFailure}
        formRef={null}
        setIsBraintreeFormComplete={vi.fn()}
        onFocus={vi.fn()}
      />
    );
    await act(async () => {});

    fireEvent.submit(screen.getByTestId('payment-form'));
    await act(async () => {});

    expect(mockOnFailure).toHaveBeenCalledTimes(1);
    expect(mockOnFailure).toHaveBeenCalledWith({
      error: multipleFieldError.message,
      reasons: [
        {
          code: multipleFieldError.code,
          message: Tx.Errors.CARDHOLDER_NAME_INVALID
        },
        {
          code: multipleFieldError.code,
          message: Tx.Errors.CREDIT_CARD_INVALID
        },
        {
          code: multipleFieldError.code,
          message: Tx.Errors.EXPIRATION_INVALID
        },
        {
          code: multipleFieldError.code,
          message: Tx.Errors.CVV_INVALID
        }
      ]
    });
    expect(mockOnSuccess).not.toHaveBeenCalled();

    // per-field error helper text
    expect(screen.getByText(Tx.Errors.CARDHOLDER_NAME_INVALID)).toBeInTheDocument();
    expect(screen.getByText(Tx.Errors.CREDIT_CARD_INVALID)).toBeInTheDocument();
    expect(screen.getByText(Tx.Errors.EXPIRATION_INVALID)).toBeInTheDocument();
    expect(screen.getByText(Tx.Errors.CVV_INVALID)).toBeInTheDocument();
  });
});

describe('Braintree event handlers', () => {
  it('should set braintree form as not complete when a field is empty', () => {
    const mockSetIsBraintreeFormComplete = vi.fn();

    emptyEventHandler(mockSetIsBraintreeFormComplete)();

    expect(mockSetIsBraintreeFormComplete).toHaveBeenCalledTimes(1);
    expect(mockSetIsBraintreeFormComplete).toHaveBeenCalledWith(false);
  });

  it('should set braintree form as complete when all fields are populated', () => {
    const mockSetIsBraintreeFormComplete = vi.fn();
    const notEmptyEvent: HostedFieldsEvent = {
      fields: {
        cardholderName: {
          isEmpty: false
        },
        number: {
          isEmpty: false
        },
        expirationDate: {
          isEmpty: false
        },
        cvv: {
          isEmpty: false
        }
      }
    } as HostedFieldsEvent;

    notEmptyEventHandler(mockSetIsBraintreeFormComplete)(notEmptyEvent);

    expect(mockSetIsBraintreeFormComplete).toHaveBeenCalledTimes(1);
    expect(mockSetIsBraintreeFormComplete).toHaveBeenCalledWith(true);
  });

  it('should clear payment errors and form field errors when the on focus event handler is fired', () => {
    const mockSetFormFieldErrors = vi.fn();
    let onFocusHandler = vi.fn();
    onFocusEventHandler(mockSetFormFieldErrors, onFocusHandler)({} as HostedFieldsEvent);

    expect(mockSetFormFieldErrors).toHaveBeenCalledTimes(1);
    expect(mockSetFormFieldErrors).toHaveBeenCalledWith({
      cardholderName: false,
      cvv: false,
      expirationDate: false,
      number: false
    });
    expect(onFocusHandler).toHaveBeenCalledWith();
  });

  test.each([
    {
      cardholderIsEmpty: true,
      numberIsEmpty: false,
      expirationDateIsEmpty: false,
      cvvIsEmpty: false
    },
    {
      cardholderIsEmpty: false,
      numberIsEmpty: true,
      expirationDateIsEmpty: false,
      cvvIsEmpty: false
    },
    {
      cardholderIsEmpty: false,
      numberIsEmpty: false,
      expirationDateIsEmpty: true,
      cvvIsEmpty: false
    },
    {
      cardholderIsEmpty: false,
      numberIsEmpty: false,
      expirationDateIsEmpty: false,
      cvvIsEmpty: true
    }
  ])('should set braintree form as not complete when any field is empty', (fieldStatus) => {
    const mockSetIsBraintreeFormComplete = vi.fn();
    const notEmptyEvent: HostedFieldsEvent = {
      fields: {
        cardholderName: {
          isEmpty: fieldStatus.cardholderIsEmpty
        },
        number: {
          isEmpty: fieldStatus.numberIsEmpty
        },
        expirationDate: {
          isEmpty: fieldStatus.expirationDateIsEmpty
        },
        cvv: {
          isEmpty: fieldStatus.cvvIsEmpty
        }
      }
    } as HostedFieldsEvent;

    notEmptyEventHandler(mockSetIsBraintreeFormComplete)(notEmptyEvent);

    expect(mockSetIsBraintreeFormComplete).toHaveBeenCalledTimes(1);
    expect(mockSetIsBraintreeFormComplete).toHaveBeenCalledWith(false);
  });

  it('should set card type when card type is detected', () => {
    const mockSetCardType = vi.fn();
    const mastercardEvent: HostedFieldsEvent = {
      cards: [{ type: 'mastercard' }]
    } as HostedFieldsEvent;

    cardTypeEventHandler(mockSetCardType)(mastercardEvent);

    expect(mockSetCardType).toHaveBeenCalledTimes(1);
    expect(mockSetCardType).toHaveBeenCalledWith('mastercard');
  });

  it('should set card type as empty when there is no card', () => {
    const mockSetCardType = vi.fn();
    const cardEvent: HostedFieldsEvent = {
      cards: [] as HostedFieldsHostedFieldsCard[]
    } as HostedFieldsEvent;

    cardTypeEventHandler(mockSetCardType)(cardEvent);

    expect(mockSetCardType).toHaveBeenCalledTimes(1);
    expect(mockSetCardType).toHaveBeenCalledWith('');
  });
});
