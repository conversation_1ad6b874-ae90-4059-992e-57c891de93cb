import React, { useEffect, useState } from 'react';
import { client, paypalCheckout } from 'braintree-web';
import { PayPalCheckout, PayPalCheckoutCreatePaymentOptions } from 'braintree-web/paypal-checkout';
import { Skeleton } from '@mui/material';
import { ENV_VARS } from '../../../environment';
import { PaymentCardType, PaymentMethodPayload } from './PaymentMethodPayload';

// -- types --
declare global {
  interface Window {
    paypal: {
      FUNDING: {
        PAYPAL: string;
      };
      Buttons: (options: { [key: string]: any }) => any;
    };
  }
}

interface Props {
  onSelect: () => void;
  onSuccess: (payload: PaymentMethodPayload) => void;
  onFailure: (response: any) => void;
  onCancel: () => void;
}

// -- constants --
export const PAYPAL_WINDOW_CLOSED_ERRORS = [
  'Window is closed, can not determine type',
  'Detected popup close'
];

// -- impls --
export const PaypalCheckout = ({ onSelect, onSuccess, onFailure, onCancel }: Props) => {
  const braintreeTokenizationKey = ENV_VARS.BRAINTREE_TOKENIZATION_KEY;
  const [isPaypalReady, setIsPaypalReady] = useState<boolean>(false);

  // -- state --
  const [instance, setInstance] = useState<PayPalCheckout>();

  // -- events --
  useEffect(() => {
    const init = async () => {
      try {
        const braintreeClient = await client.create({
          authorization: braintreeTokenizationKey
        });
        const paypalCheckoutInstance = await paypalCheckout.create({
          client: braintreeClient
        });
        paypalCheckoutInstance.loadPayPalSDK({ vault: true }).then((checkoutInstance) => {
          const paypal = window.paypal as Window['paypal'];
          setIsPaypalReady(true);
          paypal
            .Buttons({
              style: {
                layout: 'horizontal',
                height: 50,
                color: 'gold',
                shape: 'rect',
                label: 'paypal'
              },
              fundingSource: paypal.FUNDING.PAYPAL,
              onClick: onSelect,
              createBillingAgreement: () =>
                // @ts-ignore
                checkoutInstance.createPayment({
                  flow: 'vault',
                  enableShippingAddress: true,
                  shippingAddressEditable: false
                } as PayPalCheckoutCreatePaymentOptions),

              onApprove: async (data: any) => {
                try {
                  const payload = await checkoutInstance.tokenizePayment(data);
                  const cardType: PaymentCardType = 'PAYPAL';
                  const paypalPayload: PaymentMethodPayload = {
                    nonceToken: payload.nonce,
                    cardNumber: payload.details.email,
                    cardLastFourDigits: undefined,
                    cardType,
                    cardholderName: `${payload.details.firstName} ${payload.details.lastName}`,
                    expirationMonth: '12',
                    expirationYear: '2099'
                  };
                  onSuccess(paypalPayload);
                } catch (err: unknown) {
                  onFailure(getErrorMessage(err));
                }
              },

              onCancel: (_data: any) => {
                onCancel();
              },

              onError: (err: unknown) => {
                const errorMessage = getErrorMessage(err);
                if (PAYPAL_WINDOW_CLOSED_ERRORS.includes(errorMessage)) {
                  onCancel();
                } else {
                  onFailure(errorMessage);
                }
              }
            })
            .render('#paypal-button');

          setInstance(checkoutInstance);
        });
      } catch (error) {
        console.error(error);
        throw error;
      }
    };

    init();

    return () => {
      if (instance) {
        instance.teardown();
      }
    };
  }, []);

  // -- view --
  return (
    <>
      <div id="paypal-button" />
      {!isPaypalReady && <Skeleton variant="rectangular" width="auto" height={50} />}
    </>
  );
};

const getErrorMessage = (error: any): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return '';
};

/*
  This is a workaround to preload necessary scripts to load PaypalCheckout
  Because the PayPal button oftentimes takes a good few extra seconds to render in.
*/
export const preloadPaypal = () => (
  <div style={{ display: 'none' }}>
    <PaypalCheckout
      onCancel={() => {}}
      onFailure={() => {}}
      onSelect={() => {}}
      onSuccess={() => {}}
    />
  </div>
);
