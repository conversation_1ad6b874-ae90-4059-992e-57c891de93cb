// -- components --
import { InputBaseComponentProps, TextField, Typography } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';

type FormFieldProps = {
  id: string;
  labelTranslationKey: string;
  errorMessageTranslationKey: string;
  isFieldInError: boolean;
  extraStyle?: '' | { style: any };
};

export const BraintreeFormField = ({
  id,
  labelTranslationKey,
  errorMessageTranslationKey,
  isFieldInError,
  extraStyle
}: FormFieldProps) => {
  const styles = fieldStyles();
  const { t: translate } = useTranslation();
  return (
    <div>
      <Typography {...(isFieldInError ? styles.inError : {})}>
        {translate(labelTranslationKey)}
      </Typography>
      <TextField
        fullWidth
        error={isFieldInError}
        helperText={isFieldInError ? translate(errorMessageTranslationKey) : ''}
        InputProps={{
          inputComponent: CustomInputElement,
          inputProps: {
            component: BraintreeInput,
            id,
            isFieldInError
          }
        }}
        {...extraStyle}
      />
    </div>
  );
};

type InputElementProps = InputBaseComponentProps & {
  component: React.FunctionComponent<InputBaseComponentProps>;
};

const CustomInputElement = React.forwardRef<any, InputElementProps>((props, ref) => {
  const { component: Component, options, ...other } = props;
  const [mountNode, setMountNode] = React.useState<HTMLInputElement | null>(null);

  React.useImperativeHandle(
    ref,
    () => ({
      focus: () => {
        if (mountNode === null) {
          return;
        }
        mountNode.focus();
      }
    }),
    [mountNode]
  );

  // `Component` will be your `SomeThirdPartyComponent` from below
  return (
    <Component
      onReady={setMountNode}
      options={{
        ...options
      }}
      {...other}
    />
  );
});

interface BraintreeInputProps {
  id: string;
}

const BraintreeInput = ({ id }: BraintreeInputProps) => {
  const styles = fieldStyles();
  // @ts-ignore
  return <div id={id} {...styles.braintreeFormField}></div>;
};

// -- styles --
const fieldStyles = () => ({
  braintreeFormField: {
    style: {
      ...Design.Alias.Text.BodyUniversal.Md,
      boxSizing: 'content-box',
      width: '100%',
      height: '56px',
      position: 'relative',
      borderRadius: '4px'
    }
  },
  inError: {
    sx: {
      color: Design.Alias.Color.errorMain
    }
  }
});
