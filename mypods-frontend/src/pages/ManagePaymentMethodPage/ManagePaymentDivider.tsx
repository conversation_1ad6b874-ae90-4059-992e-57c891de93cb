import { Grid, Typography } from '@mui/material';
import Divider from '@mui/material/Divider';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../locales/TranslationKeys';

const Tx = TranslationKeys.ManagePaymentMethodsPage;
export const ManagePaymentDivider = () => {
  const { t: translate } = useTranslation();
  return (
    <Grid container display="flex" sx={{ margin: '1rem 0' }}>
      <Grid item flexGrow={1} alignSelf="center">
        <Divider />
      </Grid>
      <Grid item flexGrow={0} padding="0 1rem">
        <Typography textAlign="center">{translate(Tx.OR)}</Typography>
      </Grid>
      <Grid item flexGrow={1} alignSelf="center">
        <Divider />
      </Grid>
    </Grid>
  );
};
