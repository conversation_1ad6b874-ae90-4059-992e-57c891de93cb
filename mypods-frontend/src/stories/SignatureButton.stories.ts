import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { AddSignatureButtonCustomer } from '../components/buttons/AddSignature';
import { createCustomer } from '../testUtils/MyPodsFactories';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'PostBooking/SignatureButton',
  component: AddSignatureButtonCustomer,
  // parameters: {
  //   isSigned: false
  // },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // argTypes: {
  //   isSigned: boolean
  // },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: { handleSignClicked: fn() }
} satisfies Meta<typeof AddSignatureButtonCustomer>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {
  args: {
    isSigned: false,
    customer: createCustomer()
  }
};
