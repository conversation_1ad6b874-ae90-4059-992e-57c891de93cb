import type { Meta, StoryObj } from '@storybook/react';
import { PodsAlert, PodsAlertIcon, PodsAlertType } from '../components/alert/PodsAlert';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'PostBooking/PodsAlert',
  component: PodsAlert,
  tags: ['autodocs']
} satisfies Meta<typeof PodsAlert>;

export default meta;
type Story = StoryObj<typeof meta>;

export const InfoAlert: Story = {
  args: {
    title: 'This is an Info Alert',
    description: 'This is the description',
    icon: PodsAlertIcon.INFO,
    alertType: PodsAlertType.INFO
  }
};
export const ErrorAlert: Story = {
  args: {
    title: 'This is an Error Alert',
    description: 'This is the description',
    icon: PodsAlertIcon.ERROR,
    alertType: PodsAlertType.ERROR
  }
};
export const GrayAlert: Story = {
  args: {
    title: 'This is a Gray Alert',
    description: 'This is the description',
    icon: PodsAlertIcon.TRUCK,
    alertType: PodsAlertType.GRAY
  }
};
