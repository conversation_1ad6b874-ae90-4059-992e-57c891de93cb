export const SupportedLocales = {
  EN_US: 'en_us',
  FR_CA: 'fr_ca'
};
export const ALL_SUPPORTED_LOCALES = Object.values(SupportedLocales);

enum SitecoreLanguageCodes {
  EN = 'en',
  FR_CA = 'fr-CA'
}

export const sitecoreLanguageCodeToSupportedLocale = (
  sitecoreLanguageCode: string
): string | undefined => {
  switch (sitecoreLanguageCode) {
    case SitecoreLanguageCodes.EN:
      return SupportedLocales.EN_US;
    case SitecoreLanguageCodes.FR_CA:
      return SupportedLocales.FR_CA;
    default:
      return undefined;
  }
};

export const supportedLocaleToSitecoreLanguageCode = (locale: string): string => {
  switch (locale) {
    case SupportedLocales.FR_CA:
      return SitecoreLanguageCodes.FR_CA;
    case SupportedLocales.EN_US:
    default:
      return SitecoreLanguageCodes.EN;
  }
};
