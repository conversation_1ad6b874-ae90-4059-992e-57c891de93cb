{"commonComponents": {"editButton": "Edit", "saveButton": "Save", "cancelButton": "Cancel", "confirmCancelButton": "Confirm Cancel", "confirmButton": "Confirm", "continueButton": "Continue", "backButton": "Back", "nevermindButton": "Nevermind", "nextButton": "Next", "previousButton": "Previous", "yesButton": "Yes", "noButton": "No", "reviewButton": "Review", "acceptButton": "Accept", "progressCounter": "{{current}} of {{total}}", "percent": "{{amount}}%", "addSignature": {"buttonText": "Add your signature", "electronicallySigned": "Electronically signed by", "signedDateLabel": "Signed", "customerId": "Customer ID #{{customerId}}"}, "notification": {"billingAddressSaveSucceeded": "New Billing Address Saved", "emailSaveSucceeded": "New Email Address Saved", "newPasswordSaveSucceeded": "New Password Saved", "pinSaveSucceeded": "PIN successfully updated", "primaryPhoneNumberSaveSucceeded": "New Primary Phone Number Saved", "secondaryPhoneNumberSaveSucceeded": "New Secondary Phone Number Saved", "shippingAddressSaveSucceeded": "New Shipping Address Saved", "smsPreferencesSaveSucceeded": "Communication Preferences Saved", "passwordNotSaved": "Password Not Changed.", "containerVisitCanceled": "Container <PERSON><PERSON><PERSON> Canceled", "genericFailure": "Oops! Something went wrong. Please try again.", "paymentMethodAddedSucceeded": "New Payment Method Added", "paymentMethodAddFailed": "Failed to add new Payment Method.", "paymentMethodAddValidationError": "Card Not Added: Expiration Date Must Be At Least 30 Days In The Future", "billingAddressSaveFailed": "Failed to update Billing Address.", "signFnpsWaiverSucceeded": "Waiver(s) signed", "containerPlacementSucceeded": "Container Placement Saved", "fileNotReadyError": "File not ready, try again later.", "documentNotFound": "Document not found, try again later.", "invalidPassword": "Please enter a valid password."}, "input": {"error": {"invalidPhone": "Please enter a valid phone number", "invalidEmail": "Please enter a valid email address", "invalidPostalCode": "Please enter a valid postal code", "validation": {"base": "Almost there! It looks like you're missing:", "pin": "- 4 numbers", "password": {"length": "- 8 characters", "lowercase": "- 1 lower case letters (a-z)", "uppercase": "- 1 upper case letters (A-Z)", "specialCharacter": "- 1 special character (e.g. !@$%^&*)"}}, "required": "Required", "stateLength": "Please enter the two letter state abbreviation"}}, "banner": {"title_reviewDocuments": "You have new weight tickets available to review", "message_reviewDocuments": "Go to the Documents page to review", "title_onlineSchedulingUnavailable": "Online scheduling temporarily unavailable", "message_onlineSchedulingUnavailable": "Please call (************* to schedule or reschedule an appointment.", "ctaLabel_reviewDocuments": "Review Documents", "title_mothAgreements": "Submit your invasive moth and fly inspection", "message_mothAgreements": "Submit your mandatory inspection form at least 5 days before your move to secure your transit and avoid delays.", "ctaLabel_mothAgreements": "Start Inspection (5min)", "title_fnpsAgreements": "Submit your Fragile or Non-Paved Surface Waiver", "message_fnpsAgreements": "Please submit your waiver at least 3 days before your next delivery.", "ctaLabel_fnpsAgreements": "Sign Waiver", "title_desyncedEmails": "We could not update your login email", "message_desyncedEmails": "Your email was changed and we couldn't update your login email.", "ctaLabel_desyncedEmails": "Update Email"}}, "navigation": {"home": "Home", "billing": "Billing", "account": "Account", "supportPhoneNumberMobileLabel": "Support", "supportPhoneNumber": "(*************", "dropdown": {"logout": "Logout", "account": "Account", "documents": "Documents"}}, "accountPage": {"accountInfo": {"header": "Account", "email": {"label": "Email Address", "outOfSync": {"label": "Update your email to:", "other": "Other"}, "notifications": {"title": {"accountUnderMaintenance": "Account is under maintenance.", "noAccountFound": "Something went wrong.", "success": "Email updated.", "tokenExpired": "Request expired.", "tokenInvalid": "Invalid request.", "error": "Something went wrong."}, "message": {"accountUnderMaintenance": "Please try again later. If this continues to happen, please contact our Customer Care Team at (*************.", "noAccountFound": "Please log out and try again. If this continues to happen, please contact our Customer Care Team at (*************.", "success": "You will now log in and receive email notifications from your new email.", "tokenExpired": "Please try again. If this continues to happen, please contact our Customer Care Team at (*************.", "tokenInvalid": "Please log out and try again. If this continues to happen, please contact our Customer Care Team at (*************.", "error": "Please try again later. If this continues to happen, please contact our Customer Care Team at (*************."}}, "helperText": {"emailAlreadyInUse": "That email is already used on another account. Please try again with a different email.", "invalidEmail": "Please review the new email and try again."}, "otpVerification": {"dialog": {"title": "Confirm it's you", "subtitle": "Please enter the verification code we sent to ", "missingCode": "Didn't get the code?", "resendCodeCountdown": "Resend in {{amount}} seconds", "resendCodeButton": "Resend Code", "helperText": "That code didn't work.<br /> Enter the code again or request a new one."}}}, "password": {"helperText": {"invalidCredentials": "Password incorrect, please try again.", "newPasswordFailedRequirements": "Please review the password requirements and try again.", "newPasswordIsCommon": "We found your new password on a list of vulnerable passwords. Please try again with a more unique password.", "newPasswordIsReused": "Looks like you used that password too recently on this account. Please try again with a password you have not used on this account.", "newPasswordIsOldPassword": "The new password you entered was the same as your current password. Please try again with a new password.", "passwordFailedRequirements": "Please review the password requirements and try again."}, "notifications": {"title": {"accountNotConverted": "No Password Found.", "noAccountFound": "Account not found."}, "message": {"accountNotConverted": "This account doesn't have a password to update. Please log out and try again.", "noAccountFound": "We couldn't find an account matching that email. Please try again with a different email."}}, "labels": {"viewPassword": "Password", "currentPassword": "Current Password", "newPassword": "New Password"}, "validationHeader": "Your new password must contain at least: ", "rules": {"length": "At least 8 characters", "lowercase": "Lower case letters (a-z)", "uppercase": "Upper case letters (A-Z)", "specialCharacter": "Special characters (e.g. !@$%^&*)"}}}, "communication": {"header": "Communication", "smsPreferences": {"label": "Receive text messages", "description": "Receive messages and order updates from PODS team and drivers"}}, "contactInfo": {"header": "Phone Contact", "labels": {"primaryPhone": "Primary Phone Number", "secondaryPhone": "Secondary Phone Number"}}, "addressInfo": {"header": "Address", "labels": {"billingAddress": "Billing Address", "shippingAddress": "Shipping Address"}, "inputFields": {"address1": "Street Address", "address2": "Apt / Unit / Floor", "postalCode": "Postal Code", "city": "City", "state": "State"}}, "supportInfo": {"header": "Support", "labels": {"supportPin": "Support Pin", "securityQuestion": "Security Question", "securityAnswer": "Security Answer"}}}, "homePage": {"header": "Hey, {{fullName}}", "welcomeMessage": "Welcome to PODS", "skeletonLoader": {"header": "Something went wrong.", "subHeader": "Getting the latest data, so you can try again."}, "containerTile": {"containerHeader": {"containerId": "Container ID#: ", "containerType": "{{size}}ft Container", "noContainerId": "Not yet assigned"}, "showHistory": "Show Order History", "hideHistory": "Hide Order History", "showUpcomingActivity": "Show Upcoming Activity", "hideUpcomingActivity": "Hide Upcoming Activity", "orderDetails": {"status": "Ordered", "orderNumber": "Order Number"}, "serviceCountdown_default_zero": "Arriving today", "serviceCountdown_default_one": "Arriving tomorrow", "serviceCountdown_default_other": "Arriving in {{count}} days", "serviceCountdown_selfdelivery_zero": "Self-Delivery today", "serviceCountdown_selfdelivery_one": "Self-Delivery tomorrow", "serviceCountdown_selfdelivery_other": "Self-Delivery in {{count}} days", "serviceCountdown_selfpickup_zero": "Self-Pickup today", "serviceCountdown_selfpickup_one": "Self-Pickup tomorrow", "serviceCountdown_selfpickup_other": "Self-Pickup in {{count}} days", "serviceCountdownEtaBetween": "between {{eta}}", "serviceCountdownHelperStorageTomorrow": "Your container will be ready for you at the storage center.", "serviceCountdownHelperStorageToday": "Your container is ready for you at the storage center.", "serviceCountdownHelperWithEta": "Your driver will call you when they are on the way.", "serviceCountdownHelperWithoutEta_delivery": "Your delivery window will be available shortly. Your driver will call you when they are on the way.", "serviceCountdownHelperWithoutEta_pickup": "Your pickup window will be available shortly. Your driver will call you when they are on the way.", "serviceCountdownHelperWithoutEta_dropoff": "Your drop off window will be available shortly. Your driver will call you when they are on the way.", "serviceCountdownHelperWithoutEta_move": "Your move window will be available shortly. Your driver will call you when they are on the way.", "serviceCountdownHelperWithoutEta_return": "Your return window will be available shortly. Your driver will call you when they are on the way.", "serviceCountdownHelperWithoutEta_selfdelivery": "Your delivery window will be available shortly. Your driver will call you when they are on the way.", "serviceCountdownHelperWithoutEta_selfpickup": "Your pickup window will be available shortly. Your driver will call you when they are on the way.", "serviceCountdownHelper24Hour_delivery": "You can make changes up to 24 hours before your delivery", "serviceCountdownHelper24Hour_pickup": "You can make changes up to 24 hours before your pickup", "serviceCountdownHelper24Hour_dropoff": "You can make changes up to 24 hours before your drop off", "serviceCountdownHelper24Hour_move": "You can make changes up to 24 hours before your move", "serviceCountdownHelper24Hour_return": "You can make changes up to 24 hours before your return", "serviceCountdownHelper24Hour_selfdelivery": "You can make changes up to 24 hours before your delivery", "serviceCountdownHelper24Hour_selfpickup": "You can make changes up to 24 hours before your pickup"}, "moveLegs": {"title": {"initialDelivery_past": "Delivered", "initialDelivery_future": "Scheduled for Delivery", "redelivery_past": "Delivered", "redelivery_future": "Scheduled for Drop Off", "redelivery_unscheduled": "Schedule Drop Off", "pickup_past": "Picked Up", "pickup_future": "Scheduled for Pick Up", "pickup_unscheduled": "Schedule Pickup", "finalPickup_past": "Returned", "finalPickup_future": "Scheduled for Return", "finalPickup_unscheduled": "Schedule Return", "containerAtWarehouse": "Container at Storage Center", "containerAtWarehouseHistory": "Storage Center", "containerMoveLocal_past": "Moved", "containerMoveLocal_future": "Scheduled for Move", "containerMoveLocal_unscheduled": "Schedule Move", "warehouseToWarehouse_past": "Moved to {{newCity}}", "warehouseToWarehouse_future": "Scheduled for transport to {{newCity}}", "warehouseToWarehouse_unscheduled": "Schedule to new city", "selfInitialDelivery_past": "Delivered", "selfInitialDelivery_future": "Scheduled Storage Center Delivery", "selfFinalPickup_past": "Picked Up", "selfFinalPickup_future": "Scheduled Storage Center Pick Up", "selfFinalPickup_unscheduled": "Schedule Storage Center Pick Up", "inTransit": "In Transit", "callToSchedule": "Call to schedule", "callToReschedule": "Call to reschedule", "visitContainer": "Scheduled Container Visit", "chatButton": "Cha<PERSON>", "SMSButton": "Text {{number}}"}, "upcomingMoveLeg": {"descriptionGeneric": "You must schedule the previous step before you can schedule this step.", "descriptionPrefix": "You must schedule the previous step before you can schedule your ", "moveLegSuffix": {"finalPickUp": "return.", "redelivery": "drop off.", "selfInitialDelivery": "delivery."}}, "inTransitDescription": "Your container is in transit. Call us at <a href=\"tel:************\">(*************</a> to review or update its status.", "callToScheduleDescription": "Please call to schedule your delivery to your new city at <a href=\"tel:(*************\">(*************</a>", "callToScheduleWhenOnlineSchedulingDisabled": "To schedule your appointment please call <a href=\"tel:(*************.\">(*************</a>", "callForSpecialAssistance": "It looks like your order requires special assistance, please give us a call at <a href=\"tel:************\">(************* </a> and we'll be happy to help you with your scheduling needs.", "message_Unavailable_ToSchedule": "To schedule your appointment please call (*************.", "scheduling": {"scheduleUpdateSuccessful": "Update Succeeded", "scheduleVisitContainerSuccessful": "Container Visit Scheduled", "rescheduleVisitContainerSuccessful": "Container <PERSON><PERSON><PERSON> Rescheduled", "priceChangeTitle": "Price change due to date change", "priceChangeDescription_increase": "Please press confirm to lock-in your new date and accept a price <strong>increase</strong> of ${{amount}}.", "priceChangeDescription_decrease": "Please press confirm to lock-in your new date and accept a price <strong>decrease</strong> of ${{amount}}.", "serviceNotSameAreaTitle": "Address Too Far Away", "serviceNotSameAreaDescription": " It looks like this new address is outside this container's available delivery area. If you need to move your delivery further out, please call us at (*************.", "addressLabels": {"labelStorageCenter": "Storage Center Address", "labelDefault": "Address"}, "containerPlacementTitle": "Container Placement", "containerPlacementButton": "Container Placement", "containerPlacementSubtitle": "To ensure delivery and avoid cancellation fees, you'll need to confirm where your container will be placed.", "containerPlacementCompleted": "Container Placement Confirmed", "noWarehouseHoursFound": "Warehouse hours unable to be found.", "dateLabels": {"labelDelivery": "Delivery Date", "labelPickup": "Pickup Date", "labelArrival": "Arrival Date", "labelMove": "Move Date", "labelDropOff": "Drop Off Date", "labelReturn": "Return Date", "labelVisit": "Visit Date", "labelVisitWindow": "Visit Window", "labelPickupWindow": "Pickup Window", "labelDefault": "Date", "UnscheduledDate": "Not Scheduled"}, "pickupContainer": {"title": {"step1": "Manage pick up", "step2": "Confirm pick up"}, "subtitle": "You don't need to be present for your pickup, but the PODS container must be locked and accessible.", "detailsTitle": "Pick up details", "addressLabel": "Address", "addressValue": "1210 Verona Ct, Powder Springs, GA 30127", "dateLabel": "Date", "infoTitle": "Things to keep in mind", "info": {"info1": {"title": "Why are some dates unavailable?", "desc": "Some PODS delivery dates may be unavailable due to high demand, limited truck or driver availability in your area, the storage center is closed, or the date is too close to one of your other container moves."}, "info2": {"title": "How far in advance should I schedule a pickup?", "desc": "Generally we recommend scheduling at least 1 week before your pickup. If your planning a container move during peak moving season, spring through summer,  we recommend scheduling 2-3 weeks in advance."}}, "confirmButton": "Save", "nextButton": "Next", "loadingText": "We are on the move!"}, "visitContainer": {"title": {"step1": "Visit your container", "step2": "Confirm visit"}, "subtitle": "Let us know when you want to visit your container so we can make sure it is accessible for you.", "detailsTitle": "Storage center details", "addressLabel": "Storage Center", "addressValue": "3407 Florence Cir, Powder Springs, GA 30127", "dateLabel": "Pick a visit date", "infoTitle": "Things to keep in mind", "info": {"info1": {"title1": "Why are some dates unavailable?", "desc1": "Some dates may be unavailable due to storage center hours, holidays, or high demand. Please select an available date."}, "info2": {"title2": "How far in advance should I schedule a visit?", "desc2": "We recommend scheduling your visit at least 24 hours in advance to ensure access to your container."}}, "confirmButton": "Save", "nextButton": "Next", "loadingText": "We are on the move!"}}, "scheduleButton": "Schedule", "scheduleButton_pickup": "Schedule Pickup", "scheduleButton_redelivery": "Schedule Drop Off", "scheduleButton_visitContainer": "Schedule Container Visit", "scheduleButton_finalPickup": "Schedule Pickup", "scheduleButton_selfFinalPickup": "Schedule Pickup", "scheduleButton_selfInitialDelivery": "Schedule Delivery", "initialDeliveryReviewAlert": {"upperTitle": "Container placement", "title": "Review Container Placement Details", "subtitle": "Your container will be placed on your driveway with the door facing your home."}, "initialDeliveryPriceDifferenceAlert": {"title": "Price Change Due to Placement Changes", "subtitle_increase": "Please press confirm to lock-in your new date and accept a price <strong>increase</strong> of ${{amount}}.", "subtitle_decrease": "Please press confirm to lock-in your new date and accept a price <strong>decrease</strong> of ${{amount}}."}, "initialDeliverySaveDetails": {"title": "Saving your Placement Details", "subtitle": "This may take more then a minute."}}, "containerPlacement": {"pavedSurfaceScreen": {"title": "Does this address have a paved surface?", "subtitle": "Please note: Additional fees may apply for container placements on non-paved or fragile surfaces.", "alertTitle": "Here are some common surfaces types:", "alertDescription": "<strong>Paved:</strong> Concrete or asphalt.<br /><strong>Non-paved/fragile:</strong> Gravel, dirt, grass, sand, stones, shells, and brick pavers."}, "placementTips": {"title": "Before you decide please keep these tips in mind", "item1": "1. Avoid steep inclines", "item2": "2. Check around the ground for objects", "item3": "3. Clear a space 12ft wide and 15ft tall", "item4": "4. Look out for wires and branches", "item5": "5. Move your vehicle on delivery day", "item6": "6. Multiple containers need more room"}, "siteTypeScreen": {"title": "Where would you like to place the container?", "buttons": {"driveway": "Driveway", "street": "Street", "parkingLot": "Parking Lot"}, "prompts": {"driveway": {"siteType": "Select the image that most closely resembles your driveway location.", "containerLocation": "Where would you like us to place the container on your driveway?", "cabDirection": "What direction would you like the door to face?"}, "streetAlleyway": {"siteType": "Select the image that most closely resembles your street or alley location."}, "parkingLot": {"siteType": "Select the image that most closely resembles your parking lot location."}}}, "driverNotes": {"title": "Add driver instructions with placement details, gate codes, delivery entrances or other notes.", "subtitle": "Please be as detailed as you can and include information about gates, building access, pets, or anything else that may effect delivery.", "label": "Driver Instructions", "helperText": "{{count}} of {{total}} characters"}, "reviewScreen": {"title": "Let's review your details", "subtitle": "Check that everything looks right. If not, edit and update your container placement details.", "finishButton": "Finish", "labels": {"address": "Address", "placementSite": "Type of placement site", "containerPlacement": "Container Placement", "driverInstructions": "Driver Instructions"}}}, "splashScreen": {"acceptButton": "Get started", "title": "Everything You Need to Manage Your Move", "description": "Skip the call — schedule services, track your container, and handle everything from your MyPODS account.", "features": {"scheduling": {"title": "24/7 <PERSON><PERSON>uling", "description": "Make changes or book services anytime — no need to wait on hold or call during business hours."}, "onePlace": {"title": "Everything in One Place", "description": "Invoices, billing history, scheduled services, and documents like order confirmations are all accessible from your account."}, "integratedSupport": {"title": "Integrated Support", "description": "Access online chat if you need help — often faster than waiting on the phone."}}}, "sidebar": {"resourcesTitle": "Resources", "servicesTitle": "Need Moving Services?", "faqs": {"text": "FAQs", "url": "https://www.pods.com/faqs"}, "billingPayment": {"text": "Billing & Payment Resources", "url": "https://www.pods.com/faqs/pricing-and-billing"}, "deliveryChecklist": {"text": "Container Delivery Checklist", "url": "https://prodmypods.azureedge.net/-/media/my-pods/pods-container-delivery-checklist.pdf?rev=3e6a9dd43ede4f2abeede01cb1321912"}, "packingTips": {"text": "Packing Tips", "url": "https://www.youtube.com/watch?v=VWqaYNODImY"}, "packingDosDonts": {"text": "Packing a container: Do's & Don'ts", "url": "https://www.pods.com/blog/storage-mistakes"}, "carShipping": {"text": "Car Shipping", "url": "https://www.pods.com/resource-center/vehicle-transport"}, "packingHelp": {"text": "Packing and Loading Help", "url": "https://www.pods.com/packing-and-loading-assistance"}}}, "onboarding": {"rentalAgreements": {"header": "Accept your rental agreement to get started.", "subtitle": "To ensure your order gets delivered, you'll first need to review and accept your rental agreement.", "rentalAgreement_local": "Local Rental Agreement", "rentalAgreement_interFranchise": "Long Distance Rental Agreement", "declineError": "You must accept the rental agreement to use PODS."}, "signRentalAgreements": {"order_number": "Order #{{orderNumber}}", "header": "Rental Agreement", "subtitle": "Please agree to PODS’ terms of service before continuing to MyPODS. The terms of service govern your use of PODS' services, including storage, delivery, and billing.", "viewRentAgreement": "View rental agreement.", "linkFlowAgreementViewer": {"checkboxLabelStart": "By checking this box, you confirm that you have read and agree to the", "rentalAgreementLinkText": "PODS Rental Agreement", "checkboxLabelEndWithOrderId": "for Order #{{orderId}}"}}, "acceptButton_desktop": "Accept and Continue", "acceptButton": "Accept", "declineButton": "Decline", "returnToTaskButton": "Your Tasks"}, "billingPage": {"header": "Billing", "totalAccountBalance": "Account <PERSON><PERSON>", "totalAccountCredit": "Account Credit", "makePaymentLink": "Make payment", "invoice": "Invoice", "status": "Due on {{date}}", "loading": "Loading billing information", "processingPayment": "Processing Payment...", "upcomingPayments": {"header": "Unpaid Invoices"}, "paymentHistory": {"header": "Paid Invoices"}, "statements": {"header": "Statements", "statement": {"title": "Statement"}}, "paymentMethods": {"defaultPaymentMethod": {"header": "Payment Methods", "notFound": "No Primary Payment Method Found", "viewPaymentMethodsLink": "View payment methods"}}, "billingFaqCard": {"combinedTitle": "Billing & Statements", "billingTitle": "Billing", "lookingForMoreDetailsTitle": "Looking for more details on billing terms and conditions?", "lookingForMoreDetailsDesc": "Learn about holds, charges, refunds, and when payments are processed in our <a target=\"_blank\" rel=\"noopener\" href=\"https://www.pods.com/faqs/pricing-and-billing\">Billing FAQ</a>.", "statementInfoAlertTitle": "Looking for your statements?", "statementInfoAlertDesc": "We no longer auto-generate statements. If you need one, click the \"custom statement\" link above."}}, "customStatementPage": {"header": "Create Custom Statement", "subtitle": "Select your preferred dates from the calendars below.", "link": "Custom Statement", "button_default": "Create Statement", "button_loading": "Creating Statement", "datePickers": {"startDate": "Start Date", "endDate": "End Date"}, "generatedStatements": {"header": "Generated Statements", "empty": "No Generated Statements"}, "errorMessages": {"notFound": "No transactions found in that range.", "invalidDatesTitle": "Dates Out of Order", "invalidDatesDescription": "Your end date must be after your start date. Select a new end date and try again."}}, "makePaymentsPage": {"header": "Make a Payment", "subtitle": "Review your payment", "invoicePrefix": "Invoice #", "selectInvoice": {"subtitle": "Select all that apply.", "question": "Which invoice do you want to pay?", "questionSubtext": "All invoices are scheduled for auto-pay", "allInvoices": "All Invoices", "dueOnPrefix": "Due on "}, "totalLabel": "Total", "inputLabel": "Payment Amount", "paymentAmountDisclaimer": "*Partial payments are only available for single invoices", "selectLabel": "Payment Method", "selectMethodLoadingText": "Loading payment methods", "addPaymentMethodLink": "Add Payment Method", "submitButton": "Make Payment", "submitButtonSeven": "Payment Processing...", "submitButtonFourteen": "Updating account balance...", "radioOptions": {"fullAmount": "Full Amount", "customAmount": "Custom Amount"}, "selectOptionLabel": "Ending in {{lastFourDigits}} ", "notifications": {"success": "Payment successful"}, "error": {"exceedsTotalAmountDue": "You cannot pay more than your Current Balance", "declined": {"noBalanceRemaining": "Payment Declined: No Balance Remaining", "fraudSuspected": "Payment Declined: <PERSON><PERSON>ed", "cardClosed": "Payment Declined: Card Closed", "inactiveCard": "Payment Declined: Your card has not been activated. Please activate your card or use a different payment method.", "invalidAccount": "The account information provided is invalid. Please check your details or use a different payment method.", "insufficientFunds": "Payment Declined: Insufficient Funds", "processorDeclined": "Your payment was declined by the processor. Please try again or use a different payment method.", "cardIssuerDeclined": "Your card issuer has declined this transaction. Please contact your card issuer or use a different payment method.", "bankPaymentUnauthorised": "Payment Declined: Your payment was not authorized by your bank. Please contact your bank or use a different payment method.", "paypalAccountIssue": "Payment Declined: There was an issue with your PayPal account. Please check your PayPal settings or use a different payment method. ", "limitExceeded": "Payment Declined: <PERSON>it Exceeded", "declined": "Your payment could not be processed. Please try again or use a different payment method."}, "warning": {"title": "Problem Processing Payment", "body": "We were unable to process your payment for all invoices. Please review your account balance and try to make your payment again. If this continues, please contact our Customer Care team."}}}, "paymentMethodsPage": {"header": "Payment Methods", "addPaymentMethodButton": "Add payment method", "manageLoan": "Manage Loan", "makeDefault_default": "Make Default", "makeDefault_loading": "Saving", "infoIconText": "Since you have a moving loan, \nyou cannot change your default method", "default": "<PERSON><PERSON><PERSON>", "paymentType": {"personalLoan": "Personal Loan", "creditCard": "Credit Card", "paypal": "PayPal", "financing": "Financing", "moveLoanUpgradeText": "Moving Loan through Upgrade", "moveLoanCitiBankText": "Citi Pay® Credit"}, "callToChangeModal": {"title": "Give us a call", "body": "To change your payment method, talk to one of our Customer Service Representatives by calling (*************. After your payment method is switched, allow 24-48 hours for the change to be reflected in your MyPods account.", "buttons": {"callUs": "Call us", "close": "Close this window"}}, "messages": {"success": "Default Method Updated", "paymentMethodIssue": "Unable to set default payment method. Please check your payment method details."}}, "managePaymentMethodsPage": {"title": "Add new payment method", "cardFormTitle": "Credit Card Information", "billingAddressTitle": "Billing Address", "billingAddressCheckboxLabel": "Use my Billing Address", "billingAddressSubtitle": "*This will change the billing address for the entire account", "saveButton": "Save Payment Method", "otherWaysToPay": "Other ways to pay", "or": "OR", "paymentInfo": {"cardholderName": {"label": "Name on card", "placeholder": "Full name on card"}, "cardNumber": {"label": "Card number", "placeholder": "1234 1234 1234 1234"}, "expirationDate": {"label": "Expiration", "placeholder": "MM / YY"}, "cvv": {"label": "Security code", "placeholder": "CVC"}}, "noBillingAddressFound": "Billing Address not found", "goToAccountPage": "Click <url>here</url> to add a Billing Address to your account on the \"My Account\" page. ", "goToAccountPageLabel": "Go to account page", "errors": {"processingPayment": "There was an error processing your payment method.", "allFieldsEmpty": "All fields are empty", "cardholderNameInvalid": "Invalid cardholder name", "creditCardInvalid": "Invalid credit card number", "expirationInvalid": "Invalid expiration date", "cvvInvalid": "Invalid CVV", "addressInvalid": "Incorrect billing address.", "paypalClosedUnexpectedly": "Unable to save PayPal payment information."}}, "documentsPage": {"header": "Documents", "subtitle": "Here you'll find all your agreements, documents, and forms in one place.", "types": {"ACH_AUTOPAY_AUTHORIZATION_FORM": "Ach Autopay Authorization Form", "ANCILLARY_ITEM_DOCUMENTATION": "Ancillary Item Documentation", "AUCTION_PAPERWORK": "Auction Paperwork", "BAD_DEBT_PAPERWORK": "Bad Debt Paperwork", "BANKRUPTCY_PAPERWORK": "Bankruptcy Paperwork", "BOOKING_CONFIRMATION": "Booking Confirmation", "CBP_FORM_7533_INWARD_CARGO": "Cbp Form 7533 Inward <PERSON>go", "CREDIT_CARD_AUTHORIZATION": "Credit Card Authorization", "CREDIT_CHECK_FORM": "Credit Check Form", "CREDIT_CARD_CHARGEBACKS": "Credit Card Chargebacks", "CERTIFICATE_OF_INSURANCE": "Certificate Of Insurance", "RENTAL_AGREEMENT": "Rental Agreement", "IF_RENTAL_AGREEMENT": "Rental Agreement", "LOCAL_RENTAL_AGREEMENT": "Rental Agreement", "IF_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE": "Rental Agreement Electronic Acceptance", "LOCAL_RENTAL_AGREEMENT_ELECTRONIC_ACCEPTANCE": "Rental Agreement Electronic Acceptance", "CUSTOMER_INCIDENT_NOTE": "Customer Incident Note", "DAMAGE_WAIVERS_AND_RELEASES": "Damage Waivers And Releases", "DAMAGE_PICTURES": "Damage Pictures", "COPY_OF_DEATH_CERTIFICATE": "Copy Of Death Certificate", "DEED_OR_LEASE_VISITOR": "Deed Or Lease Visitor", "DEFAULT_ON_PAYMENT_LETTER": "Default On Payment Letter", "UNACCOMPANIED_ARTICLES": "Unaccompanied Articles", "ADD_A_GENERAL_FORM_TO_CAPTURE_OTHER_FORMS": "Add A General Form To Capture Other Forms", "SPONGY_MOTH_FORM": "Spongy <PERSON>th Form", "HOUSEHOLD_GOODS_INVENTORY_LIST": "Household Goods Inventory List", "IMAGE_FILE": "Image File", "INVENTORY_ACKNOWLEDGEMENT_OF_HAZARDOUS_MATERIALS": "Inventory Acknowledgement Of Hazardous Materials", "RETAIL_CUSTOMER_INVOICE": "Retail Customer Invoice", "LEGAL_CORRESPONDENCE": "Legal Correspondence", "LEGAL_STATUS": "Legal Status", "MASTER_RENTAL_AGREEMENT": "Master Rental Agreement", "CERTIFIED_NAME_CHANGE": "Certified Name Change", "FRAGILE_AND_NON_PAVED_SURFACE_WAIVER": "Fragile And Non Paved Surface Waiver", "GENERAL_NOTE_WITH_FILE_ATTACHMENT": "General Note With File Attachment", "GENERAL_NOTE_WITH_IMAGE_FILE_ATTACHMENT": "General Note With Image File Attachment", "OCEAN_TRANSPORT_QUOTE": "Ocean Transport Quote", "ORDER_CONFIRMATION": "Order Confirmation", "ORDER_UPDATE_CONFIRMATION": "Order Update Confirmation", "COPY_OF_PASSPORTS": "Copy Of Passports", "ATTACH_PDF_FILE": "Attach Pdf File", "FORM_B4A_PERSONAL_EFFECTS_ACCOUNTING": "Form B4a Personal Effects Accounting", "FORM_B4E_PERSONAL_EFFECTS_ACCOUNTING_DOCUMENT": "Form B4e Personal Effects Accounting Document", "MONTHLY_STATEMENT_OR_INVOICE": "Monthly Statement Or Invoice", "PLACEMENT_WAIVER": "Placement Waiver", "FORM_5291_CUSTOMS_POWER_OF_ATTORNEY": "Form 5291 Customs Power Of Attorney", "PODS_CANADA_CUSTOMS_FORM": "Pods Canada Customs Form", "PURPOSE_OF_THE_MOVE_AND_CONTACT_PHONE_NUMBER_DOCUMENT": "Purpose Of The Move And Contact Phone Number Document", "SETTLEMENT_AGREEMENT": "Settlement Agreement", "SPOTTED_LANTERN_FLY_FORM": "Spotted Lantern Fly Form", "FORM_II_RC_159_SUPPLEMENTAL_DECLARATION": "Form II RC 159 Supplemental Declaration", "DEED_OR_LEASE_VACATION": "Deed Or Lease Vacation", "W9": "W9", "COPY_OF_WILL_AND_OR_TRUST": "Copy Of Will And Or Trust", "MILITARY_WEIGHT_TICKET_EMPTY": "Military Weight Ticket Empty", "MILITARY_WEIGHT_TICKET_EMPTY_WITH_TRUCK": "Military Weight Ticket Empty With Truck", "MILITARY_WEIGHT_TICKET_FULL": "Military Weight Ticket Full", "MILITARY_WEIGHT_TICKET_FULL_WITH_TRUCK": "Military Weight Ticket Full With Truck", "UNKNOWN": "Unknown"}}, "footer": {"privacyPolicy": "Privacy", "privacyPolicyUrl": "https://www.pods.com/privacy-policy", "termsAndConditions": "Terms", "termsAndConditionsUrl": "https://www.pods.com/terms-and-conditions", "copyright": "© {{currentYear}} PODS Enterprises LLC. All rights reserved.", "financingDisclaimersTitle": "DISCLAIMERS", "financingDisclaimersOne": "PODS Enterprises, LLC. is not an agent, advisor, or lender.", "financingDisclaimersTwo": "Your interest rate is determined by the lender and will vary based on your credit score and creditworthiness.", "financingDisclaimersThree": "All financial terms of the loan, including Annual Percentage Rate (APR), fees, charges, and repayment period will be provided to you by the lending partner.", "financingDisclaimersFour": "PODS Enterprises, LLC. assumes no liability or responsibility to you or any third party related to your loan.", "over3000FinancingTerms": "<p>Please carefully review the loan Terms and Conditions provided by the lending partner prior to signing a loan agreement.</p><p>Financing is offered through Acorn Finance, a marketplace of top lenders. Acorn Finance has partnered to provide affordable financing options (\"Moving Loans\") through Upgrade to qualified customers for their moving needs with PODS.</p><p>Only Moving and Storage orders that are $3,000 and above qualify for financing. Please carefully review the loan terms and conditions provided by the lending partner prior to signing a loan agreement.</p><p>Acorn Finance is not currently available in MA, NV, USA territories or outside the USA.</p><p>Offers are an estimate based on information provided from PODS which only includes one month of storage for the number of containers you requested and is subject to change. Please refer to your Truth in Lending Act Disclosures and your Borrower Agreement for the most up-to-date estimates about your loan offer. While PODS does not disclose to us your estimated moving timeline, advances for additional monthly PODS storage fees will be automatically disbursed for up to six months after the date your Loan Account is opened (the \"Advance Period\"), so long as the additional advance would not cause your loan to exceed the maximum amount for you which you are approved (the \"Maximum Total Amount of Advances\"), which will be disclosed in your Borrower Agreement. IF YOUR MOVE IS SCHEDULED FOR MORE THAN 180 DAYS FROM NOW, you will not be able to use this loan to finance your move with PODS.</p><p>*For example, the borrower receives a loan for the PODS moving estimate amount of $5,000. The loan has a 5-year term and a 9.99% Annual Percentage Rate (APR). If the container is only stored for one month and the loan is disbursed in three advances ($750 for initial charges on Day 1, $4,000 for transit on Day 7, and $250 for redelivery on Day 30), the borrower will have a required monthly payment of $106.95.</p><p>If, however, the borrower stores their container for multiple months, the loan amount will be increased by the PODS recurring rental fee each month, up to the maximum loan amount. Disbursements will not be made for charges incurred more than six months after the loan is opened, even if the disbursement would not exceed the maximum loan amount.</p><p><a href=\"https://help.acornfinance.com/en/articles/9095149-upgrade-moving-loan-disclosure\">View Upgrade's Legal Disclosure</a></p><p><a href=\"https://help.acornfinance.com/en/articles/9095173-acorn-finance-legal-disclosure\">View Acorn Finance's Legal Disclosure</a></p><p><a href=\"https://help.acornfinance.com/en/articles/9095185-acorn-finance-advertiser-disclosure\">View Acorn Finance's Advertiser's Disclosure</a></p><p>Acorn Finance is independently owned and operated and has no affiliation with PODS.</p>", "under3000FinancingTerms": "<p>Please carefully review the loan Terms and Conditions provided by the lending partner prior to signing a loan agreement.</p><p>Financing is offered through Acorn Finance, a marketplace of top lenders. Acorn Finance has partnered to provide affordable financing options (\"Moving Loans\") through Upgrade to qualified customers for their moving needs with PODS.</p><p>Acorn Finance is not currently available in MA, NV, USA territories or outside the USA.</p><p><a href=\"https://help.acornfinance.com/en/articles/9095173-acorn-finance-legal-disclosure\">View Acorn Finance's Legal Disclosure</a></p><p><a href=\"https://help.acornfinance.com/en/articles/9095185-acorn-finance-advertiser-disclosure\">View Acorn Finance's Advertiser's Disclosure</a></p><p>Acorn Finance is independently owned and operated and has no affiliation with PODS.</p>", "manageCookiePreferences": "Manage Cookie Preferences"}, "language": {"usEnglishLabel": "United States", "caFrenchLabel": "Canada (French)", "accessibilityLabel": "Language Options"}, "mothInspectionPage": {"header": "Record your Invasive Moth and Fly Inspection", "multi_page_header": "About the Invasive Species Inspection Form", "subtitle": "After creating your inventory and finishing your inspection we will create and save and submit your required Spongy <PERSON>th and Spotted Lanternfly forms to your account so you can get moving.", "multi_page_subtitle": "Spongy Moths and Spotted Lanternflies can devastate local ecosystems and agriculture. Your inspection helps prevent their spread while ensuring smooth transport of your container.", "footnote": "*Spongy Moths and Spotted Lanternflies are damaging and invasive insects capable of destroying important crops, shrubs, and trees. That's why the State of California requires all incoming residents to inspect their belongings as they're preparing for their upcoming moves to California or through California to help protect the environment from these invasive species.", "cards": {"title_fiveDays": "Complete at Least 5 Days Before Your Move", "title_hawaii": "Includes Hawaii Moves", "title_dontRiskIt": "Don't Risk Cancellation!", "bodyText_fiveDays": "This form must be completed at least 5 days before your PODS container is transported to or through California", "bodyText_hawaii": "If you're moving to Hawaii, this form must be filled out since your container will be entering California", "bodyText_dontRiskIt": "Submit your mandatory inspection form 5 days before your move to secure your transit and avoid delays."}, "faq": {"header": "FAQs", "subtitle": "Have more questions? Go to our", "linkTitle": "Moth resource guide", "prompt_incomplete": "What happens if I don't complete my form within 5 days before transit to or through California?", "prompt_notInfested": "I don't live in an infested area. Do I still need to complete the form?", "prompt_afterSubmit": "What happens after I submit my form to PODS?", "prompt_howToInspect": "How do I inspect my items for evidence of spongy moths and spotted lanternflies?", "answer_incomplete": "Transit and delivery of your container will be cancelled, and rescheduled service dates will be subject to availability.", "answer_notInfested": "To ensure compliance with California's mandate, all customers with containers traveling into the state must submit the form at least 5 days before their scheduled transit to or through California.", "answer_afterSubmit": "Once you've submitted your required form to our team, you're done. Now you can focus on other details of your move.", "answer_howToInspect": "Carefully inspect all the items listed on the checklist and your PODS container. Don't forget to closely check all cracks and crevices, and be sure to keep your PODS container closed when not actively loading it. If you see any signs of pest activity, destroy them immediately."}}, "mothInspectionFormPage": {"instructions": "Select all that apply.", "agreeButton": "I agree", "successMessage": "Form Signed Successfully", "restrictedItemMessage": "This item would require an inspection we can't perform since you will hold the only key. Please remove from your container.", "introTitle": "Complete your Invasive Species Inspection Form", "introDesc": "Your destination requires an inventory of items to protect against invasive Spongy Moth and Spotted Lanternfly's. Your answers help us complete required documentation for transport.", "introInfo": "Please select all items below that you are moving in your container.", "introLearnMore": "Learn More", "legalCopy": {"title": "Search your belongings for Spongy Moths or Spotted Lanternflies", "subtitle": "Sign Your Inventory", "description": "Please select the \"Add Your Signature\" button below to provide your electronic signature to the respective federal and state insect self-inspection forms.  By signing below, you are (i) confirming that you have inspected all outdoor household articles you are moving, whether included on the form or not, for all life stages of Spongy Moth in accordance with the procedures set forth in the form, as required by Title 7, Code of Federal Regulation, part 301.45-4(a); and (ii) confirming that you have inspected your items for the Spotted Laternfly, and that you did not see any Spotted Lanternfly egg masses or other life stages in or on anything you are moving.", "Image1Caption": "<PERSON><PERSON><PERSON><PERSON>", "Image2Caption": "Spotted Lanternfly", "RestrictedItemTitle": "Please remove restricted items", "RestrictedItemDetails": "You have restricted items selected above. Please remove them from inventory and container to continue."}}, "notFoundPage": {"title": "Hmm, we can't find this page", "body": "Sorry about that. Check if you typed in the page address correctly or head back to our homepage.", "button": "Back to homepage"}, "fnpsModal": {"title": "Damage Waiver and Release for Fragile and Non-Paved Surfaces", "actionButtonLabel": "Finish", "containerLocation": "Container Location", "body": {"intro": "Customer (defined below) hereby authorizes PODS Enterprises, LLC (\"PODS\") to drive on Customer's lawn, non-paved area or other area of Customer's premises pursuant to Customer's instructions to deliver, place and retrieve Customer's container with the understanding that said actions by PODS may result in damage to Customer's premises and/or surrounding property.", "conditions": "Customer hereby further acknowledges and agrees: (i) that the delivery, placement and retrieval are being performed at the Customer's request, (ii) that the Customer assumes full risk of any damage or loss to Customer's premises or other nearby property, including, but not limited to, damage or loss to any sprinkler heads, decorative pavers, decorative driveway, gutters, roof, retaining walls, building, place of residence, low hanging wires, meters, septic tanks, low hanging tree branches and underground pipes or wires, (iii) that the Customer releases PODS from any such damage or loss resulting from the delivery, placement and retrieval of the PODS container, (iv) Customer waives any rights of subrogation to the extent that any loss or damage is covered by Customer's insurance (e.g., homeowner's insurance), (v) that the Customer shall reimburse PODS for any out-of-pocket costs PODS incurs for towing or otherwise in the event that any of PODS's equipment becomes disabled on Customer's premises, (vi) Customer agrees to continue to pay monthly rent until such time that the container may be retrieved from Customer's premises.", "termsConfirmation": "By selecting \"Add Your Signature\" below, the customer agrees to the terms and conditions above.", "containerLocation": "Container Location"}, "alertTitle": "Something went wrong", "alertDescription": "Please try to submit your waiver again. If this continues please call our customer care team."}, "loadingScreens": {"initial": "Taking you to your account...", "homePage": "Loading your move details..."}, "taskPage": {"welcomePanel": {"title": "Welcome,", "body": "Let's get you <strong>PODS Ready</strong> by taking care of the details."}, "orderStatusCard": {"moving": "MOVING", "storage": "STORAGE", "containers": "Containers", "orderNo": "Order Number"}, "progressBar": {"title": "Tasks", "completed": "completed"}, "rentalAgreement": {"title": "Sign Rental Agreement", "description": "This outlines the details of your rental, including costs and responsibilities, so everyone is on the same page for a stress-free experience."}, "invasiveSpecies": {"title": "Complete Invasive Species Inventory Form", "description": "Help protect the environment by ensuring we're moving responsibly and complying with important regulations."}, "order": {"title": "Order Placed", "description": "Booked on {{date}}"}, "dueDate": "Due {{dueDate}}", "done": "Done", "reviewAndSignButton": "Review & Sign"}, "podsReady": {"setPasswordPage": {"intro": "Last step to get you PODS Ready!", "subtitle": "Let's create your password to get you into your MyPODS account.", "title": "Set up your account", "buttonText": "Set Password", "notifications": {"title": {"invalidPassword": "Password Invalid", "usernameAlreadyTaken": "It looks like you already have an account.", "customerIdTaken": "It looks like you already have an account.", "default": "Something went wrong."}, "message": {"invalidPassword": "Please make sure your password meets the requirements, and then try again.", "usernameAlreadyTaken": "Please sign in with your username and password.", "customerIdTaken": "Please sign in with your username and password.", "default": "Please try again."}}}, "successPage": {"successMessage": "Woohoo!", "title": "You're <strong>PODS Ready</strong>!", "subtitle_one": "You can now access your MyPODS account and manage your order.", "subtitle_other": "You can now access your MyPODS account and manage your orders.", "buttonText": "Continue to MyPODS", "altText": "A moving box opening to release a celebratory burst of packing peanuts"}}}