import { ContainerPlacementSiteType, MoveLeg, MoveLegType } from '../domain/OrderEntities';
import { TranslationKeys } from './TranslationKeys';
import { toCamelCase } from '../helpers/stringHelper';

export enum PriceDifferenceContext {
  INCREASE = 'increase',
  DECREASE = 'decrease'
}

export type ServiceCountdownContext =
  | 'default'
  | 'delivery'
  | 'pickup'
  | 'return'
  | 'dropoff'
  | 'selfdelivery'
  | 'selfpickup';

export enum BannerContext {
  REVIEW_DOCUMENTS = 'reviewDocuments',
  ONLINE_SCHEDULING_UNAVAILABLE = 'onlineSchedulingUnavailable',
  MOTH_AGREEMENTS = 'mothAgreements',
  FNPS_AGREEMENTS = 'fnpsAgreements',
  DESYNCED_EMAILS = 'desyncedEmails'
}

export const getServiceCountdownContext = (moveLeg: MoveLeg): ServiceCountdownContext => {
  if (moveLeg.serviceCountdownType === 'SELFDELIVERY') return 'selfdelivery';
  if (moveLeg.serviceCountdownType === 'SELFPICKUP') return 'selfpickup';
  return 'default';
};

export const getServiceCountdownHelperContext = (moveLeg: MoveLeg) =>
  toCamelCase(moveLeg.serviceCountdownType.toString());

interface DateComponentFields {
  firstDateLabel: string;
  secondDateLabel?: string;
}

const TxDateLabels = TranslationKeys.HomePage.MoveLegs.Scheduling.DateLabels;
export const getDateLabels = (moveLegType: MoveLegType): DateComponentFields => {
  switch (moveLegType) {
    case 'WAREHOUSE_TO_WAREHOUSE':
      return {
        firstDateLabel: TxDateLabels.LABEL_PICKUP,
        secondDateLabel: TxDateLabels.LABEL_DELIVERY
      };
    case 'VISIT_CONTAINER':
      return {
        firstDateLabel: TxDateLabels.LABEL_ARRIVAL,
        secondDateLabel: TxDateLabels.LABEL_MOVE
      };
    default:
      return {
        firstDateLabel: TxDateLabels.LABEL_DEFAULT,
        secondDateLabel: undefined
      };
  }
};

const TxAddressLabels = TranslationKeys.HomePage.MoveLegs.Scheduling.AddressLabels;
export const getMoveLegAddressLabelKey = (moveLeg: MoveLeg) => {
  switch (moveLeg.serviceCountdownType) {
    case 'SELFDELIVERY':
    case 'SELFPICKUP':
      return TxAddressLabels.LABEL_STORAGE_CENTER;
    default:
      return TxAddressLabels.LABEL_DEFAULT;
  }
};

export const getDatePickerLabelKey = (moveLeg: MoveLeg) => {
  if (moveLeg.moveLegType === 'VISIT_CONTAINER') return TxDateLabels.LABEL_VISIT;
  switch (moveLeg.serviceCountdownType) {
    case 'DELIVERY':
    case 'SELFDELIVERY':
      return TxDateLabels.LABEL_DELIVERY;
    case 'DROPOFF':
      return TxDateLabels.LABEL_DROP_OFF;
    case 'MOVE':
      return TxDateLabels.LABEL_MOVE;
    case 'RETURN':
      return TxDateLabels.LABEL_RETURN;
    case 'PICKUP':
    case 'SELFPICKUP':
      return TxDateLabels.LABEL_PICKUP;
    default:
      return TxDateLabels.LABEL_DEFAULT;
  }
};

export enum MothFaqContext {
  INCOMPLETE = 'incomplete',
  NOT_INFESTED = 'notInfested',
  AFTER_SUBMIT = 'afterSubmit',
  HOW_TO_INSPECT = 'howToInspect'
}

// ContainerPlacement
export const getContainerPlacementSiteTypeButton = (siteType: ContainerPlacementSiteType) => {
  switch (siteType) {
    case 'DRIVEWAY':
      return TranslationKeys.HomePage.ContainerPlacement.SiteTypeScreen.Buttons.DRIVEWAY;
    case 'STREET':
      return TranslationKeys.HomePage.ContainerPlacement.SiteTypeScreen.Buttons.STREET;
    case 'PARKING_LOT':
      return TranslationKeys.HomePage.ContainerPlacement.SiteTypeScreen.Buttons.PARKING_LOT;
  }
};
