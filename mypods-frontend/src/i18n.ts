import i18n, { BackendModule, ReadCallback } from 'i18next';
import { initReactI18next } from 'react-i18next';
import I18nextBrowserLanguageDetector, { CustomDetector } from 'i18next-browser-languagedetector';
import {
  sitecoreLanguageCodeToSupportedLocale,
  SupportedLocales
} from './locales/SupportedLocales';

const webpackBackend: BackendModule = {
  type: 'backend',
  read(language: string, namespace: string, callback: ReadCallback): void {
    import(`./locales/${language}.json`)
      .then((resources) => {
        callback(null, resources);
      })
      .catch((error) => {
        callback(error, null);
      });
  },
  init(): void {}
};

const SitecoreLanguageParameterDetector: CustomDetector = {
  name: 'sitecore-querystring',
  lookup() {
    const queryParams = new URLSearchParams(window.location.search);
    const sitecoreLanguage = queryParams.get('sc_lang');
    if (!sitecoreLanguage) {
      return undefined;
    }
    return sitecoreLanguageCodeToSupportedLocale(sitecoreLanguage);
  }
};

const LanguageDetector = new I18nextBrowserLanguageDetector();
LanguageDetector.addDetector(SitecoreLanguageParameterDetector);

i18n
  .use(webpackBackend)
  // pass the i18n instance to react-i18next.
  .use(initReactI18next)
  .use(LanguageDetector)
  // init i18next
  // for all options read: https://www.i18next.com/overview/configuration-options
  .init({
    fallbackLng: SupportedLocales.EN_US,
    supportedLngs: [SupportedLocales.EN_US, SupportedLocales.FR_CA],
    debug: false,

    interpolation: {
      escapeValue: false // not needed for react as it escapes by default
    },

    detection: {
      order: ['querystring', 'sitecore-querystring', 'localStorage'],
      lookupQuerystring: 'lang',
      lookupLocalStorage: 'i18nLang',
      caches: ['localStorage']
    }
  });

export default i18n;
