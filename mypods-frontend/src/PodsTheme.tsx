import { createTheme } from '@mui/material';
import { Design } from './helpers/Design';

declare module '@mui/material/styles' {
  interface Palette {
    neutral: Palette['primary'];
  }

  interface PaletteOptions {
    neutral?: PaletteOptions['primary'];
  }
}

declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides {
    neutral: true;
  }
}

declare module '@mui/material/TextField' {
  interface TextFieldPropsColorOverrides {
    neutral: true;
  }
}

declare module '@mui/material/FormLabel' {
  interface FormLabelPropsColorOverrides {
    neutral: true;
  }
}

declare module '@mui/material/InputLabel' {
  interface InputLabelPropsColorOverrides {
    neutral: true;
  }
}

declare module '@mui/material/styles' {
  interface TypographyVariants {
    accordionSummary: React.CSSProperties;
    body1Accent: React.CSSProperties;
    h2Accent: React.CSSProperties;
  }

  // allow configuration using `createTheme`
  interface TypographyVariantsOptions {
    accordionSummary?: React.CSSProperties;
    body1Accent?: React.CSSProperties;
    h2Accent?: React.CSSProperties;
  }
}

// Update the Typography's variant prop options
declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    accordionSummary: true;
    body1Accent: true;
    h2Accent: true;
  }
}

const baseTheme = createTheme({
  breakpoints: {
    values: {
      xs: 0,
      sm: 768,
      md: 900,
      lg: 1200,
      xl: 1536
    }
  },
  palette: {
    neutral: {
      100: Design.Alias.Color.neutral100,
      200: Design.Alias.Color.neutral200,
      300: Design.Alias.Color.neutral300,
      400: Design.Alias.Color.neutral400,
      500: Design.Alias.Color.neutral500,
      600: Design.Alias.Color.neutral600,
      700: Design.Alias.Color.neutral700,
      800: Design.Alias.Color.neutral800,
      900: Design.Alias.Color.neutral900
    },
    primary: {
      100: Design.Alias.Color.primary100,
      200: Design.Alias.Color.primary200,
      300: Design.Alias.Color.primary300,
      400: Design.Alias.Color.primary400,
      500: Design.Alias.Color.primary500,
      600: Design.Alias.Color.primary600,
      700: Design.Alias.Color.primary700,
      800: Design.Alias.Color.primary800,
      900: Design.Alias.Color.primary900,
      A400: Design.Alias.Color.primary500
    },
    secondary: {
      100: Design.Alias.Color.secondary100,
      200: Design.Alias.Color.secondary200,
      300: Design.Alias.Color.secondary300,
      400: Design.Alias.Color.secondary400,
      500: Design.Alias.Color.secondary500,
      600: Design.Alias.Color.secondary600,
      700: Design.Alias.Color.secondary700,
      800: Design.Alias.Color.secondary800,
      900: Design.Alias.Color.secondary900,
      A400: Design.Alias.Color.secondary500,
      contrastText: Design.Alias.Color.neutral100
    },
    info: {
      light: Design.Alias.Color.infoLight,
      main: Design.Alias.Color.infoMain,
      dark: Design.Alias.Color.infoDark
    },
    success: {
      light: Design.Alias.Color.successLight,
      main: Design.Alias.Color.successMain,
      dark: Design.Alias.Color.successDark
    },
    error: {
      light: Design.Alias.Color.errorLight,
      main: Design.Alias.Color.errorMain,
      dark: Design.Alias.Color.errorDark
    },
    warning: {
      light: Design.Alias.Color.warningLight,
      main: Design.Alias.Color.warningMain,
      dark: Design.Alias.Color.warningDark
    }
  },
  components: {
    MuiSelect: {
      styleOverrides: {
        standard: Design.Alias.Text.BodyUniversal.Xs
      }
    },
    MuiButton: {
      styleOverrides: {
        outlined: {
          fontWeight: Design.Primitives.Font.Weight.bold,
          textTransform: 'none',
          height: '40px',
          width: '112px',
          border: `1px solid ${Design.Alias.Color.neutral300}`,
          ':disabled': {
            color: Design.Alias.Color.neutral400,
            border: `1px solid ${Design.Alias.Color.neutral200}`
          }
        },
        contained: {
          fontWeight: Design.Primitives.Font.Weight.bold,
          textTransform: 'none',
          height: '40px',
          width: '112px',
          ':hover': {
            backgroundColor: Design.Alias.Color.secondary400
          },
          ':active': {
            backgroundColor: Design.Alias.Color.secondary600
          },
          ':disabled': {
            color: Design.Alias.Color.neutral400,
            backgroundColor: Design.Alias.Color.neutral200
          }
        },
        text: ({ ownerState }) => ({
          ...(ownerState.color === 'secondary' && {
            ...Design.Alias.Text.BodyUniversal.SmBold,
            color: Design.Alias.Color.secondary600,
            textTransform: 'none',
            ':hover': {
              backgroundColor: 'transparent',
              color: Design.Alias.Color.secondary600
            },
            ':active': {
              backgroundColor: 'transparent',
              color: Design.Alias.Color.secondary800
            },
            ':disabled': {
              backgroundColor: 'transparent',
              color: Design.Alias.Color.neutral500
            }
          })
        })
      }
    },
    MuiAlert: {
      defaultProps: {
        sx: {
          alignItems: 'center',
          minWidth: '320px',
          minHeight: Design.Primitives.Spacing.lgPlus,
          padding: '6px 16px'
        }
      },
      styleOverrides: {
        root: ({ ownerState }) => ({
          ...(ownerState.severity === 'error' && {
            backgroundColor: Design.Primitives.Color.Semantic.error
          }),
          ...(ownerState.severity === 'success' && {
            backgroundColor: Design.Primitives.Color.Semantic.success
          })
        })
      }
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: ({ ownerState }) => ({
          ...(ownerState.color === 'secondary' && {
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: ownerState.error
                ? Design.Alias.Color.errorMain
                : Design.Alias.Color.secondary800
            }
          })
        })
      }
    },
    MuiFormLabel: {
      defaultProps: {
        sx: {
          '&.Mui-focused': {
            color: Design.Alias.Color.secondary800
          }
        }
      },
      styleOverrides: {
        root: ({ ownerState }) => ({
          ...(ownerState.color === 'secondary' && {
            '&.Mui-focused': {
              color: ownerState.error
                ? Design.Alias.Color.errorMain
                : Design.Alias.Color.secondary600
            }
          })
        })
      }
    }
  }
});

export const theme = createTheme({
  ...baseTheme,
  typography: {
    fontFamily: Design.Primitives.Font.Family.secondary,
    allVariants: {
      fontFamily: Design.Primitives.Font.Family.secondary,
      color: Design.Alias.Color.neutral800
    },
    accordionSummary: {
      ...Design.Alias.Text.BodyUniversal.XsBold,
      color: Design.Alias.Color.secondary500
    },
    body1: {
      ...Design.Alias.Text.BodyUniversal.Md
    },
    body1Accent: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      [baseTheme.breakpoints.up('sm')]: {
        ...Design.Alias.Text.BodyUniversal.Md
      },
      color: Design.Alias.Color.accent100
    },
    body2: {
      ...Design.Alias.Text.BodyUniversal.Lg
    },
    subtitle1: {
      ...Design.Alias.Text.BodyUniversal.Sm
    },
    subtitle2: {
      ...Design.Alias.Text.BodyUniversal.Xs
    },
    h1: {
      ...Design.Alias.Text.Heading.Mobile.Xxl,
      [baseTheme.breakpoints.up('sm')]: {
        ...Design.Alias.Text.Heading.Desktop.Xxl
      },
      color: Design.Alias.Color.accent900
    },
    h2: {
      ...Design.Alias.Text.Heading.Mobile.Xl,
      [baseTheme.breakpoints.up('sm')]: {
        ...Design.Alias.Text.Heading.Desktop.Xl
      }
    },
    h2Accent: {
      ...Design.Alias.Text.Heading.Mobile.Lg,
      [baseTheme.breakpoints.up('sm')]: {
        ...Design.Alias.Text.Heading.Desktop.Xl
      },
      color: Design.Alias.Color.accent100
    },
    h3: {
      ...Design.Alias.Text.Heading.Mobile.Lg,
      [baseTheme.breakpoints.up('sm')]: {
        ...Design.Alias.Text.Heading.Desktop.Lg
      }
    },
    h4: {
      ...Design.Alias.Text.Heading.Mobile.Md,
      [baseTheme.breakpoints.up('sm')]: {
        ...Design.Alias.Text.Heading.Desktop.Md
      }
    },
    h5: {
      ...Design.Alias.Text.Heading.Mobile.Sm,
      [baseTheme.breakpoints.up('sm')]: {
        ...Design.Alias.Text.Heading.Desktop.Sm
      }
    },
    h6: {
      ...Design.Alias.Text.Heading.Mobile.Sm,
      [baseTheme.breakpoints.up('sm')]: {
        ...Design.Alias.Text.Heading.Desktop.Xs
      }
    }
  },
  components: {
    ...baseTheme.components,
    MuiToolbar: {
      styleOverrides: {
        regular: {
          height: '62px',
          minHeight: '52px',
          [baseTheme.breakpoints.down('sm')]: {
            height: '52px'
          }
        }
      }
    },
    MuiTypography: {
      defaultProps: {
        variantMapping: {
          accordionSummary: 'p',
          body1Accent: 'p',
          h2Accent: 'h2'
        }
      }
    },
    MuiChip: {
      styleOverrides: {
        sizeSmall: {
          height: 'auto',
          '.MuiChip-label': {
            fontSize: '14px',
            lineHeight: '21px',
            padding: '3px 8px'
          }
        },
        sizeMedium: {
          height: 'auto',
          borderRadius: '100px',
          '.MuiChip-label': {
            padding: '3px 8px',
            fontSize: '12px',
            borderRadius: '100px',
            lineHeight: '18px',
            [baseTheme.breakpoints.up('sm')]: {
              padding: '7px 10px',
              fontSize: '14px',
              lineHeight: '21px'
            }
          }
        }
      }
    }
  }
});
