import React from 'react';
import { Box, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import MenuIcon from '@mui/icons-material/Menu';
import { Design } from '../../helpers/Design';

interface Props {
  onClickToggle: () => void;
  isMenuOpen: boolean;
}

export const MenuToggleIcon: React.FC<Props> = ({ onClickToggle, isMenuOpen }: Props) => (
  <IconButton
    centerRipple
    aria-label={isMenuOpen ? 'close-navigation-menu' : 'open-navigation-menu'}
    sx={{
      margin: `0 ${Design.Primitives.Spacing.sm}`,
      height: Design.Primitives.Spacing.md,
      width: Design.Primitives.Spacing.md,
      color: Design.Primitives.Color.NeutralDark.midnight,
      '& svg:active': {
        color: Design.Alias.Color.secondary500
      }
    }}
    onClick={onClickToggle}>
    {isMenuOpen ? (
      <Box style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <CloseIcon viewBox="4 4 16 16" />
      </Box>
    ) : (
      <Box style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <MenuIcon
          viewBox="5.5 6 13 12"
          sx={{
            height: '16px',
            width: '22px'
          }}
        />
      </Box>
    )}
  </IconButton>
);
