import React, { act } from 'react';
import { screen } from '@testing-library/react';
import { NavigationBar } from '../NavigationBar';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ROUTES } from '../../../Routes';
import {
  getInitialEntries,
  renderWithPoetProvidersAndState,
  testQueryClient
} from '../../../testUtils/RenderHelpers';
import { createCustomer, createRefreshSessionClaims } from '../../../testUtils/MyPodsFactories';
import userEvent from '@testing-library/user-event';
import { ENV_VARS } from '../../../environment';
import { QueryClient } from '@tanstack/react-query';
import { mockGetCustomer, mockRefreshSession } from '../../../../setupTests';

describe('NavigationBar', () => {
  let queryClient: QueryClient;
  const queryParams = '?customerId=111222333';
  const refreshSessionClaims = createRefreshSessionClaims();
  const customer = createCustomer({ ...refreshSessionClaims });

  beforeEach(() => {
    queryClient = testQueryClient();
    mockGetCustomer.mockResolvedValue(customer);
    mockRefreshSession.mockResolvedValue(refreshSessionClaims);
  });

  function renderNavBar() {
    return renderWithPoetProvidersAndState(<NavigationBar />, {
      initialEntries: getInitialEntries('billing', queryParams),
      customQueryClient: queryClient
    });
  }

  it('renders page options', async () => {
    renderNavBar();

    expect(
      await screen.findByRole('link', { name: TranslationKeys.Navigation.BILLING })
    ).toHaveAttribute('href', ROUTES.BILLING + queryParams);
    expect(
      await screen.findByRole('link', { name: TranslationKeys.Navigation.HOME })
    ).toHaveAttribute('href', ROUTES.HOME + queryParams);

    expect(screen.getByText(TranslationKeys.Navigation.SUPPORT_PHONE_NUMBER)).toBeVisible();
  });

  it('customer can click on their name to enable a drop down', async () => {
    renderNavBar();

    const dropdownButton = await screen.findByRole('button', {
      name: `${customer.firstName} ${customer.lastName.charAt(0)}.`
    });

    expect(dropdownButton).toBeEnabled();

    await act(async () => {
      await userEvent.click(dropdownButton);
    });
    expect(
      screen.getByRole('link', { name: TranslationKeys.Navigation.Dropdown.LOGOUT })
    ).toHaveAttribute('href', ENV_VARS.LOGOUT);
    expect(
      screen.getByText(TranslationKeys.Navigation.Dropdown.ACCOUNT).parentNode
    ).toHaveAttribute('href', ROUTES.ACCOUNT);
  });
});
