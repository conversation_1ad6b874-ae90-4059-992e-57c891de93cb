import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { ROUTES } from '../../../Routes';
import { renderWithPoetProvidersAndState } from '../../../testUtils/RenderHelpers';
import userEvent from '@testing-library/user-event';
import { ENV_VARS } from '../../../environment';
import { MobileMenuDrawer } from '../MobileMenuDrawer';
import { mockGetCustomer, mockNavigate, mockRefreshSession } from '../../../../setupTests';
import { createCustomer, createRefreshSessionClaims } from '../../../testUtils/MyPodsFactories';

describe('NavigationBar', () => {
  const refreshSessionClaims = createRefreshSessionClaims();
  const customerData = createCustomer({ ...refreshSessionClaims });
  const setIsMobileMenuOpen = vi.fn();

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(refreshSessionClaims);
    mockGetCustomer.mockResolvedValue(customerData);
  });

  function renderMobileMenu() {
    return renderWithPoetProvidersAndState(
      <MobileMenuDrawer isOpen={true} setIsMobileMenuOpen={setIsMobileMenuOpen} />
    );
  }

  it.each([
    [TranslationKeys.Navigation.HOME, ROUTES.HOME],
    [TranslationKeys.Navigation.ACCOUNT, ROUTES.ACCOUNT],
    [TranslationKeys.DocumentsPage.HEADER, ROUTES.DOCUMENT],
    [TranslationKeys.Navigation.BILLING, ROUTES.BILLING]
  ])('renders the links', async (label, route) => {
    renderMobileMenu();

    expect(await screen.findByText(label)).toBeInTheDocument();

    await waitFor(async () => {
      await userEvent.click(screen.getByText(label));
    });

    expect(mockNavigate).toHaveBeenCalledWith(route);
  });

  it('renders the support information', async () => {
    renderMobileMenu();

    expect(
      await screen.findByText(TranslationKeys.Navigation.SUPPORT_PHONE_NUMBER)
    ).toBeInTheDocument();
    expect(
      screen.getByText(TranslationKeys.Navigation.SUPPORT_PHONE_NUMBER_MOBILE_LABEL)
    ).toBeInTheDocument();
  });

  it('renders the logout', async () => {
    renderMobileMenu();

    expect(await screen.findByText(TranslationKeys.Navigation.Dropdown.LOGOUT)).toBeInTheDocument();

    await waitFor(async () => {
      await userEvent.click(screen.getByText(TranslationKeys.Navigation.Dropdown.LOGOUT));
    });
    expect(window.location.replace).toHaveBeenLastCalledWith(ENV_VARS.LOGOUT);
  });

  it('clicking the top of the drawer hides the menu', async () => {
    renderMobileMenu();

    const drawer = await screen.findByTestId('drawer');
    await waitFor(async () => {
      await userEvent.click(drawer);
    });

    expect(setIsMobileMenuOpen).toHaveBeenCalledWith(false);
  });
});
