import React from 'react';
import { Grid } from '@mui/material';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import ListItemButton from '@mui/material/ListItemButton';
import { Design } from '../../helpers/Design';
import { MobileNavigationItem } from '../ComponentTypes';

interface Props {
  item: MobileNavigationItem;
  onClick: () => void;
  selectedItem: boolean;
}

export const MenuListItem: React.FC<Props> = ({ item, onClick, selectedItem }: Props) => {
  const styles = menuItemStyles(selectedItem);
  return (
    <ListItemButton disableGutters {...styles.listItemButton} onClick={onClick}>
      {item.icon ? (
        <ListItemIcon {...styles.listIcon}>{item.icon}</ListItemIcon>
      ) : (
        <div style={{ width: '58px', height: '24px' }} />
      )}
      <Grid container {...styles.MenuListGrid} flexDirection="column">
        <ListItemText primary={item.title} {...styles.listItemText} />
        {item.subtitle && (
          <ListItemText {...styles.listItemSubtitle} primaryTypographyProps={{ noWrap: true }}>
            {item.subtitle}
          </ListItemText>
        )}
      </Grid>
    </ListItemButton>
  );
};

export const SupportListItem = ({
  item,
  selectedItem
}: {
  item: MobileNavigationItem;
  selectedItem: boolean;
}) => {
  const styles = menuItemStyles(selectedItem);
  return (
    <ListItemButton href={item.path} disableGutters {...styles.listItemButton}>
      <ListItemIcon {...styles.listIcon}>{item.icon}</ListItemIcon>
      <Grid container flexDirection="column">
        <ListItemText {...styles.listItemText} primary={item.title} />
        <ListItemText {...styles.listItemSubtitle}>{item.subtitle}</ListItemText>
      </Grid>
    </ListItemButton>
  );
};

const menuItemStyles = (selectedItem: boolean) => ({
  listItemButton: {
    sx: {
      paddingY: Design.Primitives.Spacing.sm,
      paddingLeft: Design.Primitives.Spacing.sm,
      width: '100%',
      height: '72px',
      gap: Design.Primitives.Spacing.xs,
      sx: { '.MuiListItemText-root': { marginTop: 0, marginBottom: 0, backgroundColor: 'green' } }
    }
  },
  MenuListGrid: {
    sx: {
      // Need minwidth here so that this component does not overflow the MenuItem container.
      // This is a quirk of flex elements like the MenuItem when dealing with overflowing elements.
      // see https://stackoverflow.com/questions/36230944/prevent-flex-items-from-overflowing-a-container/66689926#66689926 for more info
      minWidth: 0
    }
  },
  listIcon: {
    style: {
      color: selectedItem ? Design.Alias.Color.secondary500 : Design.Alias.Color.neutral800,
      minWidth: 'auto'
    }
  },
  listItemTextContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.xxs
    }
  },
  listItemText: {
    style: { marginTop: 0, marginBottom: 0 },
    sx: {
      '.MuiTypography-root': {
        ...Design.Alias.Text.Heading.Mobile.Xl,
        color: selectedItem ? Design.Alias.Color.secondary500 : Design.Alias.Color.neutral800
      }
    }
  },
  listItemSubtitle: {
    style: { marginTop: 0, marginBottom: 0, maxWidth: '100%' },
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md
    }
  }
});
