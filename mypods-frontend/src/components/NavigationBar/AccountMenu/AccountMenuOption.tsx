import React from 'react';
import { NavLink } from 'react-router-dom';
import MenuItem from '@mui/material/MenuItem';
import { useTranslation } from 'react-i18next';
import { Typography } from '@mui/material';
import { Design } from '../../../helpers/Design';

// -- types --
export interface AccountMenuItem {
  text: string;
  subtext?: string;
  path: string;
  endsSection?: boolean;
}

interface AccountMenuOptionProps {
  option: AccountMenuItem;
  handleClose: () => void;
}

// -- impls --
export const AccountMenuOption: React.FC<AccountMenuOptionProps> = ({
  option,
  handleClose
}: AccountMenuOptionProps) => {
  const { path, text, subtext, endsSection } = option;
  const { t: translation } = useTranslation();
  const styles = accountMenuStyles();

  return (
    <MenuItem disableRipple {...styles.menuItem} onClick={handleClose} divider={!endsSection}>
      <NavLink to={path} {...styles.accountMenuLink}>
        <Typography variant="subtitle1" {...styles.accountMenuLinkText}>
          {translation(text)}
        </Typography>
        {subtext && (
          <Typography
            variant="subtitle2"
            noWrap
            {...styles.accountMenuLinkSubtext}
            dangerouslySetInnerHTML={{ __html: subtext }}
          />
        )}
      </NavLink>
    </MenuItem>
  );
};

// -- styles --
const accountMenuStyles = () => ({
  accountMenuLink: {
    style: {
      width: '100%',
      // Need minwidth here so that this component does not overflow the MenuItem container.
      // This is a quirk of flex elements like the MenuItem when dealing with overflowing elements.
      // see https://stackoverflow.com/questions/********/prevent-flex-items-from-overflowing-a-container/********#******** for more info
      minWidth: 0,
      height: '100%',
      padding: '6px 16px',
      textDecoration: 'none',
      color: 'inherit'
    }
  },
  accountMenuLinkText: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.SmBold,
      color: 'inherit'
    }
  },
  accountMenuLinkSubtext: {
    sx: {
      paddingTop: '6px'
    }
  },
  menuItem: {
    sx: {
      padding: 0,
      maxWidth: '100%',
      ':hover': {
        backgroundColor: 'transparent',
        color: Design.Alias.Color.secondary500
      }
    }
  }
});
