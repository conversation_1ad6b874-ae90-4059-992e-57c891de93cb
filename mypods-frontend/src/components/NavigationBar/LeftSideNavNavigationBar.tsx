import React, { useState } from 'react';
import { Grid, Toolbar, useMediaQuery } from '@mui/material';
import { theme } from '../../PodsTheme';
import { Design } from '../../helpers/Design';
import { MenuToggleIcon } from './MenuToggleIcon';
import { ENV_VARS } from '../../environment';

export const LeftSideNavNavigationBar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const styles = navBarStyles(isMobile);
  const onMenuToggleClicked = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <Grid item xs={12} sx={styles.leftMenu}>
      <Toolbar disableGutters>
        <Grid {...styles.imageGrid}>
          {isMobile && (
            <MenuToggleIcon onClickToggle={onMenuToggleClicked} isMenuOpen={isMobileMenuOpen} />
          )}
          <img
            src={`${ENV_VARS.ASSETS_BASE_URL}/pods-secondary-logo-rgb-1.webp`}
            alt="pods-logo"
            {...styles.image}
          />
        </Grid>
      </Toolbar>
    </Grid>
  );
};

const navBarStyles = (isMobile: boolean) => ({
  leftMenu: {
    flexDirection: 'column',
    width: { xs: '100%', md: '250px' }
  },
  imageGrid: {
    sx: {
      width: '100%',
      height: '52px',
      flexShrink: '0',
      display: 'flex',
      alignItems: 'center',
      background: Design.Alias.Color.neutral100,
      justifyContent: isMobile ? 'flex-start' : 'center',
      borderRight: `1px solid ${Design.Alias.Color.neutral200}`,
      borderBottom: `1px solid ${Design.Alias.Color.neutral200}`
    }
  },
  image: {
    style: {
      height: '23px',
      flexShrink: '0',
      display: 'block'
    }
  }
});
