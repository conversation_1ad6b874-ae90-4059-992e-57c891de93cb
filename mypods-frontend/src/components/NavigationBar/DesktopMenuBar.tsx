import React from 'react';
import { Box, Typography } from '@mui/material';
import { NavLink } from 'react-router-dom';
import { NavigationItem } from '../ComponentTypes';
import { Design } from '../../helpers/Design';

interface Props {
  items: NavigationItem[];
}

export const DesktopMenuBar: React.FC<Props> = ({ items }: Props) => {
  const styles = stylesheet();

  return (
    <Box {...styles.linksContainer}>
      {items.map((item) => (
        <NavLink
          to={item.path}
          data-testid={`link-${item.title}`}
          key={item.title}
          style={({ isActive }) => ({
            ...styles.navLink,
            flexDirection: 'column',
            color: isActive ? `${Design.Alias.Color.secondary500}` : Design.Alias.Color.neutral800
          })}>
          {({ isActive }) => (
            <Box {...styles.linkContainer}>
              <Typography {...styles.title}>{item.title}</Typography>
              {isActive && <Box {...styles.activeLinkUnderscore} />}
            </Box>
          )}
        </NavLink>
      ))}
    </Box>
  );
};

// -- styles --
const stylesheet = () => ({
  linksContainer: {
    sx: {
      display: 'flex',
      justifyContent: 'center',
      flex: 1,
      alignItems: 'flex-start',
      gap: Design.Primitives.Spacing.lg,
      fontFamily: 'Open Sans'
    }
  },
  linkContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      cursor: 'pointer',
      textAlign: 'center'
    }
  },
  navLink: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '80px',
    gap: '4.5px',
    alignSelf: 'stretch',
    textDecoration: 'none'
  },
  title: {
    ...Design.Alias.Text.BodyUniversal.MdBold,
    fontFamily: 'Open Sans',
    fontStyle: 'normal',
    fontWeight: '600',
    color: 'inherit'
  },
  activeLinkUnderscore: {
    sx: {
      paddingTop: '2.5px',
      height: '.5px',
      width: '36px',
      backgroundColor: Design.Alias.Color.secondary500
    }
  }
});
