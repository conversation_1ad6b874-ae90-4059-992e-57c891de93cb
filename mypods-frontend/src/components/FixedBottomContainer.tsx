import { AppBar, Grid, useMediaQuery } from '@mui/material';
import React from 'react';
import { Design } from '../helpers/Design';
import { theme } from '../PodsTheme';

export const FixedBottomContainer = ({ children }: { children: React.ReactNode }) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = style(isMobile);

  return (
    <>
      <Grid sx={{ marginTop: '80px' }} />
      <AppBar position="fixed" {...styles.appBar}>
        <Grid container {...styles.barContainer} id="bar-container">
          {children}
        </Grid>
      </AppBar>
    </>
  );
};

const style = (isMobile: boolean) => ({
  appBar: {
    sx: {
      top: 'auto',
      bottom: 0,
      backgroundColor: 'transparent',
      '.MuiGrid-root:has(+ header)': {
        display: 'none'
      }
    }
  },
  barContainer: {
    sx: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: Design.Primitives.Spacing.sm,
      backgroundColor: isMobile ? Design.Alias.Color.neutral100 : Design.Alias.Color.accent100,
      position: 'relative',
      boxShadow: `0px -1px 4px 0px rgba(0, 0, 0, 0.25)`,
      height: '80px',
      borderTopRightRadius: isMobile ? '8px' : 0,
      borderTopLeftRadius: isMobile ? '8px' : 0
    }
  }
});
