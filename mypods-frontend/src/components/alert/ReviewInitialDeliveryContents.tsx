import React from 'react';
import { CircularProgress, Grid, Typography, Button, useMediaQuery } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { Design } from '../../helpers/Design';
import { PriceDifferenceContext } from '../../locales/TranslationConstants';
import { theme } from '../../PodsTheme';

const Tx = TranslationKeys.HomePage.MoveLegs;

// -- Price Difference Confirmation Content--
// -- types --
interface DefaultContainerPlacementProps {
  onReview: () => void;
  onAccept: () => void;
  isLoading: boolean;
}

// -- impls --
export const ReviewContainerPlacementContent: React.FC<DefaultContainerPlacementProps> = ({
  onReview,
  onAccept,
  isLoading
}: DefaultContainerPlacementProps) => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = contentStyles(isMobile);

  return (
    <Grid>
      <Grid {...styles.textContainer}>
        <Typography {...styles.title}>{translate(Tx.InitialDeliveryReviewAlert.TITLE)}</Typography>
        <Typography {...styles.subtitle}>
          {translate(Tx.InitialDeliveryReviewAlert.SUBTITLE)}
        </Typography>
      </Grid>
      <Grid {...styles.buttonContainer}>
        <Button onClick={onReview} {...styles.reviewButton} disabled={isLoading}>
          {translate(TranslationKeys.CommonComponents.REVIEW_BUTTON)}
        </Button>
        <LoadingButton
          onClick={onAccept}
          disabled={isLoading}
          loadingPosition={isLoading ? 'start' : undefined}
          centerRipple
          startIcon={isLoading && <CircularProgress size={20} color="inherit" />}
          {...styles.acceptButton}>
          {translate(TranslationKeys.CommonComponents.ACCEPT_BUTTON)}
        </LoadingButton>
      </Grid>
    </Grid>
  );
};

// -- Price Difference --
// -- types --
interface PriceDifferenceProps {
  onConfirm: () => void;
  onCancel: () => void;
  isLoading: boolean;
  priceDifference: number;
}

// -- impls --
export const PriceDifferenceAlertContent: React.FC<PriceDifferenceProps> = ({
  onConfirm,
  onCancel,
  isLoading,
  priceDifference
}: PriceDifferenceProps) => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = contentStyles(isMobile);

  const context =
    priceDifference > 0 ? PriceDifferenceContext.INCREASE : PriceDifferenceContext.DECREASE;
  const amount = Math.abs(priceDifference).toFixed(2);

  return (
    <>
      <Grid {...styles.textContainer}>
        <Typography {...styles.title}>
          {translate(Tx.InitialDeliveryPriceDifferenceAlert.TITLE)}
        </Typography>
        <Typography
          {...styles.subtitle}
          dangerouslySetInnerHTML={{
            __html: translate(Tx.InitialDeliveryPriceDifferenceAlert.SUBTITLE, { context, amount })
          }}
        />
      </Grid>
      <Grid {...styles.buttonContainer}>
        <Button onClick={onCancel} {...styles.reviewButton} disabled={isLoading}>
          {translate(TranslationKeys.CommonComponents.CANCEL_BUTTON)}
        </Button>
        <LoadingButton
          onClick={onConfirm}
          disabled={isLoading}
          loadingPosition={isLoading ? 'start' : undefined}
          centerRipple
          startIcon={isLoading && <CircularProgress size={20} color="inherit" />}
          {...styles.acceptButton}>
          {translate(TranslationKeys.CommonComponents.CONFIRM_BUTTON)}
        </LoadingButton>
      </Grid>
    </>
  );
};

// -- styles --
const contentStyles = (isMobile: boolean) => ({
  textContainer: {
    sx: {
      gap: isMobile ? Design.Primitives.Spacing.xs : Design.Primitives.Spacing.xxxs
    }
  },
  title: {
    sx: {
      ...Design.Alias.Text.Heading.Mobile.Sm,
      marginTop: '6px'
    }
  },
  subtitle: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Sm,
      color: '#242424'
    }
  },
  buttonContainer: {
    sx: {
      display: 'flex',
      justifyContent: 'end',
      gap: '4px',
      marginTop: '12px'
    }
  },
  reviewButton: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.SmBold,
      color: Design.Alias.Color.successDark,
      textTransform: 'capitalize',
      borderRadius: '4px',
      border: `1px solid ${Design.Alias.Color.successDark}`,
      background: Design.Alias.Color.successLight,
      width: '120px',
      '&:hover': {
        background: '#dbefdd'
      },
      '&:disabled': {
        color: Design.Alias.Color.neutral500,
        border: `1px solid ${Design.Alias.Color.neutral300}`,
        background: Design.Alias.Color.neutral200
      }
    }
  },
  acceptButton: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.SmBold,
      color: Design.Alias.Color.neutral100,
      textTransform: 'capitalize',
      borderRadius: '4px',
      border: `1px solid ${Design.Alias.Color.successDark}`,
      background: Design.Alias.Color.successMain,
      width: '120px',
      '&:hover': {
        background: Design.Alias.Color.successDark
      },
      '&:disabled': {
        color: Design.Alias.Color.neutral500,
        border: `1px solid ${Design.Alias.Color.neutral300}`,
        background: Design.Alias.Color.neutral200
      }
    }
  }
});
