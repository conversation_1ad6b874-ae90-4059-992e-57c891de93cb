import { screen } from '@testing-library/react';
import { renderWithPoetProvidersAndState } from '../../../testUtils/RenderHelpers';
import { ReviewInitialDeliveryAlert } from '../ReviewInitialDeliveryAlert';
import {
  mockAcceptInitialDeliveryPlacement,
  mockRefreshSession,
  mockUpdateMoveLeg
} from '../../../../setupTests';
import {
  createContainer,
  createMoveLeg,
  createMoveLegUpdateResponse,
  createOrder,
  createRefreshSessionClaims
} from '../../../testUtils/MyPodsFactories';
import { vi } from 'vitest';
import { addDays } from 'date-fns';
import userEvent from '@testing-library/user-event';
import { act } from 'react';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { MoveLegProvider } from '../../../pages/HomePage/container/moveleg/MoveLegContext';

const TxButtons = TranslationKeys.CommonComponents;

describe('Review Initial Delivery Alert', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
  });

  it('should alert user about a price difference when there is a change in container placement', async () => {
    const mockHideAlert = vi.fn();
    const mockRefetch = vi.fn();

    let priceDiffResponse = createMoveLegUpdateResponse({
      priceDifference: '-12.99',
      quoteId: '123456789'
    });
    mockUpdateMoveLeg
      .mockResolvedValueOnce(priceDiffResponse)
      .mockResolvedValue(createMoveLegUpdateResponse());

    const moveLeg = createMoveLeg({
      scheduledStatus: 'FUTURE',
      scheduledDate: addDays(new Date(), 2)
    });
    const orderWithInitialDelivery = createOrder({
      initialDeliveryPlacementIsReviewed: false,
      containers: [createContainer({ moveLegs: [moveLeg] })]
    });

    renderWithPoetProvidersAndState(
      <MoveLegProvider
        moveLeg={moveLeg}
        isLastRenderedMoveLeg={true}
        lastMoveLegId={moveLeg.moveLegId}>
        <ReviewInitialDeliveryAlert hideAlertCallback={mockHideAlert} />
      </MoveLegProvider>,
      {
        ordersState: {
          orders: [orderWithInitialDelivery],
          refetch: mockRefetch,
          showLoader: false,
          refetchOnFailure: vi.fn()
        }
      }
    );

    const reviewButton = await screen.findByRole('button', { name: TxButtons.REVIEW_BUTTON });
    await act(async () => {
      await user.click(reviewButton);
    });

    const finishButton = await screen.findByRole('button', {
      name: TranslationKeys.HomePage.ContainerPlacement.ReviewScreen.FINISH_BUTTON
    });
    await act(async () => {
      await user.click(finishButton);
    });

    expect(
      screen.getByText(
        `${TranslationKeys.HomePage.MoveLegs.InitialDeliveryPriceDifferenceAlert.SUBTITLE}[decrease,12.99]`
      )
    ).toBeInTheDocument();

    const confirmButton = await screen.findByRole('button', { name: TxButtons.CONFIRM_BUTTON });
    expect(confirmButton).toBeEnabled();
    await act(async () => {
      await user.click(confirmButton);
    });

    expect(mockUpdateMoveLeg).toHaveBeenCalled();
    expect(mockAcceptInitialDeliveryPlacement).toHaveBeenCalled();
    expect(mockRefetch).toHaveBeenCalled();
    expect(mockHideAlert).toHaveBeenCalled();
  });
});
