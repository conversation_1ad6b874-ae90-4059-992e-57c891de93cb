import { screen, waitFor } from '@testing-library/react';
import { GlobalBanners } from '../GlobalBanners';
import {
  mockedUseFeatureFlags,
  mockGetCustomer,
  mockGetOrderDocuments,
  mockNavigate,
  mockRefreshSession
} from '../../../../setupTests';
import {
  createCustomer,
  createEmail,
  createOrderDocument,
  createRefreshSessionClaims,
  createUseFeatureFlagResult
} from '../../../testUtils/MyPodsFactories';
import { renderWithPoetProvidersAndState, testQueryClient } from '../../../testUtils/RenderHelpers';
import React from 'react';
import userEvent from '@testing-library/user-event';
import { ROUTES } from '../../../Routes';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { BannerContext } from '../../../locales/TranslationConstants';
import {
  getWeightTicketBanner,
  setOrdersWithSignedMothAgreements,
  setWeightTicketBanner
} from '../../../helpers/storageHelpers';
import { OrderDocumentAPI } from '../../../networkRequests/responseEntities/DocumentApiEntities';
import { DISMISS_ICON_ID } from '../../icons/DismissXIcon';
import { QueryCacheKeys } from '../../../networkRequests/QueryCacheKeys';

describe('Global Banners', () => {
  const Tx = TranslationKeys.CommonComponents.Banner;

  const spottedLanternFlyDocument: OrderDocumentAPI = {
    ...createOrderDocument({
      orderId: '123456',
      docType: 'SPOTTED_LANTERN_FLY_FORM',
      docStatus: 'SENT',
      billingCompanyCode: '1234',
      docPath: '',
      docName: 'Spotted Lantern Fly Form',
      isInterFranchise: true
    })
  };

  const emptyWeightTicket: OrderDocumentAPI = {
    ...createOrderDocument({
      orderId: '123456',
      docType: 'MILITARY_WEIGHT_TICKET_EMPTY_WITH_TRUCK',
      docStatus: 'SENT',
      billingCompanyCode: '1234',
      docPath: '',
      docName: 'Empty weight ticket',
      isInterFranchise: true
    })
  };

  const fullWeightTicket: OrderDocumentAPI = {
    ...createOrderDocument({
      orderId: '123456',
      docType: 'MILITARY_WEIGHT_TICKET_FULL',
      billingCompanyCode: '1234',
      docName: 'Full weight ticket'
    })
  };

  const FnpsAgreements: OrderDocumentAPI = {
    ...createOrderDocument({
      orderId: '123456',
      docStatus: 'SENT',
      billingCompanyCode: '1234',
      docType: 'FRAGILE_AND_NON_PAVED_SURFACE_WAIVER',
      docName: 'Fragile Non Paved Surface Waiver'
    })
  };

  const customerWithDesyncedEmails = createCustomer({
    isConverted: true,
    email: createEmail({ address: '<EMAIL>' }),
    username: '<EMAIL>'
  });

  function renderBanners() {
    return renderWithPoetProvidersAndState(<GlobalBanners />, {
      customQueryClient: testQueryClient()
    });
  }

  describe('Moth Agreements', () => {
    beforeEach(() => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [spottedLanternFlyDocument]
      });

      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
      mockGetCustomer.mockResolvedValue(createCustomer());

      testQueryClient().setQueryData([QueryCacheKeys.ORDER_DOCUMENTS_KEY], {
        documents: [spottedLanternFlyDocument]
      });
    });

    afterEach(() => {
      setOrdersWithSignedMothAgreements(null);
      setWeightTicketBanner(null);
    });

    it('should display banner for moth agreements', async () => {
      renderBanners();

      expect(
        await screen.findByText(`${Tx.TITLE}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.MESSAGE}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.CTA_LABEL}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).toBeInTheDocument();
    });

    it('should navigate to the first page of the moth form on button click', async () => {
      renderBanners();

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', {
            name: `${Tx.CTA_LABEL}[${BannerContext.MOTH_AGREEMENTS}]`
          })
        );
      });
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.MOTH_FLY_INSPECTION);
    });

    it('should not display a banner if user has already signed it', async () => {
      spottedLanternFlyDocument.docStatus = 'COMPLETED';
      mockGetOrderDocuments.mockResolvedValue({
        documents: [spottedLanternFlyDocument]
      });

      renderBanners();

      await waitFor(async () => {
        expect(
          screen.queryByText(`${Tx.TITLE}[${BannerContext.MOTH_AGREEMENTS}]`)
        ).not.toBeInTheDocument();
      });
    });
  });

  describe('FNPS Agreements', () => {
    const renderGlobalBannerWithFNPSAgreement = () => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [FnpsAgreements]
      });

      return renderBanners();
    };

    beforeEach(() => {
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
      mockGetCustomer.mockResolvedValue(createCustomer());
      mockGetOrderDocuments.mockResolvedValue({
        documents: [FnpsAgreements]
      });
    });

    it('should display banner for FNPS agreement when FNPS feature flag on', async () => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isFNPSEnabled: () => true })
      );
      renderGlobalBannerWithFNPSAgreement();
      await waitFor(() => {
        expect(
          screen.queryByText(`${Tx.TITLE}[${BannerContext.FNPS_AGREEMENTS}]`)
        ).toBeInTheDocument();
      });
    });

    it('should not display banner for FNPS agreement when feature flag set false', async () => {
      mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
        createUseFeatureFlagResult({ isFNPSEnabled: () => false })
      );
      renderGlobalBannerWithFNPSAgreement();
      await waitFor(() => {
        expect(
          screen.queryByText(`${Tx.TITLE}[${BannerContext.FNPS_AGREEMENTS}]`)
        ).not.toBeInTheDocument();
      });
    });
  });

  describe('Weight Tickets', () => {
    const renderGlobalBannerWithWeightTickets = () => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [fullWeightTicket, emptyWeightTicket]
      });

      return renderBanners();
    };

    beforeEach(() => {
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
      mockGetCustomer.mockResolvedValue(createCustomer());
      mockGetOrderDocuments.mockResolvedValue({
        documents: [fullWeightTicket, emptyWeightTicket]
      });
    });

    afterEach(() => {
      setWeightTicketBanner(null);
      testQueryClient().setQueryData([QueryCacheKeys.ORDER_DOCUMENTS_KEY], {
        documents: []
      });
    });

    it('should display banner for weight tickets', async () => {
      renderGlobalBannerWithWeightTickets();

      expect(
        await screen.findByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.MESSAGE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.CTA_LABEL}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).toBeInTheDocument();
    });

    it('should navigate to document page on button click', async () => {
      renderGlobalBannerWithWeightTickets();

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', { name: `${Tx.CTA_LABEL}[reviewDocuments]` })
        );
      });
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.DOCUMENT);
    });

    it('should dismiss banner on X click', async () => {
      renderGlobalBannerWithWeightTickets();

      await waitFor(async () => {
        await userEvent.click(screen.getByTestId(DISMISS_ICON_ID));
      });
      expect(
        screen.queryByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).not.toBeInTheDocument();
      expect(getWeightTicketBanner()).toBeTruthy();
    });

    it('should not render a weight ticket banner if a weight ticket does not exist for a customer', async () => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: []
      });
      renderBanners();

      await waitFor(() => {
        expect(
          screen.queryByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
        ).not.toBeInTheDocument();
      });
    });

    it('should not render weight ticket banner if a weight ticket does exist but has been dismissed', async () => {
      setWeightTicketBanner(true);

      renderGlobalBannerWithWeightTickets();

      await waitFor(() => {
        expect(
          screen.queryByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
        ).not.toBeInTheDocument();
      });
    });
  });

  describe('Desynced Emails', () => {
    beforeEach(() => {
      mockGetCustomer.mockResolvedValue(customerWithDesyncedEmails);
      mockRefreshSession.mockResolvedValue(
        createRefreshSessionClaims({ username: '<EMAIL>' })
      );
      mockGetOrderDocuments.mockResolvedValue({
        documents: []
      });
    });

    afterEach(() => {
      setWeightTicketBanner(null);
      testQueryClient().setQueryData([QueryCacheKeys.ORDER_DOCUMENTS_KEY], {
        documents: []
      });
    });

    it('should display banner for desynced emails', async () => {
      renderBanners();

      expect(
        await screen.findByText(`${Tx.TITLE}[${BannerContext.DESYNCED_EMAILS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.MESSAGE}[${BannerContext.DESYNCED_EMAILS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.CTA_LABEL}[${BannerContext.DESYNCED_EMAILS}]`)
      ).toBeInTheDocument();
    });

    it('should navigate to account page on button click', async () => {
      renderBanners();

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', { name: `${Tx.CTA_LABEL}[desyncedEmails]` })
        );
      });
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.ACCOUNT);
    });

    it('should not render a desynced email banner if the customers email & username match', async () => {
      mockGetCustomer.mockResolvedValue(createCustomer());

      renderBanners();

      await waitFor(() => {
        expect(
          screen.queryByText(`${Tx.TITLE}[${BannerContext.DESYNCED_EMAILS}]`)
        ).not.toBeInTheDocument();
      });
    });
  });
});
