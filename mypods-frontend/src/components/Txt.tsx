import { Typography } from '@mui/material';
import React, { ComponentProps } from 'react';
import { Trans, useTranslation } from 'react-i18next';

// -- types --
// prettier savages any attempts to format this for readability
type BaseProps = ComponentProps<typeof Typography> &
  Pick<ComponentProps<typeof Trans>, 'i18nKey' | 'count' | 'context' | 'values' | 'components'>;

interface Props extends BaseProps {
  raw?: boolean;
  inline?: boolean;
}

// -- impls --
export const Txt = ({
  i18nKey,
  context,
  count,
  values,
  components,
  raw = false,
  inline = false,
  children,
  ...props
}: Props) => {
  const { t } = useTranslation();

  // -- view --
  if (raw) {
    const content = i18nKey != null ? t(i18nKey) : children;

    if (inline) {
      return <span className={props.className}>{content}</span>;
    }

    return (
      <p style={{ margin: '0' }} className={props.className}>
        {content}
      </p>
    );
  }

  return (
    <Typography {...(inline && { component: 'span' })} {...props}>
      {i18nKey != null ? (
        <Trans
          i18nKey={i18nKey}
          context={context}
          count={count}
          values={values}
          components={components}
        />
      ) : (
        children
      )}
    </Typography>
  );
};
