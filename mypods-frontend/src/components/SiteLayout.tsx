import React, { ReactNode } from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { NavigationBar } from './NavigationBar/NavigationBar';
import { Footer } from './Footer/Footer';
import { theme } from '../PodsTheme';
import { Design } from '../helpers/Design';
import { LEFT_NAV_ENABLED, useFeatureFlags } from '../helpers/useFeatureFlags';
import { LeftSideNavSiteLayout } from './LeftSideNavSiteLayout';
import { ScrollRestoration } from '../routes/ScrollRestoration';

interface LayoutProps {
  children: ReactNode;
  showHeaderFooter?: boolean;
  globalBanners?: ReactNode;
}

export const SiteLayout: React.FC<LayoutProps> = ({
  children,
  showHeaderFooter = true,
  globalBanners
}: LayoutProps) => {
  const { isLeftNavEnabled } = useFeatureFlags([LEFT_NAV_ENABLED]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = siteLayoutStyles(isMobile);

  return isLeftNavEnabled() ? (
    <LeftSideNavSiteLayout>{children}</LeftSideNavSiteLayout>
  ) : (
    <Grid {...styles.container}>
      {showHeaderFooter && <NavigationBar />}
      <ScrollRestoration>
        <Grid {...styles.belowHeaderContent}>
          {globalBanners}
          <Grid {...styles.pageLayoutContent}>{children}</Grid>
        </Grid>
        {showHeaderFooter && <Footer />}
      </ScrollRestoration>
    </Grid>
  );
};

// -- styles --
const siteLayoutStyles = (isMobile: boolean) => {
  const navBarHeight = isMobile ? '52px' : '62px'; // heights are set in the PodsTheme
  return {
    container: {
      sx: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        minHeight: `calc(100vh - ${navBarHeight})`,
        marginTop: navBarHeight
      }
    },
    belowHeaderContent: {
      sx: {
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        overflowY: 'auto',
        width: '100%',
        alignItems: 'center'
      }
    },
    pageLayoutContent: {
      sx: {
        paddingTop: {
          xs: Design.Primitives.Spacing.md,
          md: Design.Primitives.Spacing.lgPlus
        },
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        flex: 1
      }
    }
  };
};
