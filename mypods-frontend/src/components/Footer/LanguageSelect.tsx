import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid, InputLabel, MenuItem, Select, SelectChangeEvent, Typography } from '@mui/material';
import { Design } from '../../helpers/Design';
import { ALL_SUPPORTED_LOCALES, SupportedLocales } from '../../locales/SupportedLocales';
import { TranslationKeys } from '../../locales/TranslationKeys';

// -- types --

const detectedLanguageOrDefault = (detectedLanguage: string) => {
  if (!ALL_SUPPORTED_LOCALES.includes(detectedLanguage)) {
    return SupportedLocales.EN_US;
  }
  return detectedLanguage;
};

// -- impls --
export const LanguageSelect: React.FC = () => {
  const { i18n, t: translate } = useTranslation();
  const [language, setLanguage] = useState(detectedLanguageOrDefault(i18n.language));
  const handleOnChange = (event: SelectChangeEvent) => {
    setLanguage(event.target.value);
  };
  useEffect(() => {
    i18n.changeLanguage(language);
    if (language === SupportedLocales.FR_CA) {
      document.documentElement.lang = 'fr-CA';
    } else {
      document.documentElement.lang = 'en-US';
    }
  }, [i18n, language]);

  const hideIfSelected = (itemLanguage: string) => {
    if (language !== itemLanguage) {
      return {};
    }
    return { display: 'none' };
  };

  const localeMenuItem = (localeShortName: string, region: string) => (
    <MenuItem value={localeShortName} sx={hideIfSelected(localeShortName)}>
      <Typography
        color={Design.Alias.Color.accent800}
        sx={{ ...Design.Alias.Text.BodyUniversal.Xs }}>
        {region}
      </Typography>
    </MenuItem>
  );
  return (
    <Grid container maxWidth="fill-available">
      <InputLabel id="language-select-label" sx={{ display: 'none' }}>
        {translate(TranslationKeys.Language.ACCESSIBILITY_LABEL)}
      </InputLabel>
      <Select
        labelId="language-select-label"
        value={language}
        onChange={handleOnChange}
        variant="standard"
        disableUnderline
        sx={{
          '.MuiSelect-icon': {
            top: '0px'
          }
        }}
        inputProps={{
          id: 'location-select-dropdown',
          sx: {
            '&:focus': {
              backgroundColor: 'transparent'
            }
          }
        }}
        MenuProps={{
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center'
          },
          transformOrigin: {
            vertical: 'bottom',
            horizontal: 'center'
          },
          MenuListProps: {
            sx: {
              backgroundColor: Design.Primitives.Color.Bkgd.bkgd
            }
          },
          PaperProps: {
            sx: {
              '& .MuiMenuItem-root:hover': {
                backgroundColor: Design.Primitives.Color.NeutralLight.cloud
              }
            }
          }
        }}>
        {localeMenuItem(
          SupportedLocales.EN_US,
          translate(TranslationKeys.Language.US_ENGLISH_LABEL)
        )}
        {localeMenuItem(
          SupportedLocales.FR_CA,
          translate(TranslationKeys.Language.CA_FRENCH_LABEL)
        )}
      </Select>
    </Grid>
  );
};
