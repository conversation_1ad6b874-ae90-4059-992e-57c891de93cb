import { AppBar, CircularProgress, Grid, useMediaQuery } from '@mui/material';
import Button from '@mui/material/Button';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { LoadingButton } from '@mui/lab';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { theme } from '../../../PodsTheme';
import { DownChevronIcon } from '../../icons/DownChevronIcon';
import { useScrollbarWidth } from '../../../helpers/useScrollbarWidth';

export const DocumentActionsBar = ({
  showScroll,
  acceptanceEnabled,
  onAccept,
  acceptIsLoading,
  onDecline
}: {
  showScroll: boolean;
  acceptanceEnabled: boolean;
  onAccept: () => void;
  acceptIsLoading: boolean;
  onDecline: () => void;
}) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isLargeScreen = useMediaQuery(theme.breakpoints.up('lg'));
  const scrollbarWidth = useScrollbarWidth();
  const styles = barStyles(isMobile, isLargeScreen, scrollbarWidth);
  const { t: translate } = useTranslation();
  const gridRef = useRef<null | HTMLDivElement>(null);

  return (
    <Grid ref={gridRef}>
      <AppBar position="fixed" {...styles.appBar}>
        <Grid container {...styles.barContainer} id="bar-container">
          <Grid container item {...styles.barContent} id="bar-content" xs={12} lg={8}>
            <DownChevronIcon
              sx={{
                position: 'absolute',
                ...(showScroll ? { display: 'inline' } : { display: 'none' })
              }}
              {...styles.icon}
              onClick={() => gridRef.current?.scrollIntoView({ behavior: 'smooth' })}
            />
            <Grid container item {...styles.buttonWrapper}>
              <Button
                size={isMobile ? 'small' : 'medium'}
                variant="outlined"
                color="secondary"
                {...styles.buttons.decline}
                onClick={onDecline}>
                {translate(TranslationKeys.Onboarding.DECLINE_BUTTON)}
              </Button>
              <LoadingButton
                size={isMobile ? 'small' : 'medium'}
                variant="contained"
                color="secondary"
                disabled={!acceptanceEnabled || acceptIsLoading}
                startIcon={acceptIsLoading && <CircularProgress color="inherit" size={20} />}
                {...styles.buttons.accept}
                onClick={onAccept}>
                {translate(TranslationKeys.Onboarding.ACCEPT_BUTTON, {
                  context: isMobile || acceptIsLoading ? '' : 'desktop'
                })}
              </LoadingButton>
            </Grid>
          </Grid>
        </Grid>
      </AppBar>
    </Grid>
  );
};

const barStyles = (isMobile: boolean, isLargeScreen: boolean, scrollbarWidth: number) => ({
  appBar: {
    sx: { top: 'auto', bottom: 0, backgroundColor: 'transparent' }
  },
  barContainer: {
    sx: {
      display: 'flex',
      justifyContent: 'center',
      backgroundColor: Design.Alias.Color.accent100,
      paddingX: () => {
        if (isLargeScreen) return '104px';
        if (isMobile) return '12px';
        return '0px';
      },
      position: 'relative',
      boxShadow: `0px -1px 4px 0px rgba(0, 0, 0, 0.25)`,
      height: '80px',
      borderTopRightRadius: isMobile ? '8px' : 0,
      borderTopLeftRadius: isMobile ? '8px' : 0
    }
  },

  icon: {
    style: {
      top: '-62px',
      marginRight: `${scrollbarWidth}px`,
      color: Design.Alias.Color.neutral100,
      borderRadius: 'var(--spacing-md, 24px)',
      background: 'var(--color-secondary-secondary600, #00649F)',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '10px',
      gap: '10px',
      width: '24px',
      height: '24px'
    }
  },
  barContent: {
    sx: {
      marginX: '104px',
      justifyContent: 'center',
      alignContent: 'center',
      maxWidth: '960px !important'
    }
  },
  buttonWrapper: {
    sx: {
      flexDirection: 'row',
      gap: Design.Primitives.Spacing.sm,
      justifyContent: isMobile ? 'center' : 'end',
      flexWrap: 'nowrap'
    }
  },
  buttons: {
    accept: {
      style: Design.Alias.Text.BodyUniversal.SmBold,
      sx: {
        width: '200px',
        backgroundColor: Design.Alias.Color.secondary500,
        position: 'relative'
      }
    },
    decline: {
      style: Design.Alias.Text.BodyUniversal.SmBold,
      sx: {
        color: Design.Alias.Color.secondary500,
        border: `1px solid ${Design.Alias.Color.secondary500}`,
        borderRadius: Design.Primitives.Spacing.xxxs,
        width: '200px',
        backgroundColor: Design.Alias.Color.neutral100,
        paddingT: Design.Primitives.Spacing.xxs,
        paddingX: Design.Primitives.Spacing.sm
      }
    }
  }
});
