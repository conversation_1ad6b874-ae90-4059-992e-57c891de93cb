import React from 'react';
import { Grid } from '@mui/material';
import { Button } from 'pods-component-library';
import { css } from 'pods-component-library/styled-system/css';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { FixedBottomContainer } from '../../FixedBottomContainer';
export interface ContinueButtonProps {
  onAccept: () => void;
  isMobile: boolean;
  isSigned: boolean;
  isProcessing: boolean;
}
export const ContinueButton = ({
  onAccept,
  isMobile,
  isSigned,
  isProcessing
}: ContinueButtonProps) => {
  const { t } = useTranslation();
  const handleAccept = () => {
    if (!isProcessing) {
      onAccept();
    }
  };

  const buttonCss = isMobile
    ? css.raw({ ...styles.continue_button.mobile })
    : css.raw({ ...styles.continue_button.desktop });

  const renderButton = () => (
    <Button
      variant="filled"
      buttonSize="large"
      color="primary"
      isDisabled={!isSigned}
      onPress={() => handleAccept()}
      isLoading={isProcessing}
      css={buttonCss}>
      {t(TranslationKeys.CommonComponents.CONTINUE_BUTTON)}
    </Button>
  );
  return (
    <Grid container>
      {isMobile ? <FixedBottomContainer>{renderButton()}</FixedBottomContainer> : renderButton()}
    </Grid>
  );
};

const styles = {
  continue_button: {
    desktop: {
      paddingY: '12px',
      paddingX: '40px',
      width: 'fit-content'
    },
    mobile: { width: '100%' }
  }
};
