import { Grid, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { theme } from '../../../PodsTheme';

export const RentalAgreementsTitle = () => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = titleStyles(isMobile);
  return (
    <Grid container {...styles.container}>
      <Grid container item xs={12} md={8} gap={2} flexDirection="column">
        <Grid item>
          <Typography {...styles.heading}>
            {translate(TranslationKeys.Onboarding.RentalAgreements.HEADER)}
          </Typography>
        </Grid>
        <Grid item>
          <Typography {...styles.subHeading}>
            {translate(TranslationKeys.Onboarding.RentalAgreements.SUBTITLE)}
          </Typography>
        </Grid>
      </Grid>
    </Grid>
  );
};

const titleStyles = (isMobile: boolean) => ({
  container: {
    sx: {
      flexDirection: 'column',
      alignContent: 'start',
      gap: Design.Primitives.Spacing.xxs,
      marginBottom: isMobile ? Design.Primitives.Spacing.sm : Design.Primitives.Spacing.md
    }
  },
  heading: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Xxl : Design.Alias.Text.Heading.Desktop.Xxl),
      color: Design.Alias.Color.accent900
    }
  },
  subHeading: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Sm : Design.Alias.Text.Heading.Desktop.Xs),
      fontWeight: 400,
      color: Design.Alias.Color.accent900
    }
  }
});
