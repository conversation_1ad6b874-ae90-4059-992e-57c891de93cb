import { Grid, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { theme } from '../../../PodsTheme';
import { OutstandingRentalAgreement } from '../../../networkRequests/responseEntities/AuthorizationEntities';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { OrderDocument } from '../../../domain/DocumentEntities';

const Tx = TranslationKeys.Onboarding.RentalAgreements;

export const RentalAgreementHeader = ({
  outstandingRentalAgreement,
  isInterFranchise
}: {
  outstandingRentalAgreement: OutstandingRentalAgreement | OrderDocument;
  isInterFranchise: boolean;
}) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t: translate } = useTranslation();

  const styles = headerStyles(isMobile);
  return (
    <Grid container {...styles.container}>
      <Typography {...styles.header}>
        {translate(Tx.RENTAL_AGREEMENT, {
          context: isInterFranchise ? 'interFranchise' : 'local'
        })}
      </Typography>
      <Typography style={Design.Alias.Text.BodyUniversal.Md}>
        ID# {outstandingRentalAgreement.orderId}
      </Typography>
    </Grid>
  );
};

const headerStyles = (isMobile: boolean) => ({
  container: {
    sx: {
      paddingTop: Design.Primitives.Spacing.md,
      paddingBottom: Design.Primitives.Spacing.sm,
      paddingX: Design.Primitives.Spacing.sm,
      justifyContent: 'space-between',
      display: 'flex',
      flexDirection: isMobile ? 'column' : 'row',
      gap: '8px'
    }
  },
  header: {
    style: isMobile ? Design.Alias.Text.Heading.Mobile.Lg : Design.Alias.Text.Heading.Desktop.Md,
    sx: {
      color: Design.Alias.Color.accent900
    }
  }
});
