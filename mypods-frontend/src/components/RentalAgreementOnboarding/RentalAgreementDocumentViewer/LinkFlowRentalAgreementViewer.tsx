import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { PageLayout } from '../../PageLayout';
import { theme } from '../../../PodsTheme';
import { OutstandingRentalAgreement } from '../../../networkRequests/responseEntities/AuthorizationEntities';
import { OrderDocument } from '../../../domain/DocumentEntities';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { BackHyperLink } from '../../buttons/BackHyperLink';
import { PodsReadyRoutes } from '../../../PodsReadyRoutes';
import { Customer } from '../../../networkRequests/responseEntities/CustomerEntities';
import { AgreementCheckbox } from './AgreementCheckbox';
import { ContinueButton } from './ContinueButton';

export interface LinkFlowRentalAgreementViewerProps {
  onAccept: () => void;
  rentalAgreements: (OutstandingRentalAgreement | OrderDocument)[];
  pdfUrls: string[];
  isProcessing: boolean;
  podsReadyTaskPage: boolean;
  customer: Customer;
}

const translationKeys = TranslationKeys.Onboarding.SignRentalAgreements;

export const LinkFlowRentalAgreementViewer = ({
  onAccept,
  rentalAgreements,
  pdfUrls,
  isProcessing,
  podsReadyTaskPage
}: LinkFlowRentalAgreementViewerProps) => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [checkedState, setCheckedState] = useState<boolean[]>(() =>
    new Array(rentalAgreements.length).fill(false)
  );

  const handleCheckboxChange = (index: number, checked: boolean) => {
    const newCheckedState = [...checkedState];
    newCheckedState[index] = checked;
    setCheckedState(newCheckedState);
  };

  const allAgreementsSigned = checkedState.length > 0 && checkedState.every((checked) => checked);

  const getPdfUrl = (index: number) => pdfUrls[index] || '';
  return (
    <Grid
      sx={{
        paddingX: isMobile ? '12px' : '104px',
        backgroundColor: Design.Alias.Color.neutral100,
        paddingTop: Design.Primitives.Spacing.lgPlus,
        overflowY: 'auto',
        justifyContent: 'center',
        display: 'flex',
        height: 'calc(100% - 80px)',
        zIndex: 999
      }}>
      <PageLayout columnsMd={12} columnsLg={8}>
        {podsReadyTaskPage && (
          <BackHyperLink
            route={PodsReadyRoutes.TASKS}
            label={t(TranslationKeys.Onboarding.RETURN_TO_TASK_BUTTON)}
            style={{ ...styles.backlink }}
          />
        )}
        <div style={isMobile ? { marginTop: '16px' } : styles.agreementPanel}>
          <Grid container {...styles.mainPanel}>
            <Typography variant="h1">{t(translationKeys.HEADER)}</Typography>
            <Typography sx={{ ...Design.Alias.Text.BodyUniversal.Sm }}>
              {t(translationKeys.SUBTITLE)}
            </Typography>

            <div>
              {rentalAgreements.map((agreement, index) => (
                <div key={index}>
                  <AgreementCheckbox
                    checked={checkedState[index]}
                    onChange={(e) => handleCheckboxChange(index, e.target.checked)}
                    pdfUrl={getPdfUrl(index)}
                    orderId={agreement.orderId}
                  />
                </div>
              ))}
            </div>
          </Grid>
          <Grid container {...styles.buttons}>
            <ContinueButton
              onAccept={onAccept}
              isMobile={isMobile}
              isSigned={allAgreementsSigned}
              isProcessing={isProcessing}
            />
          </Grid>
        </div>
      </PageLayout>
    </Grid>
  );
};

const styles = {
  agreementPanel: {
    marginTop: 'calc(50% - 200px)',
    borderRadius: '8px',
    borderStyle: 'solid',
    borderWidth: '1px',
    borderColor: Design.Alias.Color.neutral300,
    backgroundColor: Design.Alias.Color.neutral100,
    boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)',
    padding: '32px'
  },
  backlink: {
    display: 'flex',
    alignItems: 'center',
    textDecoration: 'none',
    color: 'inherit',
    position: 'relative',
    left: '-7px',
    marginBottom: '16px'
  },
  mainPanel: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.sm,
      maxWidth: '650px'
    }
  },
  buttons: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: '2rem',
      marginTop: '2rem'
    }
  }
};
