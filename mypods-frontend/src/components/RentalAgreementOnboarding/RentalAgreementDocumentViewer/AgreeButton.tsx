import React from 'react';
import { Grid } from '@mui/material';
import { Button } from 'pods-component-library';
import { css } from 'pods-component-library/styled-system/css';
import { useTranslation } from 'react-i18next';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { FixedBottomContainer } from '../../FixedBottomContainer';
export interface AgreeButtonProps {
  onAccept: () => void;
  isMobile: boolean;
  isSigned: boolean;
  isProcessing: boolean;
}
export const AgreeButton = ({ onAccept, isMobile, isSigned, isProcessing }: AgreeButtonProps) => {
  const { t } = useTranslation();
  const handleAccept = () => {
    if (!isProcessing) {
      onAccept();
    }
  };

  const buttonCss = isMobile
    ? css.raw({ ...styles.agree_button.mobile })
    : css.raw({ ...styles.agree_button.desktop });

  const renderButton = () => (
    <Button
      variant="filled"
      buttonSize="large"
      color="secondary"
      isDisabled={!isSigned}
      onPress={() => handleAccept()}
      isLoading={isProcessing}
      css={buttonCss}>
      {t(TranslationKeys.MothInspectionFormPage.AGREE_BUTTON)}
    </Button>
  );
  return (
    <Grid container>
      {isMobile ? <FixedBottomContainer>{renderButton()}</FixedBottomContainer> : renderButton()}
    </Grid>
  );
};

const styles = {
  agree_button: {
    desktop: {
      paddingY: '12px',
      paddingX: '40px',
      width: 'fit-content'
    },
    mobile: { width: '100%' }
  }
};
