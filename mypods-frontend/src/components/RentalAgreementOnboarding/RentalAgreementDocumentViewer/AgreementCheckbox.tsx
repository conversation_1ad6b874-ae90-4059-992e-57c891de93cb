import { Checkbox, FormControlLabel, Link, Typography } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Design } from '../../../helpers/Design';
import { TranslationKeys } from '../../../locales/TranslationKeys';

interface AgreementCheckboxProps {
  checked: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  pdfUrl: string;
  orderId: string;
}
const translationKeys = TranslationKeys.Onboarding.SignRentalAgreements.LinkFlowAgreementViewer;

export const AgreementCheckbox = ({
  checked,
  onChange,
  pdfUrl,
  orderId
}: AgreementCheckboxProps) => {
  const { t } = useTranslation();

  return (
    <FormControlLabel
      control={<Checkbox checked={checked} onChange={onChange} color="secondary" />}
      label={
        <Typography sx={{ ...Design.Alias.Text.BodyUniversal.Sm }}>
          {t(translationKeys.CHECKBOX_LABEL_START)}{' '}
          <Link
            href={pdfUrl}
            target="_blank"
            underline="hover"
            color="secondary"
            sx={{ fontWeight: 'bold' }}>
            {t(translationKeys.RENTAL_AGREEMENT_LINK_TEXT)}
          </Link>{' '}
          {t(translationKeys.CHECKBOX_LABEL_END_WITH_ORDER_ID, { orderId })}
        </Typography>
      }
      sx={{ mb: 2 }}
    />
  );
};
