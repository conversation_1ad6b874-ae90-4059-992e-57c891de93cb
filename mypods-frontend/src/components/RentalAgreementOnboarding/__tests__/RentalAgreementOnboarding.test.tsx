import { RentalAgreementOnboarding } from '../RentalAgreementOnboarding';
import { screen, waitFor } from '@testing-library/react';
import { renderWithPoetProvidersAndState, testQueryClient } from '../../../testUtils/RenderHelpers';
import React from 'react';
import {
  mockAcceptRentalAgreement,
  mockGetCustomer,
  mockGetOrderDocuments,
  mockRefreshSession
} from '../../../../setupTests';
import {
  createCustomer,
  createOrderDocument,
  createRefreshSessionClaims,
  mockIsESignRentalAgreementEnabled,
  mockIsPodsReadySingleOrderEnabled
} from '../../../testUtils/MyPodsFactories';
import { TestOutstandingDocumentViewerViews } from '../../../testUtils/testComponents/TestRentalAgreementViewer';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { vi } from 'vitest';
import { CORPORATE_RENTAL_AGREEMENT } from '../../../networkRequests/MyPodsConstants';
import { getTimezoneOffset } from '../../../helpers/dateHelpers';
import { Customer } from '../../../networkRequests/responseEntities/CustomerEntities';
import { QueryClient } from '@tanstack/react-query';
import { SplitEventType } from '../../../config/SplitEventTypes';

describe('RentalAgreementOnboarding', () => {
  let user: UserEvent;
  let customer: Customer;
  let queryClient: QueryClient;
  const pdfUrl =
    'https://salrseuspodsmypodsnp01.blob.core.windows.net/mypods/rental-agreements/Corporate_Rental_Agreement.pdf';
  const firstRentalAgreement = createOrderDocument({
    docType: 'RENTAL_AGREEMENT',
    docStatus: 'SENT',
    billingCompanyCode: '1234',
    docPath: '/firstRentalAgreement.pdf',
    docName: 'firstRentalAgreement.pdf',
    isInterFranchise: true
  });
  const mothForm = createOrderDocument({
    docType: 'SPONGY_MOTH_FORM',
    docStatus: 'SENT',
    billingCompanyCode: '1234',
    docPath: '/firstRentalAgreement.pdf',
    docName: 'firstRentalAgreement.pdf',
    isInterFranchise: true,
    id: 'mothId'
  });

  const mockSendSplitEvent = vi.hoisted(() => vi.fn());
  vi.mock('../../../config/useSplitEvents', async () => ({
    useSplitEvents: () => ({
      send: mockSendSplitEvent
    })
  }));

  const mockLogger = vi.hoisted(() => vi.fn());
  vi.mock('@datadog/browser-logs', () => {
    return {
      datadogLogs: {
        logger: {
          log: mockLogger
        }
      }
    };
  });

  beforeEach(() => {
    mockGetCustomer.mockResolvedValue(createCustomer());
    queryClient = testQueryClient();
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims());
    user = userEvent.setup();
  });

  // Children are the SiteLayout & Routes
  function renderSubject() {
    customer = createCustomer();
    mockGetCustomer.mockResolvedValue(customer);

    return renderWithPoetProvidersAndState(
      <RentalAgreementOnboarding>
        <div>Children</div>
      </RentalAgreementOnboarding>,
      {
        customQueryClient: queryClient
      }
    );
  }

  const expectChildrenToBeRendered = () => screen.getByText('Children');

  const expectChildrenToNotBeRendered = () =>
    expect(screen.queryByText('Children')).not.toBeInTheDocument();

  describe('when there are no outstanding rental agreements', () => {
    beforeEach(() => {
      mockGetOrderDocuments.mockResolvedValue([]);
    });

    it('renders the app, as a child component', async () => {
      renderSubject();

      await waitFor(async () => {
        expectChildrenToBeRendered();
      });
    });
  });

  describe('when there are unsigned rental agreements', () => {
    beforeEach(() => {
      mockRefreshSession.mockResolvedValue(createRefreshSessionClaims);
      mockGetOrderDocuments.mockResolvedValue({
        documents: [firstRentalAgreement]
      });
    });

    it('should render document viewer', async () => {
      renderSubject();

      expectChildrenToNotBeRendered();
      expect(await TestOutstandingDocumentViewerViews.button()).toBeInTheDocument();
      expect(await TestOutstandingDocumentViewerViews.pdfLink()).toHaveAttribute('href', pdfUrl);
    });

    it('should render universal rental agreement for all IF moves', async () => {
      renderSubject();

      expectChildrenToNotBeRendered();

      expect(await TestOutstandingDocumentViewerViews.button()).toBeInTheDocument();
      expect(await TestOutstandingDocumentViewerViews.pdfLink()).toHaveAttribute(
        'href',
        CORPORATE_RENTAL_AGREEMENT
      );
    });

    it('should render franchise rental agreement if given a specific franchise company code', async () => {
      renderSubject();

      expectChildrenToNotBeRendered();
      expect(await TestOutstandingDocumentViewerViews.button()).toBeInTheDocument();
      expect(await TestOutstandingDocumentViewerViews.pdfLink()).toHaveAttribute(
        'href',
        CORPORATE_RENTAL_AGREEMENT
      );
    });

    it('should render the application as a child after accepting rental agreement', async () => {
      const expectedRequest = {
        orderId: firstRentalAgreement.orderId,
        identity: 'CONTRACT',
        companyCode: 'PEIU',
        firstName: customer.firstName,
        lastName: customer.lastName,
        docId: firstRentalAgreement.id,
        docTitle: 'Local Rental Agreement',
        ...getTimezoneOffset()
      };
      renderSubject();

      await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));

      expect(mockAcceptRentalAgreement).toHaveBeenCalledWith(expectedRequest);
      expectChildrenToBeRendered();
    });

    it('should render a notification in a snackbar if accepting the rental agreement fails', async () => {
      mockAcceptRentalAgreement.mockRejectedValue(new Error('Failure'));
      renderSubject();

      await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));

      expectChildrenToNotBeRendered();
      const genericfailure = TranslationKeys.CommonComponents.Notification.GENERIC_FAILURE;
      expect(screen.getByText(genericfailure)).toBeInTheDocument();
    });

    it('should render children after accepting multiple rental agreements', async () => {
      mockGetOrderDocuments.mockResolvedValue({
        documents: [
          firstRentalAgreement,
          createOrderDocument({ id: 'new', docType: 'RENTAL_AGREEMENT', docStatus: 'SENT' })
        ]
      });
      renderSubject();

      await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));

      expect(mockAcceptRentalAgreement).toHaveBeenCalledTimes(1);
      expectChildrenToNotBeRendered();

      await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));
      expect(mockAcceptRentalAgreement).toHaveBeenCalledTimes(2);
      expectChildrenToBeRendered();
    });

    it('should render e-sign form when isESignRentalAgreementEnabled returns true', async () => {
      mockIsESignRentalAgreementEnabled.mockResolvedValue(true);
      mockGetOrderDocuments.mockResolvedValue({
        documents: [
          firstRentalAgreement,
          createOrderDocument({ id: 'new', docType: 'RENTAL_AGREEMENT', docStatus: 'SENT' })
        ]
      });

      renderSubject();

      expect(
        await screen.findByText(TranslationKeys.Onboarding.SignRentalAgreements.HEADER)
      ).toBeInTheDocument();
    });
  });

  it('should not send PODS_READY_COMPLETE if there is an outstanding moth form render children after accepting multiple rental agreements', async () => {
    mockIsPodsReadySingleOrderEnabled.mockReturnValue(false);
    const outstandingRentalAgreement2 = {
      ...firstRentalAgreement,
      orderId: '999888777',
      id: 'anotherId'
    };
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims);
    mockGetOrderDocuments.mockResolvedValue({
      documents: [firstRentalAgreement, outstandingRentalAgreement2, mothForm]
    });
    renderSubject();

    await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));
    await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));

    const numPodsReadyStartCalls = mockSendSplitEvent.mock.calls.filter(
      (call) => call[0] == SplitEventType.PODS_READY_START
    ).length;
    expect(numPodsReadyStartCalls).toEqual(1);
    const numPodsReadyCompleteCalls = mockSendSplitEvent.mock.calls.filter(
      (call) => call[0] == SplitEventType.PODS_READY_COMPLETE
    ).length;
    expect(numPodsReadyCompleteCalls).toEqual(0);
  });

  it('should send PODS_READY_COMPLETE if there is not outstanding moth forms after accepting rental agreement', async () => {
    mockIsPodsReadySingleOrderEnabled.mockReturnValue(false);
    mockRefreshSession.mockResolvedValue(createRefreshSessionClaims);
    mockGetOrderDocuments.mockResolvedValue({
      documents: [firstRentalAgreement]
    });
    renderSubject();

    await waitFor(async () => user.click(await TestOutstandingDocumentViewerViews.button()));

    const numPodsReadyCompleteCalls = mockSendSplitEvent.mock.calls.filter(
      (call) => call[0] == SplitEventType.PODS_READY_COMPLETE
    ).length;
    expect(numPodsReadyCompleteCalls).toEqual(1);
  });
});
