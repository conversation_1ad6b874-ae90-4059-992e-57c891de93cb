import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import { renderWithPoetProvidersAndState } from '../../../testUtils/RenderHelpers';
import { mockGetCustomer, mockNavigate, mockRefreshSession } from '../../../../setupTests';
import {
  createCustomer,
  createOutstandingRentalAgreement,
  createRefreshSessionClaims
} from '../../../testUtils/MyPodsFactories';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { LinkFlowRentalAgreementViewer } from '../RentalAgreementDocumentViewer/LinkFlowRentalAgreementViewer';
import { PodsReadyRoutes } from '../../../PodsReadyRoutes';
import { OutstandingRentalAgreement } from '../../../networkRequests/responseEntities/AuthorizationEntities';

const ORDER_ID = '12349876';
const CUSTOMER_ID = '667';
const FIRST_NAME = 'First!';
const LAST_NAME = `Last!`;
const pdfUrls = [
  'https://salrseuspodsmypodsnp01.blob.core.windows.net/mypods/rental-agreements/Corporate_Rental_Agreement.pdf'
];
const mockOnAccept = vi.fn();
const rentalAgreement = [createOutstandingRentalAgreement({ orderId: ORDER_ID })];
const jwtCustomer = createRefreshSessionClaims({
  firstName: FIRST_NAME,
  lastName: LAST_NAME,
  customerId: CUSTOMER_ID
});
const customer = createCustomer({ ...jwtCustomer, id: CUSTOMER_ID });

describe('LinkFlowRentalAgreementViewer', () => {
  let user: UserEvent;

  const renderSubject = async (options?: {
    current?: number;
    total?: number;
    isProcessing?: boolean;
    returnToPodsReadyTaskPage?: boolean;
    rentalAgreements?: OutstandingRentalAgreement[];
    pdfUrls?: string[];
  }) => {
    renderWithPoetProvidersAndState(
      <LinkFlowRentalAgreementViewer
        onAccept={mockOnAccept}
        pdfUrls={options?.pdfUrls ?? pdfUrls}
        rentalAgreements={options?.rentalAgreements ?? rentalAgreement}
        isProcessing={options?.isProcessing ?? false}
        podsReadyTaskPage={options?.returnToPodsReadyTaskPage ?? false}
        customer={customer}
      />
    );
    await screen.findByText(TranslationKeys.Onboarding.SignRentalAgreements.HEADER);
  };

  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(jwtCustomer);
    mockGetCustomer.mockResolvedValue(customer);
    user = userEvent.setup();
  });

  it('renders checkbox and disabled Continue button initially', async () => {
    await renderSubject();

    expect(
      await screen.findByText(TranslationKeys.Onboarding.SignRentalAgreements.HEADER)
    ).toBeInTheDocument();
    expect(await screen.findByRole('checkbox')).not.toBeChecked();

    const continueButton = await screen.findByRole('button', {
      name: TranslationKeys.CommonComponents.CONTINUE_BUTTON
    });
    expect(continueButton).toBeInTheDocument();
    expect(continueButton).toBeDisabled();
  });

  it('enables Continue button when checkbox is checked and calls onAccept when clicked', async () => {
    await renderSubject();

    const checkbox = await screen.findByRole('checkbox');
    await waitFor(() => user.click(checkbox));
    expect(checkbox).toBeChecked();

    const continueButton = await screen.findByRole('button', {
      name: TranslationKeys.CommonComponents.CONTINUE_BUTTON
    });
    expect(continueButton).toBeEnabled();

    await waitFor(() => user.click(continueButton));
    expect(mockOnAccept).toHaveBeenCalled();
  });

  it('ignores click on Continue button when isProcessing is true', async () => {
    await renderSubject({ isProcessing: true });

    const checkbox = await screen.findByRole('checkbox');
    await waitFor(() => user.click(checkbox));
    expect(checkbox).toBeChecked();

    const continueButton = await screen.findByRole('button', {
      name: TranslationKeys.CommonComponents.CONTINUE_BUTTON
    });
    expect(continueButton).toBeEnabled();

    await waitFor(() => user.click(continueButton));
    expect(mockOnAccept).not.toHaveBeenCalled();
  });

  it('if multiple rental agreements, only allow continue to be clicked if all boxes are checked', async () => {
    const rentalAgreements = [
      createOutstandingRentalAgreement({ orderId: '12345', companyCode: 'ABC' }),
      createOutstandingRentalAgreement({ orderId: '67890', companyCode: 'XYZ' })
    ];

    const pdfUrls = ['https://example.com/agreement1.pdf', 'https://example.com/agreement2.pdf'];

    await renderSubject({
      rentalAgreements,
      pdfUrls
    });

    const checkboxes = await screen.findAllByRole('checkbox');
    expect(checkboxes).toHaveLength(2);

    await waitFor(() => user.click(checkboxes[0]));
    expect(checkboxes[0]).toBeChecked();
    expect(checkboxes[1]).not.toBeChecked();

    const continueButton = await screen.findByRole('button', {
      name: TranslationKeys.CommonComponents.CONTINUE_BUTTON
    });
    expect(continueButton).toBeDisabled();

    await waitFor(() => user.click(checkboxes[1]));
    expect(checkboxes[1]).toBeChecked();

    expect(continueButton).toBeEnabled();

    await waitFor(() => user.click(continueButton));
    expect(mockOnAccept).toHaveBeenCalled();
  });

  describe('returnToPodsReadyTaskPage is true', () => {
    it('shows back to Your Tasks', async () => {
      await renderSubject({ returnToPodsReadyTaskPage: true });

      expect(
        await screen.findByText(TranslationKeys.Onboarding.RETURN_TO_TASK_BUTTON)
      ).toBeInTheDocument();
    });

    it('clicking Your Tasks navigates to task page', async () => {
      await renderSubject({ returnToPodsReadyTaskPage: true });

      const backButton = await screen.findByText(TranslationKeys.Onboarding.RETURN_TO_TASK_BUTTON);
      await waitFor(() => user.click(backButton));

      expect(mockNavigate).toHaveBeenCalledWith(PodsReadyRoutes.TASKS);
    });
  });
});
