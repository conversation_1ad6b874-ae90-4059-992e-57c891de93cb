import { useGtmEventsWithCustomer } from '../../config/google/useGtmEvents';
import {
  createGtmErrorRequest,
  GA_GENERIC_BACKEND_MESSAGE
} from '../../config/google/googleAnalyticsUtils';
import {
  gtmAgreementTypeFromIsInterfranchise,
  viewAgreementStep
} from '../../config/google/GoogleEntities';
import { useSplitEvents } from '../../config/useSplitEvents';
import { SplitEventType } from '../../config/SplitEventTypes';
import { Customer } from '../../networkRequests/responseEntities/CustomerEntities';

interface RentalAgreementEventsProps {
  isInterFranchise: boolean;
  orderId: string;
  customer: Customer;
}

export const RentalAgreementEvents = (props: RentalAgreementEventsProps) => {
  const { orderId, customer, isInterFranchise } = props;
  const agreementType = gtmAgreementTypeFromIsInterfranchise(isInterFranchise);
  const gtmEvents = useGtmEventsWithCustomer(props.customer);

  const splitEvents = useSplitEvents(customer.id);

  const startViewingAgreement = () => {
    gtmEvents.startAgreement(agreementType, orderId);
    gtmEvents.viewAgreementStep(
      viewAgreementStep(agreementType, 'view_and_sign_rental_agreement'),
      orderId
    );
    splitEvents.send(SplitEventType.RENTAL_AGREEMENT_START);
  };

  const submitAgreement = () => {
    gtmEvents.submitAgreement(agreementType, orderId);
    splitEvents.send(SplitEventType.RENTAL_AGREEMENT_SUBMIT_AGREEMENT);
  };

  const submitSuccess = () => {
    gtmEvents.successAgreement(agreementType, orderId);
    splitEvents.send(SplitEventType.RENTAL_AGREEMENT_SUBMIT_SUCCESS);
  };

  const submitFailure = () => {
    gtmEvents.errorEvent(
      createGtmErrorRequest(GA_GENERIC_BACKEND_MESSAGE, agreementType, 'backend')
    );
    splitEvents.send(SplitEventType.RENTAL_AGREEMENT_SUBMIT_FAILURE);
  };

  return {
    startViewingAgreement,
    submitAgreement,
    submitSuccess,
    submitFailure
  };
};
