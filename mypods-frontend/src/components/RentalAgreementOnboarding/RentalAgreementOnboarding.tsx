import React, { ReactNode, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useShowPodsReadySingleOrder } from '../../helpers/useShowPodsReadySingleOrder';
import { RentalAgreementOnboardingPage } from './RentalAgreementOnboardingPage';
import { PodsReadyRoutes } from '../../PodsReadyRoutes';

type RentalAgreementOnboardingProps = { children?: ReactNode };

export const RentalAgreementOnboarding = ({ children }: RentalAgreementOnboardingProps) => {
  const navigate = useNavigate();
  const { showPodsReadySingleOrder, isFetching } = useShowPodsReadySingleOrder();

  useEffect(() => {
    if (isFetching) return;

    if (showPodsReadySingleOrder) {
      navigate(PodsReadyRoutes.TASKS, { replace: true });
    }
  }, [showPodsReadySingleOrder, isFetching]);

  return <RentalAgreementOnboardingPage>{children}</RentalAgreementOnboardingPage>;
};
