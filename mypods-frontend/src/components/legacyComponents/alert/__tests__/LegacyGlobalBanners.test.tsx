import { screen, waitFor, within } from '@testing-library/react';
import { LegacyGlobalBanners } from '../LegacyGlobalBanners';
import {
  mockLegacyGetCustomer,
  mockLegacySignFnpsWaiver,
  mockNavigate,
  mockRefreshSession
} from '../../../../../setupTests';
import {
  createCustomer,
  createEntryPointResult,
  createOutstandingFnpsAgreement,
  createOutstandingMothAgreement,
  createRefreshSessionClaims,
  createSignFnpsRequest
} from '../../../../testUtils/MyPodsFactories';
import {
  renderWithLegacyProvidersAndState,
  testQueryClient
} from '../../../../testUtils/RenderHelpers';
import React from 'react';
import { DISMISS_ICON_ID } from '../../../icons/DismissXIcon';
import userEvent from '@testing-library/user-event';
import { ROUTES } from '../../../../Routes';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { initialEntryPointState } from '../../../../context/EntryPointContext';
import {
  EntryPointResult,
  RefreshSessionClaims
} from '../../../../networkRequests/responseEntities/AuthorizationEntities';
import { BannerContext } from '../../../../locales/TranslationConstants';
import { QueryClient } from '@tanstack/react-query';
import { addSignedMothOrderId } from '../../../../helpers/entrypointHelpers';
import { Customer } from '../../../../networkRequests/responseEntities/CustomerEntities';
import {
  getWeightTicketBanner,
  setOrdersWithSignedMothAgreements,
  setWeightTicketBanner
} from '../../../../helpers/storageHelpers';
import { SplitEventType } from '../../../../config/SplitEventTypes';

const mockSendSplitEvent = vi.hoisted(() => vi.fn());
vi.mock('../../../../config/useSplitEvents', async () => ({
  useSplitEvents: () => ({
    send: mockSendSplitEvent
  })
}));

describe('Global Banners', () => {
  let queryClient: QueryClient;
  const customer = createCustomer();
  const orderId = '123';
  const Tx = TranslationKeys.CommonComponents.Banner;
  let sessionClaims: RefreshSessionClaims = createRefreshSessionClaims();

  beforeEach(() => {
    queryClient = testQueryClient();
  });

  async function renderBanners(
    entryPointResult: EntryPointResult,
    initialCustomer: Customer = customer
  ) {
    mockLegacyGetCustomer.mockResolvedValue(initialCustomer);
    mockRefreshSession.mockResolvedValue(sessionClaims);

    return renderWithLegacyProvidersAndState(<LegacyGlobalBanners />, {
      entryPointState: {
        ...initialEntryPointState,
        entryPointResult: entryPointResult
      },
      initialCustomer: initialCustomer,
      customQueryClient: queryClient,
      initialEntries: ['/']
    });
  }

  afterEach(() => {
    setOrdersWithSignedMothAgreements(null);
    setWeightTicketBanner(null);
  });

  describe('Weight Tickets', () => {
    const withWeightTicket = createEntryPointResult({
      hasWeightTickets: true
    });

    const renderGlobalBannerWithWeightTickets = async () => {
      return await renderBanners(withWeightTicket);
    };

    it('should display banner for weight tickets', async () => {
      await renderGlobalBannerWithWeightTickets();

      expect(
        await screen.findByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.MESSAGE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.CTA_LABEL}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).toBeInTheDocument();
    });

    it('should navigate to document page on button click', async () => {
      await renderGlobalBannerWithWeightTickets();

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', { name: `${Tx.CTA_LABEL}[reviewDocuments]` })
        );
      });
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.DOCUMENT);
    });

    it('should not render weight ticket banner if a weight ticket does not exist for a customer', async () => {
      const noWeightTicket = createEntryPointResult({
        hasWeightTickets: false
      });
      await renderBanners(noWeightTicket);

      expect(
        screen.queryByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).not.toBeInTheDocument();
    });

    it('should dismiss banner on X click', async () => {
      await renderGlobalBannerWithWeightTickets();

      await waitFor(async () => {
        await userEvent.click(screen.getByTestId(DISMISS_ICON_ID));
      });
      expect(
        screen.queryByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).not.toBeInTheDocument();
      expect(getWeightTicketBanner()).toBeTruthy();
    });

    it('should not render weight ticket banner if a weight ticket does exist but has been dismissed', async () => {
      setWeightTicketBanner(true);

      await renderGlobalBannerWithWeightTickets();

      expect(
        screen.queryByText(`${Tx.TITLE}[${BannerContext.REVIEW_DOCUMENTS}]`)
      ).not.toBeInTheDocument();
    });
  });

  describe('Moth Agreements', () => {
    const mothAgreement = createEntryPointResult({
      outstandingMothAgreements: [createOutstandingMothAgreement({ orderId })]
    });

    it('should display banner for moth agreements', async () => {
      await renderBanners(mothAgreement);

      expect(
        await screen.findByText(`${Tx.TITLE}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.MESSAGE}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.CTA_LABEL}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).toBeInTheDocument();
    });

    it('should navigate to the first page of the moth form on button click', async () => {
      await renderBanners(mothAgreement);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', { name: `${Tx.CTA_LABEL}[${BannerContext.MOTH_AGREEMENTS}]` })
        );
      });
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.MOTH_FLY_INSPECTION);
    });

    it('should send a split event on button click', async () => {
      await renderBanners(mothAgreement);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', { name: `${Tx.CTA_LABEL}[${BannerContext.MOTH_AGREEMENTS}]` })
        );
      });
      expect(mockSendSplitEvent).toHaveBeenCalledWith(SplitEventType.MOTH_FORM_START);
    });

    it('should not display a banner if user has already signed it', async () => {
      addSignedMothOrderId(orderId);
      await renderBanners(mothAgreement);

      expect(
        screen.queryByText(`${Tx.TITLE}[${BannerContext.MOTH_AGREEMENTS}]`)
      ).not.toBeInTheDocument();
    });
  });

  describe('Fragile Non-Paved Surface Agreement', () => {
    const moveId = '123';
    const outstandingFnpsAgreement1 = createOutstandingFnpsAgreement({ orderId, moveId });
    const outstandingFnpsAgreement2 = createOutstandingFnpsAgreement({
      orderId: '456',
      moveId: 'm456'
    });
    const surfaceAgreement = createEntryPointResult({
      outstandingFnpsAgreements: [outstandingFnpsAgreement1]
    });
    const multipleFnpsAgreements = createEntryPointResult({
      outstandingFnpsAgreements: [outstandingFnpsAgreement1, outstandingFnpsAgreement2]
    });

    it('should display a banner for surface agreements', async () => {
      await renderBanners(surfaceAgreement);
      expect(
        await screen.findByText(`${Tx.TITLE}[${BannerContext.FNPS_AGREEMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.MESSAGE}[${BannerContext.FNPS_AGREEMENTS}]`)
      ).toBeInTheDocument();
      expect(
        await screen.findByText(`${Tx.CTA_LABEL}[${BannerContext.FNPS_AGREEMENTS}]`)
      ).toBeInTheDocument();
    });

    it('should display the pagination for FNPS waiver', async () => {
      await renderBanners(multipleFnpsAgreements);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByText(
            `${TranslationKeys.CommonComponents.Banner.CTA_LABEL}[${BannerContext.FNPS_AGREEMENTS}]`
          )
        );
      });

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      const paginationContainer = await screen.findByTestId('pods-modal-pagination');

      expect(
        within(paginationContainer).getByText(
          TranslationKeys.CommonComponents.PROGRESS_COUNTER +
            `[${1},${multipleFnpsAgreements.outstandingFnpsAgreements.length}]`
        )
      ).toBeInTheDocument();

      expect(screen.getByText(TranslationKeys.CommonComponents.NEXT_BUTTON)).toBeInTheDocument();
    });

    it('should successfully submit fnps waiver on button click', async () => {
      await renderBanners(surfaceAgreement);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByText(
            `${TranslationKeys.CommonComponents.Banner.CTA_LABEL}[${BannerContext.FNPS_AGREEMENTS}]`
          )
        );
        await userEvent.click(
          screen.getByRole('button', {
            name: TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
          })
        );
        await userEvent.click(
          screen.getByRole('button', { name: TranslationKeys.FnpsModal.ACTION_BUTTON_LABEL })
        );
      });

      const expectedRequest = createSignFnpsRequest({
        orderId: outstandingFnpsAgreement1.orderId,
        moveId: moveId,
        firstName: customer.firstName,
        lastName: customer.lastName
      });

      expect(mockLegacySignFnpsWaiver).toHaveBeenCalledWith(expectedRequest);
      expect(
        screen.getByText(TranslationKeys.CommonComponents.Notification.SIGN_FNPS_WAIVER_SUCCEEDED)
      ).toBeInTheDocument();
      // TODO: Figure out a way to test that the banner has been removed
    });

    it('should display error alert if signing fnps waiver fails', async () => {
      mockLegacySignFnpsWaiver.mockRejectedValue({});
      await renderBanners(surfaceAgreement);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByText(
            `${TranslationKeys.CommonComponents.Banner.CTA_LABEL}[${BannerContext.FNPS_AGREEMENTS}]`
          )
        );
        await userEvent.click(
          screen.getByRole('button', {
            name: TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
          })
        );
        await userEvent.click(
          screen.getByRole('button', { name: TranslationKeys.FnpsModal.ACTION_BUTTON_LABEL })
        );
      });
      expect(screen.getByText(TranslationKeys.FnpsModal.ALERT_TITLE)).toBeInTheDocument();
      expect(screen.getByText(TranslationKeys.FnpsModal.ALERT_DESCRIPTION)).toBeInTheDocument();
    });
  });

  describe('Desynced Emails Banner', () => {
    const entrypoint = createEntryPointResult();

    it('should not display a banner, when a customer is unconverted', async () => {
      const unconvertedCustomer = createCustomer({
        isConverted: false,
        email: { id: 1, address: '<EMAIL>' },
        username: '<EMAIL>'
      });

      await renderBanners(entrypoint, unconvertedCustomer);

      expect(
        screen.queryByText(
          `${TranslationKeys.CommonComponents.Banner.TITLE}[${BannerContext.DESYNCED_EMAILS}]`
        )
      ).toBeNull();
    });

    it('should not display a banner, when a customer is converted & has matching emails', async () => {
      const convertedCustomer = createCustomer({
        isConverted: true,
        email: { id: 1, address: '<EMAIL>' },
        username: '<EMAIL>'
      });

      await renderBanners(entrypoint, convertedCustomer);

      expect(
        screen.queryByText(
          `${TranslationKeys.CommonComponents.Banner.TITLE}[${BannerContext.DESYNCED_EMAILS}]`
        )
      ).toBeNull();
    });

    it("should display a banner, when a customer is converted, but their email & username don't match", async () => {
      const desyncedCustomer = createCustomer({
        isConverted: true,
        email: { id: 1, address: '<EMAIL>' },
        username: '<EMAIL>'
      });

      await renderBanners(entrypoint, desyncedCustomer);

      expect(
        await screen.findByText(
          `${TranslationKeys.CommonComponents.Banner.TITLE}[${BannerContext.DESYNCED_EMAILS}]`
        )
      ).toBeInTheDocument();
    });

    it('should navigate to account page on button click', async () => {
      const desyncedCustomer = createCustomer({
        isConverted: true,
        email: { id: 1, address: '<EMAIL>' },
        username: '<EMAIL>'
      });

      await renderBanners(entrypoint, desyncedCustomer);

      await waitFor(async () => {
        await userEvent.click(
          screen.getByRole('button', { name: `${Tx.CTA_LABEL}[desyncedEmails]` })
        );
      });
      expect(mockNavigate).toHaveBeenCalledWith(ROUTES.ACCOUNT);
    });
  });
});
