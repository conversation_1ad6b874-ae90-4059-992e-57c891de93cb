import { renderWithTheme } from '../../../../testUtils/RenderHelpers';
import { LegacyFooter } from '../LegacyFooter';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TranslationKeys } from '../../../../locales/TranslationKeys';
import { vi } from 'vitest';
import { mockTransFn } from '../../../../testUtils/mock-react-i18next';
import { act } from 'react';

// -- Translation-Specific Mocks --
const mockedChangeLanguage = vi.hoisted(() => vi.fn());
const mockedLanguage = vi.hoisted(() => vi.fn().mockReturnValue('en_us'));
const mockTransComponent = vi.hoisted(() => vi.fn());
vi.mock('react-i18next', async () => ({
  useTranslation: () => {
    const t = (key: string) => key;
    const i18n = {
      changeLanguage: mockedChangeLanguage,
      get language() {
        return mockedLanguage();
      }
    };
    return { t, i18n };
  },
  Trans: mockTransComponent
}));

// -- views --

const views = {
  usEnglishMenuOption: () => screen.queryByText(TranslationKeys.Language.US_ENGLISH_LABEL),
  frenchCanadianMenuOption: () => screen.queryByText(TranslationKeys.Language.CA_FRENCH_LABEL)
};
describe('Footer', () => {
  beforeEach(() => {
    mockTransComponent.mockImplementation(mockTransFn);
  });

  const renderFooter = async () => {
    renderWithTheme(<LegacyFooter />);
    // conveniently await something to load before testing, and can use getBys
    await screen.findByRole('link', {
      name: TranslationKeys.Footer.TERMS_AND_CONDITIONS
    });
  };

  it('links to the legal pages', async () => {
    await renderFooter();

    const termsLink: HTMLAnchorElement = (await screen.findByRole('link', {
      name: TranslationKeys.Footer.TERMS_AND_CONDITIONS
    })) as any;

    const privacyPolicyLink: HTMLAnchorElement = screen.getByRole('link', {
      name: TranslationKeys.Footer.PRIVACY_POLICY
    }) as any;

    expect(termsLink.href).toContain(TranslationKeys.Footer.TERMS_AND_CONDITIONS_URL);
    expect(privacyPolicyLink.href).toContain(TranslationKeys.Footer.PRIVACY_POLICY_URL);
  });

  describe('the language selector', () => {
    it('displays English as the default localization option', async () => {
      await renderFooter();

      expect(views.usEnglishMenuOption()).toBeInTheDocument();
      expect(views.frenchCanadianMenuOption()).not.toBeInTheDocument();
      expect(mockedChangeLanguage).toHaveBeenCalledWith('en_us');
    });

    it('allows French to be selected as a localization option', async () => {
      renderWithTheme(<LegacyFooter />);

      const combobox = await screen.findByRole('combobox');
      expect(combobox).toHaveTextContent(TranslationKeys.Language.US_ENGLISH_LABEL);
      await act(async () => {
        await userEvent.click(combobox);
      });

      await act(async () => {
        await userEvent.click(views.frenchCanadianMenuOption()!);
      });

      const newCombobox = await screen.findByRole('combobox');
      expect(newCombobox).toHaveTextContent(TranslationKeys.Language.CA_FRENCH_LABEL);
      expect(mockedChangeLanguage).toHaveBeenCalledWith('fr_ca');
    });

    it('shows all language options in the localization dropdown', async () => {
      await renderFooter();
      await act(async () => {
        await userEvent.click(views.usEnglishMenuOption()!);
      });

      const optionsList = screen.getAllByRole('option');
      expect(optionsList).toHaveLength(1);
      expect(optionsList[0]).toHaveTextContent(TranslationKeys.Language.CA_FRENCH_LABEL);
    });

    it('reverts to US English if the stored localization option is not valid', async () => {
      mockedLanguage.mockReturnValue('invalid');

      await renderFooter();

      expect(views.usEnglishMenuOption()).toBeInTheDocument();
      expect(views.frenchCanadianMenuOption()).not.toBeInTheDocument();
      expect(mockedChangeLanguage).toHaveBeenCalledWith('en_us');
    });
  });
});
