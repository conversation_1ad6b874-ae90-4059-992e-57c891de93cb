import { Grid, Link, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { Design } from '../../../helpers/Design';
import { theme } from '../../../PodsTheme';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { useBillingContext } from '../../../context/BillingContext';
import { ROUTES } from '../../../Routes';
import { LegacyFinancingDisclosure } from './LegacyFinancingDisclosure';
import { LanguageSelect } from '../../Footer/LanguageSelect';

export const LegacyFooter: React.FC = () => {
  const { t: translate } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const location = useLocation();
  const { isFinancingEligible } = useBillingContext();
  const style = styles(isMobile);
  const showFinancingFooter =
    isFinancingEligible &&
    (location.pathname === ROUTES.BILLING || location.pathname === ROUTES.HOME);

  return (
    <Grid container direction="row" {...style.footerContainer}>
      <Grid container {...style.footerInnerContainer}>
        <Grid container {...style.copyrightAndLinksContainer}>
          <Grid container item {...style.copyrightContainer}>
            <Typography variant="subtitle2">
              {translate(TranslationKeys.Footer.COPYRIGHT, {
                currentYear: new Date().getFullYear()
              })}
            </Typography>
          </Grid>

          <Grid container item gap={2} alignItems="start" maxWidth="fit-content">
            <Grid item maxWidth="fill-available">
              <Link
                href={translate(TranslationKeys.Footer.PRIVACY_POLICY_URL) ?? undefined}
                target="_blank"
                rel="noopener noreferrer"
                {...style.link}>
                {translate(TranslationKeys.Footer.PRIVACY_POLICY)}
              </Link>
            </Grid>
            <Grid item>
              <Link
                href={translate(TranslationKeys.Footer.TERMS_AND_CONDITIONS_URL) ?? undefined}
                target="_blank"
                rel="noopener noreferrer"
                {...style.link}>
                {translate(TranslationKeys.Footer.TERMS_AND_CONDITIONS)}
              </Link>
            </Grid>
            <Grid item>
              <Link
                role="button"
                className="ot-floating-button__open"
                underline="none"
                target="_blank"
                rel="noopener noreferrer"
                {...style.link}>
                {translate(TranslationKeys.Footer.MANAGE_COOKIE_PREFERENCES)}
              </Link>
            </Grid>
            <Grid item>
              <LanguageSelect />
            </Grid>
          </Grid>
          {showFinancingFooter && <LegacyFinancingDisclosure />}
        </Grid>
      </Grid>
    </Grid>
  );
};

const styles = (isMobile: boolean) => ({
  footerContainer: {
    sx: {
      backgroundColor: Design.Alias.Color.neutral100,
      direction: 'row',
      justifyContent: 'center'
    }
  },
  footerInnerContainer: {
    sx: {
      justifyContent: isMobile ? 'start' : 'space-between',
      flexDirection: isMobile ? 'column-reverse' : 'row',
      maxWidth: '1440px'
    }
  },
  copyrightAndLinksContainer: {
    sx: {
      flexDirection: isMobile ? 'column' : 'row',
      maxWidth: 'fill-available',
      justifyContent: 'space-between',
      rowGap: isMobile ? Design.Primitives.Spacing.xxs : Design.Primitives.Spacing.xs,
      marginX: isMobile ? Design.Primitives.Spacing.md : '2rem',
      marginY: isMobile ? Design.Primitives.Spacing.lgPlus : Design.Primitives.Spacing.md
    }
  },
  copyrightContainer: {
    sx: {
      paddingTop: isMobile ? 0 : Design.Primitives.Spacing.xxxs,
      maxWidth: 'fit-content'
    }
  },
  link: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Xs,
      color: Design.Alias.Color.accent800,
      cursor: 'pointer',
      textDecoration: 'none'
    }
  }
});
