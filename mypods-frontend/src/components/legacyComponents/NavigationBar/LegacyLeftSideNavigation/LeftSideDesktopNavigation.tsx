import React, { ReactNode } from 'react';
import { Divider, Drawer, Toolbar } from '@mui/material';
import { ENV_VARS } from '../../../../environment';

interface Props {
  children: ReactNode;
}

export const LeftSideDesktopNavigation: React.FC<Props> = ({ children }: Props) => (
  <Drawer {...styles.desktopDrawer} variant="permanent" anchor="left">
    <Toolbar>
      <img
        src={`${ENV_VARS.ASSETS_BASE_URL}/pods-secondary-logo-rgb-1.webp`}
        alt="pods-logo"
        {...styles.image}
      />
    </Toolbar>
    <Divider />
    {children}
    <Divider />
  </Drawer>
);

const styles = {
  image: {
    style: {
      width: '100px',
      height: '22px',
      flexShrink: 0
    }
  },

  desktopDrawer: {
    sx: {
      width: '232px'
    }
  }
};
