import React from 'react';
import { Box, ListItem } from '@mui/material';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import ListItemButton from '@mui/material/ListItemButton';
import { Design } from '../../../../helpers/Design';
import { MobileNavigationItem } from '../../../ComponentTypes';

interface Props {
  item: MobileNavigationItem;
  onClick: () => void;
  selectedItem: boolean;
}

export const LeftSideNavMenuListItem: React.FC<Props> = ({
  item,
  onClick,
  selectedItem
}: Props) => {
  const styles = menuItemStyles(selectedItem);
  return (
    <ListItem disablePadding sx={styles.menuListItem}>
      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        <Box sx={styles.activeIndicator} />
        <ListItemButton
          onClick={onClick}
          sx={{
            ...styles.listItemButton,
            ...(selectedItem && styles.listItemButtonSelected)
          }}>
          <ListItemIcon sx={styles.listItemIcon}>
            {React.cloneElement(item.icon as React.ReactElement, {
              style: {
                color: selectedItem
                  ? Design.Primitives.Color.Blue.oasis
                  : Design.Alias.Color.accent900
              }
            })}
          </ListItemIcon>
          <ListItemText primary={item.title} sx={styles.listItemText} />
        </ListItemButton>
      </Box>
    </ListItem>
  );
};

export const SupportListItem = ({
  item,
  selectedItem
}: {
  item: MobileNavigationItem;
  selectedItem: boolean;
}) => {
  const styles = menuItemStyles(selectedItem);
  return (
    <ListItem disablePadding>
      <Box sx={styles.supportList}>
        <Box sx={styles.supportListMenu}>
          <ListItemIcon sx={styles.supportListItemIcon}>{item.icon}</ListItemIcon>
          <ListItemText primary={item.title} sx={styles.supportListItemText} />
        </Box>
        <ListItemText sx={styles.supportListItemSubtitle}>{item.subtitle}</ListItemText>
      </Box>
    </ListItem>
  );
};

const menuItemStyles = (selectedItem: boolean) => ({
  menuListItem: {
    position: 'relative',
    gap: Design.Primitives.Spacing.md
  },
  listItemButton: {
    padding: Design.Primitives.Spacing.sm,
    width: '200px',
    height: '56px',
    gap: Design.Primitives.Spacing.xxxs,
    borderRadius: '8px',

    '&:hover': {
      backgroundColor: Design.Primitives.Color.Blue.oasis100
    }
  },
  listItemButtonSelected: {
    backgroundColor: Design.Primitives.Color.Blue.oasis100,
    color: Design.Primitives.Color.Blue.oasis,
    borderRadius: '8px',
    transition: 'background-color 0.2s ease',
    '&:hover': {
      backgroundColor: Design.Primitives.Color.Blue.oasis100
    }
  },
  MenuListGrid: {
    sx: {
      // Need minwidth here so that this component does not overflow the MenuItem container.
      // This is a quirk of flex elements like the MenuItem when dealing with overflowing elements.
      // see https://stackoverflow.com/questions/36230944/prevent-flex-items-from-overflowing-a-container/66689926#66689926 for more info
      minWidth: 0
    }
  },
  listItemIcon: {
    color: selectedItem ? Design.Primitives.Color.Blue.oasis : Design.Alias.Color.accent900,
    minWidth: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  listItemTextContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: Design.Primitives.Spacing.md
    }
  },
  listItemText: {
    style: { marginTop: 0, marginBottom: 0 },
    '.MuiTypography-root': {
      ...Design.Alias.Text.Heading.Mobile.Sm,
      fontWeight: selectedItem
        ? Design.Primitives.Font.Weight.bold
        : Design.Primitives.Font.Weight.regular,
      color: selectedItem ? Design.Primitives.Color.Blue.oasis : Design.Alias.Color.accent900
    }
  },
  listItemSubtitle: {
    style: { marginTop: 0, marginBottom: 0, maxWidth: '100%' },
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md
    }
  },
  activeIndicator: {
    width: '2px',
    height: '28px',
    backgroundColor: selectedItem ? Design.Primitives.Color.Blue.oasis : 'transparent',
    borderRadius: '2px',
    marginRight: '6px'
  },
  supportList: {
    padding: '8px',
    width: '100%',
    display: 'flex',
    flexDirection: 'column'
  },
  supportListMenu: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  supportListItemIcon: {
    minWidth: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center'
  },
  supportListItemText: {
    fontSize: '16px',
    fontWeight: 500,
    color: Design.Alias.Color.secondary500
  },
  supportListItemSubtitle: {
    fontSize: '14px',
    color: Design.Alias.Color.neutral800,
    marginLeft: '32px'
  }
});
