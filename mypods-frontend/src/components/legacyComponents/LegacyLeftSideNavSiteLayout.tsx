import React, { ReactNode } from 'react';
import { Grid } from '@mui/material';
import { Design } from '../../helpers/Design';
import { LegacyLeftSideNavigationBar } from './NavigationBar/LegacyLeftSideNavigation/LegacyLeftSideNavigationBar';

interface LayoutProps {
  children: ReactNode;
  // showHeaderFooter?: boolean;
  // globalBanners?: ReactNode;
}

export const LegacyLeftSideNavSiteLayout: React.FC<LayoutProps> = ({ children }: LayoutProps) => (
  <Grid container sx={styles.container}>
    <LegacyLeftSideNavigationBar />
    <Grid item xs={12} md sx={styles.mainContent}>
      {children}
    </Grid>
  </Grid>
);

// -- styles --
const styles = {
  container: {
    display: 'flex',
    minHeight: '100vh',
    width: '100%'
  },
  mainContent: {
    sx: {
      flex: 1,
      display: 'flex',
      alignItems: 'flex-start',
      flexDirection: 'column',
      marginLeft: { md: Design.Alias.Sizing.leftNavDrawerWidth }, // Match the width of the desktop drawer
      paddingTop: {
        xs: Design.Primitives.Spacing.md,
        md: Design.Primitives.Spacing.lgPlus
      },
      paddingLeft: {
        xs: Design.Primitives.Spacing.md,
        md: Design.Primitives.Spacing.lg
      },
      paddingRight: {
        xs: Design.Primitives.Spacing.md,
        md: Design.Primitives.Spacing.lg
      },
      width: { md: `calc(100% - ${Design.Alias.Sizing.leftNavDrawerWidth})` } // Subtract the drawer width
    }
  }
};
