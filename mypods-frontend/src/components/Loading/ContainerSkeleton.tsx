import { Box, CircularProgress, Grid, Skeleton, Typography, useMediaQuery } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { theme } from '../../PodsTheme';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';

export const ContainerSkeleton = ({
  isError
}: {
  isError?: boolean;
  topOfPageRef?: React.RefObject<HTMLDivElement>;
}) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const styles = containerStyles(isMobile);
  const { t: translate } = useTranslation();
  const Tx = TranslationKeys.HomePage.SkeletonLoader;

  return (
    <Grid {...styles.skeletonLayout} data-testid="loading-container">
      {isError && (
        <Grid container {...styles.skeletonHeader}>
          <Typography variant="h3" {...styles.skeletonHeaderText}>
            {translate(Tx.HEADER)}
          </Typography>
          <Typography variant="subtitle1" {...styles.skeletonSubHeaderText}>
            {translate(Tx.SUB_HEADER)}
          </Typography>
          <CircularProgress size={20} color="primary" />
        </Grid>
      )}
      <Grid {...styles.skeletonContainer}>
        <Skeleton variant="rectangular" height={100} />
        <Grid {...styles.content}>
          <Grid container {...styles.moveLegSection}>
            <Grid item {...styles.flexColumn}>
              <Skeleton variant="circular" width={17} height={17} />
              <Box {...styles.trailingLineTop} />
            </Grid>
            <Grid item gap={2} {...styles.flexColumn} paddingBottom="85px">
              <HorizontalBarSkeleton width={isMobile ? 150 : 255} />
              <HorizontalBarSkeleton width={82} />
              <HorizontalBarSkeleton width={109} />
            </Grid>
          </Grid>

          <Grid container {...styles.moveLegSection}>
            <Grid item {...styles.flexColumn}>
              <Skeleton variant="circular" width={17} height={17} />
              <Box {...styles.trailingLineTop} />
            </Grid>
            <Grid item gap={2} {...styles.flexColumn}>
              <HorizontalBarSkeleton width={isMobile ? 150 : 255} />
              <HorizontalBarSkeleton width={82} />
              <HorizontalBarSkeleton width={109} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

const HorizontalBarSkeleton = ({ width }: { width: number }) => (
  <Skeleton variant="rectangular" height={18} sx={{ borderRadius: '4px', width: `${width}px` }} />
);

const containerStyles = (isMobile: boolean) => ({
  skeletonLayout: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      gap: '30px'
    }
  },
  skeletonHeader: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      paddingX: Design.Primitives.Spacing.sm,
      paddingY: '6px'
    }
  },
  skeletonHeaderText: {
    sx: {
      color: Design.Alias.Color.errorDark,
      paddingBottom: Design.Primitives.Spacing.xxxs
    }
  },
  skeletonSubHeaderText: {
    sx: {
      color: Design.Alias.Color.errorDark,
      paddingBottom: Design.Primitives.Spacing.md
    }
  },
  skeletonContainer: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      borderRadius: Design.Primitives.Spacing.xxs,
      backgroundColor: Design.Alias.Color.neutral100,
      overflow: 'hidden',
      border: '1px solid var(--color-neutral-neutral300, #CBCBCB)',
      boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.10)'
    }
  },
  content: {
    sx: {
      paddingX: isMobile ? Design.Primitives.Spacing.xs : Design.Primitives.Spacing.lgPlus,
      paddingY: '64px'
    }
  },
  moveLegSection: {
    sx: {
      display: 'flex',
      flexDirection: 'row',
      columnGap: Design.Primitives.Spacing.xxs
    }
  },
  flexColumn: {
    sx: {
      display: 'flex',
      flexDirection: 'column',
      borderRadius: '4px'
    }
  },
  trailingLineTop: {
    sx: {
      height: '100%',
      width: '50%',
      borderRight: `1px solid ${Design.Alias.Color.neutral200}`
    }
  }
});
