import React, { ReactNode } from 'react';
import { datadogLogs } from '@datadog/browser-logs';
import axios from 'axios';

interface Props {
  children: ReactNode;
  renderError: (error: Error) => ReactNode;
  componentDidCatch?: (error: Error, info: any) => void;
  redirectOnError?: (status: number | null) => void;
}

interface State {
  error: Error | null;
}

export class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    // listens for errors
    return { error };
  }

  componentDidCatch(error: Error, info: any) {
    const { redirectOnError, componentDidCatch } = this.props;

    if (redirectOnError && axios.isAxiosError(error)) {
      const status = error.response?.status || null;
      redirectOnError(status);
    }

    datadogLogs.logger.log(
      'Error Boundary reached.',
      undefined, // do not see any effect of these in datadog - leaving undefined for now
      'error',
      error
    );
    if (componentDidCatch) {
      // e.g. sending the stack trace to logging.
      componentDidCatch(error, info);
    }
  }

  render() {
    // destructure state/props for convenience
    const { children, renderError } = this.props;
    const { error } = this.state;

    if (error) {
      return renderError(error);
    }

    return children;
  }
}
