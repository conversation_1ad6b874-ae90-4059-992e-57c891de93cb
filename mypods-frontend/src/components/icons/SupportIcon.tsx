import React from 'react';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { SvgIcon } from '@mui/material';
import { Design } from '../../helpers/Design';

export const SupportIcon = (props: SvgIconProps) => (
  <SvgIcon style={{ color: Design.Alias.Color.neutral800 }} {...props}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="Headset">
        <path
          id="Vector"
          d="M21.75 11.9997V19.4997C21.75 20.4943 21.3549 21.4481 20.6517 22.1513C19.9484 22.8546 18.9946 23.2497 18 23.2497H12.75C12.5511 23.2497 12.3603 23.1707 12.2197 23.03C12.079 22.8894 12 22.6986 12 22.4997C12 22.3008 12.079 22.11 12.2197 21.9694C12.3603 21.8287 12.5511 21.7497 12.75 21.7497H18C18.5967 21.7497 19.169 21.5126 19.591 21.0907C20.0129 20.6687 20.25 20.0964 20.25 19.4997H18C17.4033 19.4997 16.831 19.2626 16.409 18.8407C15.9871 18.4187 15.75 17.8464 15.75 17.2497V13.4997C15.75 12.903 15.9871 12.3307 16.409 11.9087C16.831 11.4867 17.4033 11.2497 18 11.2497H20.2172C20.0753 9.69057 19.4926 8.20402 18.5374 6.96367C17.5821 5.72332 16.2936 4.78039 14.8225 4.245C13.3513 3.70962 11.7581 3.60389 10.2291 3.94016C8.70008 4.27644 7.2983 5.04083 6.1875 6.14407C4.80624 7.50711 3.9543 9.31664 3.78375 11.2497H6C6.59674 11.2497 7.16903 11.4867 7.59099 11.9087C8.01295 12.3307 8.25 12.903 8.25 13.4997V17.2497C8.25 17.8464 8.01295 18.4187 7.59099 18.8407C7.16903 19.2626 6.59674 19.4997 6 19.4997H4.5C3.90326 19.4997 3.33097 19.2626 2.90901 18.8407C2.48705 18.4187 2.25 17.8464 2.25 17.2497V11.9997C2.2521 10.0683 2.82715 8.18104 3.90235 6.57665C4.97755 4.97227 6.50456 3.72294 8.29012 2.9868C10.0757 2.25066 12.0395 2.0608 13.933 2.44125C15.8265 2.82171 17.5646 3.75537 18.9272 5.12407C19.8268 6.0281 20.5393 7.10077 21.0237 8.28062C21.5081 9.46046 21.7549 10.7243 21.75 11.9997Z"
          fill="currentColor"
        />
      </g>
    </svg>
  </SvgIcon>
);
