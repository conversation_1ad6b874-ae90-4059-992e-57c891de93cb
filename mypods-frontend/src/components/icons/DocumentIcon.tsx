import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { Design } from '../../helpers/Design';

export const DOCUMENT_ICON_ID = 'folder-icon';

export const DocumentIcon = (props: SvgIconProps) => (
  <SvgIcon
    data-testid={DOCUMENT_ICON_ID}
    style={{ color: Design.Alias.Color.accent900 }}
    {...props}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="File">
        <path
          id="Vector"
          d="M19.8632 8.32913L14.7478 3.21375C14.6799 3.14591 14.5993 3.09211 14.5105 3.05544C14.4218 3.01876 14.3268 2.99992 14.2308 3H5.46154C5.07391 3 4.70217 3.15398 4.42807 3.42807C4.15398 3.70217 4 4.07391 4 4.46154V20.5385C4 20.9261 4.15398 21.2978 4.42807 21.5719C4.70217 21.846 5.07391 22 5.46154 22H18.6154C19.003 22 19.3748 21.846 19.6488 21.5719C19.9229 21.2978 20.0769 20.9261 20.0769 20.5385V8.84615C20.077 8.75016 20.0582 8.65509 20.0215 8.56638C19.9848 8.47767 19.931 8.39705 19.8632 8.32913ZM14.2308 8.84615V4.82692L18.25 8.84615H14.2308Z"
          fill="currentColor"
        />
      </g>
    </svg>
  </SvgIcon>
);
