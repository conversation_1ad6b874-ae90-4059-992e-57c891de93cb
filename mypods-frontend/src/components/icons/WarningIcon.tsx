import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';

export const WARNING_ICON_ID = 'warning-icon';

export const WarningIcon = (props: SvgIconProps) => (
  <SvgIcon data-testid={WARNING_ICON_ID} style={{ color: 'white' }} {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M2.27246 17.5C2.11969 17.5 1.9808 17.4619 1.8558 17.3858C1.7308 17.3097 1.63358 17.2089 1.56413 17.0833C1.49469 16.9583 1.45663 16.8228 1.44996 16.6767C1.4433 16.5306 1.48135 16.3883 1.56413 16.25L9.27246 2.91667C9.3558 2.77778 9.46357 2.67361 9.5958 2.60417C9.72802 2.53472 9.8633 2.5 10.0016 2.5C10.1405 2.5 10.2761 2.53472 10.4083 2.60417C10.5405 2.67361 10.648 2.77778 10.7308 2.91667L18.4391 16.25C18.5225 16.3889 18.5608 16.5314 18.5541 16.6775C18.5475 16.8236 18.5091 16.9589 18.4391 17.0833C18.3697 17.2083 18.2725 17.3092 18.1475 17.3858C18.0225 17.4625 17.8836 17.5006 17.7308 17.5H2.27246ZM10.0016 15C10.2377 15 10.4358 14.92 10.5958 14.76C10.7558 14.6 10.8355 14.4022 10.835 14.1667C10.835 13.9306 10.755 13.7328 10.595 13.5733C10.435 13.4139 10.2372 13.3339 10.0016 13.3333C9.76552 13.3333 9.56774 13.4133 9.4083 13.5733C9.24885 13.7333 9.16885 13.9311 9.1683 14.1667C9.1683 14.4028 9.2483 14.6008 9.4083 14.7608C9.5683 14.9208 9.76608 15.0006 10.0016 15ZM10.0016 12.5C10.2377 12.5 10.4358 12.42 10.5958 12.26C10.7558 12.1 10.8355 11.9022 10.835 11.6667V9.16667C10.835 8.93056 10.755 8.73278 10.595 8.57333C10.435 8.41389 10.2372 8.33389 10.0016 8.33333C9.76552 8.33333 9.56774 8.41333 9.4083 8.57333C9.24885 8.73333 9.16885 8.93111 9.1683 9.16667V11.6667C9.1683 11.9028 9.2483 12.1008 9.4083 12.2608C9.5683 12.4208 9.76608 12.5006 10.0016 12.5Z"
        fill="#993600"
      />
    </svg>
  </SvgIcon>
);
