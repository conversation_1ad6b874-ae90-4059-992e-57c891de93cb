import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import React from 'react';
import { SvgIcon } from '@mui/material';
import { DOCUMENT_ICON_ID } from './DocumentIcon';

export const DeviceMobileIcon = (props: SvgIconProps) => (
  <SvgIcon data-testid={DOCUMENT_ICON_ID} {...props}>
    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.25 1.25H6.75C6.25272 1.25 5.77581 1.44754 5.42417 1.79917C5.07254 2.15081 4.875 2.62772 4.875 3.125V16.875C4.875 17.3723 5.07254 17.8492 5.42417 18.2008C5.77581 18.5525 6.25272 18.75 6.75 18.75H14.25C14.7473 18.75 15.2242 18.5525 15.5758 18.2008C15.9275 17.8492 16.125 17.3723 16.125 16.875V3.125C16.125 2.62772 15.9275 2.15081 15.5758 1.79917C15.2242 1.44754 14.7473 1.25 14.25 1.25ZM6.125 5H14.875V15H6.125V5ZM6.75 2.5H14.25C14.4158 2.5 14.5747 2.56585 14.6919 2.68306C14.8092 2.80027 14.875 2.95924 14.875 3.125V3.75H6.125V3.125C6.125 2.95924 6.19085 2.80027 6.30806 2.68306C6.42527 2.56585 6.58424 2.5 6.75 2.5ZM14.25 17.5H6.75C6.58424 17.5 6.42527 17.4342 6.30806 17.3169C6.19085 17.1997 6.125 17.0408 6.125 16.875V16.25H14.875V16.875C14.875 17.0408 14.8092 17.1997 14.6919 17.3169C14.5747 17.4342 14.4158 17.5 14.25 17.5Z"
        fill="#0069E5"
      />
    </svg>
  </SvgIcon>
);
