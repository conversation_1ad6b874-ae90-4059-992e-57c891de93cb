import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { Design } from '../../helpers/Design';

export const LogoutIcon = (props: SvgIconProps) => (
  <SvgIcon style={{ color: Design.Alias.Color.neutral800 }} {...props}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="SignOut">
        <path
          id="Vector"
          d="M11.6664 21.2083C11.6664 21.4183 11.583 21.6197 11.4345 21.7681C11.2861 21.9166 11.0847 22 10.8748 22H4.54164C4.33168 22 4.13033 21.9166 3.98187 21.7681C3.8334 21.6197 3.75 21.4183 3.75 21.2083V3.79167C3.75 3.5817 3.8334 3.38034 3.98187 3.23187C4.13033 3.08341 4.33168 3 4.54164 3H10.8748C11.0847 3 11.2861 3.08341 11.4345 3.23187C11.583 3.38034 11.6664 3.5817 11.6664 3.79167C11.6664 4.00163 11.583 4.20299 11.4345 4.35146C11.2861 4.49993 11.0847 4.58333 10.8748 4.58333H5.33328V20.4167H10.8748C11.0847 20.4167 11.2861 20.5001 11.4345 20.6485C11.583 20.797 11.6664 20.9984 11.6664 21.2083ZM22.5178 11.9399L18.5596 7.98156C18.4489 7.87072 18.3078 7.79522 18.1541 7.76462C18.0005 7.73402 17.8412 7.7497 17.6965 7.80967C17.5518 7.86964 17.4281 7.97121 17.3411 8.10151C17.2541 8.23182 17.2078 8.385 17.2079 8.54167V11.7083H10.8748C10.6648 11.7083 10.4635 11.7917 10.315 11.9402C10.1665 12.0887 10.0831 12.29 10.0831 12.5C10.0831 12.71 10.1665 12.9113 10.315 13.0598C10.4635 13.2083 10.6648 13.2917 10.8748 13.2917H17.2079V16.4583C17.2078 16.615 17.2541 16.7682 17.3411 16.8985C17.4281 17.0288 17.5518 17.1304 17.6965 17.1903C17.8412 17.2503 18.0005 17.266 18.1541 17.2354C18.3078 17.2048 18.4489 17.1293 18.5596 17.0184L22.5178 13.0601C22.5914 12.9866 22.6498 12.8993 22.6897 12.8032C22.7295 12.7071 22.75 12.604 22.75 12.5C22.75 12.396 22.7295 12.2929 22.6897 12.1968C22.6498 12.1007 22.5914 12.0134 22.5178 11.9399Z"
          fill="currentColor"
        />
      </g>
    </svg>
  </SvgIcon>
);
