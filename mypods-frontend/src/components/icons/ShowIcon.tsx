import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { Design } from '../../helpers/Design';

export const ShowIcon = (props: SvgIconProps) => (
  <SvgIcon style={{ color: Design.Alias.Color.secondary500 }} {...props}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.39082 12.3952C5.29565 12.2479 5.21377 12.1148 5.14549 12.0002C5.21377 11.8856 5.29565 11.7526 5.39082 11.6053C5.73075 11.0789 6.23379 10.3796 6.88579 9.68419C8.20802 8.2738 10.0245 7.00024 12.25 7.00024C14.4755 7.00024 16.292 8.2738 17.6142 9.68419C18.2662 10.3796 18.7693 11.0789 19.1092 11.6053C19.2044 11.7526 19.2862 11.8856 19.3545 12.0002C19.2862 12.1148 19.2044 12.2479 19.1092 12.3952C18.7693 12.9216 18.2662 13.6208 17.6142 14.3163C16.292 15.7267 14.4755 17.0002 12.25 17.0002C10.0245 17.0002 8.20802 15.7267 6.88579 14.3163C6.23379 13.6208 5.73075 12.9216 5.39082 12.3952ZM21.3941 11.5523C21.3943 11.5527 21.3944 11.553 20.5 12.0002C21.3944 12.4475 21.3943 12.4478 21.3941 12.4481L21.3937 12.449L21.3926 12.451L21.3897 12.4568L21.3806 12.4747C21.373 12.4894 21.3624 12.5098 21.3489 12.5353C21.3219 12.5864 21.283 12.6584 21.2324 12.7483C21.1314 12.928 20.9832 13.18 20.7893 13.4803C20.4026 14.0789 19.8275 14.8796 19.0733 15.6842C17.583 17.2738 15.2745 19.0002 12.25 19.0002C9.22549 19.0002 6.91698 17.2738 5.42671 15.6842C4.67246 14.8796 4.09738 14.0789 3.71074 13.4803C3.5168 13.18 3.36864 12.928 3.26758 12.7483C3.21702 12.6584 3.17813 12.5864 3.1511 12.5353C3.13757 12.5098 3.12701 12.4894 3.11942 12.4747L3.11026 12.4568L3.10736 12.451L3.10632 12.449L3.10591 12.4481C3.10574 12.4478 3.10557 12.4475 4 12.0002C3.10557 11.553 3.10574 11.5527 3.10591 11.5523L3.10632 11.5515L3.10736 11.5495L3.11026 11.5437L3.11942 11.5258C3.12701 11.511 3.13757 11.4907 3.1511 11.4651C3.17813 11.4141 3.21702 11.3421 3.26758 11.2522C3.36864 11.0725 3.5168 10.8205 3.71074 10.5202C4.09738 9.92156 4.67246 9.12084 5.42671 8.3163C6.91698 6.72668 9.22549 5.00024 12.25 5.00024C15.2745 5.00024 17.583 6.72668 19.0733 8.3163C19.8275 9.12084 20.4026 9.92156 20.7893 10.5202C20.9832 10.8205 21.1314 11.0725 21.2324 11.2522C21.283 11.3421 21.3219 11.4141 21.3489 11.4651C21.3624 11.4907 21.373 11.511 21.3806 11.5258L21.3897 11.5437L21.3926 11.5495L21.3937 11.5515L21.3941 11.5523ZM20.5 12.0002L21.3944 11.553C21.5352 11.8346 21.5352 12.1659 21.3944 12.4475L20.5 12.0002ZM3.10557 11.553L4 12.0002L3.10557 12.4475C2.96481 12.1659 2.96481 11.8346 3.10557 11.553ZM11 12.0002C11 11.3099 11.5596 10.7502 12.25 10.7502C12.9404 10.7502 13.5 11.3099 13.5 12.0002C13.5 12.6906 12.9404 13.2502 12.25 13.2502C11.5596 13.2502 11 12.6906 11 12.0002ZM12.25 8.75024C10.4551 8.75024 9 10.2053 9 12.0002C9 13.7952 10.4551 15.2502 12.25 15.2502C14.0449 15.2502 15.5 13.7952 15.5 12.0002C15.5 10.2053 14.0449 8.75024 12.25 8.75024Z"
        fill="currentColor"
      />
    </svg>
  </SvgIcon>
);
