import React from 'react';
import { createSvgIcon } from '@mui/material';

export const DiscoverCardIcon = createSvgIcon(
  <svg width="25" height="16" viewBox="0 0 25 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="24.338" height="16" fill="url(#pattern0_2359_132793)" />
    <defs>
      <pattern
        id="pattern0_2359_132793"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1">
        <use
          xlinkHref="#image0_2359_132793"
          transform="matrix(0.0138889 0 0 0.0211268 0 -0.00704225)"
        />
      </pattern>
      <image
        id="image0_2359_132793"
        width="72"
        height="48"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>,
  'DiscoverCard'
);
