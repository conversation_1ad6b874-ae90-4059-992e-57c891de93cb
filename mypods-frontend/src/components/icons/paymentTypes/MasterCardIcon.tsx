import React from 'react';
import { createSvgIcon } from '@mui/material';

export const MasterCardIcon = createSvgIcon(
  <svg width="24" height="17" viewBox="0 0 24 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="24" height="16.2254" fill="url(#pattern0_2359_132789)" />
    <defs>
      <pattern
        id="pattern0_2359_132789"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1">
        <use xlinkHref="#image0_2359_132789" transform="scale(0.0140845 0.0208333)" />
      </pattern>
      <image
        id="image0_2359_132789"
        width="71"
        height="48"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>,
  'Mastercard'
);
