import React from 'react';
import { createSvgIcon } from '@mui/material';

export const VisaCardIcon = createSvgIcon(
  <svg width="24" height="16" viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="24" height="16" fill="url(#pattern0_2359_132797)" />
    <defs>
      <pattern
        id="pattern0_2359_132797"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1">
        <use
          xlinkHref="#image0_2359_132797"
          transform="matrix(0.0140845 0 0 0.0211268 0 -0.00704225)"
        />
      </pattern>
      <image
        id="image0_2359_132797"
        width="71"
        height="48"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>,
  'VisaCard'
);
