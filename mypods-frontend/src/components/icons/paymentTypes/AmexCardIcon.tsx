import React from 'react';
import { createSvgIcon } from '@mui/material';

export const AmexCardIcon = createSvgIcon(
  <svg width="24" height="16" viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="24" height="16" fill="url(#pattern0_2359_132791)" />
    <defs>
      <pattern
        id="pattern0_2359_132791"
        patternContentUnits="objectBoundingBox"
        width="1"
        height="1">
        <use
          xlinkHref="#image0_2359_132791"
          transform="matrix(0.0140845 0 0 0.0211268 0 -0.00704225)"
        />
      </pattern>
      <image
        id="image0_2359_132791"
        width="71"
        height="48"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>,
  'AmericanExpressCard'
);
