import { SvgIcon } from '@mui/material';
import React from 'react';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';

export const GREEN_CHECKMARK_ICON_ID = 'green-checkmark';
export const DASH_ICON_ID = 'dash';
export const RED_X_ICON_ID = 'red-x';

export const GreenCheckmarkIcon = (props: SvgIconProps) => (
  <SvgIcon data-testid={GREEN_CHECKMARK_ICON_ID} {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="11" viewBox="0 0 15 11" fill="none">
      <path d="M1 4.75024L5.5 9.25024L14 0.750244" stroke="#1C843C" strokeWidth="2" />
    </svg>
  </SvgIcon>
);

export const DashIcon = (props: SvgIconProps) => (
  <SvgIcon data-testid={DASH_ICON_ID} style={{ color: '#083544' }} {...props}>
    <svg width="14" height="17" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="5" y="8" width="4" height="1" fill="currentcolor" />
    </svg>
  </SvgIcon>
);

export const RedXIcon = (props: SvgIconProps) => (
  <SvgIcon data-testid={RED_X_ICON_ID} {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.62132 5.95735L11.9142 1.66446L10.5 0.250244L6.20711 4.54314L1.91421 0.250244L0.5 1.66446L4.79289 5.95735L0.5 10.2502L1.91421 11.6645L6.20711 7.37156L10.5 11.6645L11.9142 10.2502L7.62132 5.95735Z"
        fill="#DC3333"
      />
    </svg>
  </SvgIcon>
);
