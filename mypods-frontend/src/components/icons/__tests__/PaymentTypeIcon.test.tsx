import { CardIcon, PaymentTypeIcon } from '../PaymentTypeIcon';
import { render, screen } from '@testing-library/react';

describe('Payment Type Icon', () => {
  it.each([
    [CardIcon.MASTERCARD, 'MastercardIcon'],
    [CardIcon.VISA, 'VisaCardIcon'],
    [CardIcon.AMEX, 'AmericanExpressCardIcon'],
    [CardIcon.DISCOVER, 'DiscoverCardIcon'],
    [CardIcon.PAYPAL, 'PaypalIcon'],
    [CardIcon.LINE_OF_CREDIT, 'AccountBalanceIcon']
  ])('should display the %s icon', (cardType, testId) => {
    render(<PaymentTypeIcon cardType={cardType} />);
    expect(screen.getByTestId(testId)).toBeInTheDocument();
  });
});
