import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { Design } from '../../helpers/Design';

export const BillingIcon = (props: SvgIconProps) => (
  <SvgIcon style={{ color: Design.Alias.Color.neutral800 }} {...props}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="CurrencyDollar">
        <path
          d="M15.4231 14.6923C15.4231 15.0799 15.2691 15.4517 14.995 15.7258C14.7209 15.9999 14.3492 16.1538 13.9615 16.1538H13.2308V13.2308H13.9615C14.3492 13.2308 14.7209 13.3848 14.995 13.6588C15.2691 13.9329 15.4231 14.3047 15.4231 14.6923ZM22 12.5C22 14.3789 21.4428 16.2156 20.399 17.7779C19.3551 19.3402 17.8714 20.5578 16.1355 21.2769C14.3996 21.9959 12.4895 22.184 10.6466 21.8175C8.80383 21.4509 7.11109 20.5461 5.78249 19.2175C4.45389 17.8889 3.5491 16.1962 3.18254 14.3534C2.81598 12.5105 3.00412 10.6004 3.72315 8.86451C4.44218 7.12861 5.65982 5.64491 7.22209 4.60104C8.78435 3.55717 10.6211 3 12.5 3C15.0187 3.00266 17.4336 4.0044 19.2146 5.78542C20.9956 7.56644 21.9973 9.98126 22 12.5ZM16.8846 14.6923C16.8846 13.9171 16.5767 13.1736 16.0285 12.6254C15.4803 12.0772 14.7368 11.7692 13.9615 11.7692H13.2308V8.84615H13.5962C13.9838 8.84615 14.3555 9.00014 14.6296 9.27423C14.9037 9.54832 15.0577 9.92007 15.0577 10.3077C15.0577 10.5015 15.1347 10.6874 15.2717 10.8244C15.4088 10.9615 15.5947 11.0385 15.7885 11.0385C15.9823 11.0385 16.1681 10.9615 16.3052 10.8244C16.4422 10.6874 16.5192 10.5015 16.5192 10.3077C16.5192 9.53244 16.2113 8.78895 15.6631 8.24076C15.1149 7.69258 14.3714 7.38461 13.5962 7.38461H13.2308V6.65385C13.2308 6.46003 13.1538 6.27416 13.0167 6.13711C12.8797 6.00007 12.6938 5.92308 12.5 5.92308C12.3062 5.92308 12.1203 6.00007 11.9833 6.13711C11.8462 6.27416 11.7692 6.46003 11.7692 6.65385V7.38461H11.4038C10.6286 7.38461 9.8851 7.69258 9.33692 8.24076C8.78874 8.78895 8.48077 9.53244 8.48077 10.3077C8.48077 11.0829 8.78874 11.8264 9.33692 12.3746C9.8851 12.9228 10.6286 13.2308 11.4038 13.2308H11.7692V16.1538H11.0385C10.6508 16.1538 10.2791 15.9999 10.005 15.7258C9.73091 15.4517 9.57693 15.0799 9.57693 14.6923C9.57693 14.4985 9.49993 14.3126 9.36289 14.1756C9.22584 14.0385 9.03997 13.9615 8.84616 13.9615C8.65234 13.9615 8.46647 14.0385 8.32943 14.1756C8.19238 14.3126 8.11539 14.4985 8.11539 14.6923C8.11539 15.4676 8.42335 16.211 8.97154 16.7592C9.51972 17.3074 10.2632 17.6154 11.0385 17.6154H11.7692V18.3462C11.7692 18.54 11.8462 18.7258 11.9833 18.8629C12.1203 18.9999 12.3062 19.0769 12.5 19.0769C12.6938 19.0769 12.8797 18.9999 13.0167 18.8629C13.1538 18.7258 13.2308 18.54 13.2308 18.3462V17.6154H13.9615C14.7368 17.6154 15.4803 17.3074 16.0285 16.7592C16.5767 16.211 16.8846 15.4676 16.8846 14.6923ZM9.94231 10.3077C9.94231 10.6953 10.0963 11.0671 10.3704 11.3412C10.6445 11.6152 11.0162 11.7692 11.4038 11.7692H11.7692V8.84615H11.4038C11.0162 8.84615 10.6445 9.00014 10.3704 9.27423C10.0963 9.54832 9.94231 9.92007 9.94231 10.3077Z"
          fill="currentColor"
        />
      </g>
    </svg>
  </SvgIcon>
);
