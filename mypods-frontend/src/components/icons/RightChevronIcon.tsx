import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { Design } from '../../helpers/Design';

export const RightChevronIcon = (props: SvgIconProps) => (
  <SvgIcon
    style={{ color: Design.Alias.Color.secondary500, width: '1rem', height: '1rem' }}
    {...props}>
    <svg
      version="1.2"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 16 16"
      width="16"
      height="16">
      <path
        fillRule="evenodd"
        style={{
          fill: 'none',
          stroke: 'currentcolor',
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          strokeWidth: 2
        }}
        d="m5 3.3l4.7 4.7-4.7 4.7"
      />
    </svg>
  </SvgIcon>
);
