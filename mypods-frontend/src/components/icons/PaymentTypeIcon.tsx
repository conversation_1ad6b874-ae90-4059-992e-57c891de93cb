import React from 'react';
import { AccountBalance } from '@mui/icons-material';
import { MasterCardIcon } from './paymentTypes/MasterCardIcon';
import { VisaCardIcon } from './paymentTypes/VisaCardIcon';
import { AmexCardIcon } from './paymentTypes/AmexCardIcon';
import { DiscoverCardIcon } from './paymentTypes/DiscoverCardIcon';
import { PaypalIcon } from './paymentTypes/PaypalIcon';

export enum CardIcon {
  VISA = 'Visa',
  MASTERCARD = 'MasterCard',
  AMEX = 'AmericanExpress',
  DISCOVER = 'Discover',
  PAYPAL = 'PayPal',
  LINE_OF_CREDIT = 'LineOfCredit'
}

export const PaymentTypeIcon = ({ cardType }: { cardType: CardIcon }) => {
  switch (cardType) {
    case CardIcon.MASTERCARD:
      return <MasterCardIcon />;
    case CardIcon.VISA:
      return <VisaCardIcon />;
    case CardIcon.AMEX:
      return <AmexCardIcon />;
    case CardIcon.DISCOVER:
      return <DiscoverCardIcon />;
    case CardIcon.PAYPAL:
      return <PaypalIcon sx={{ width: '62px' }} />;
    case CardIcon.LINE_OF_CREDIT:
      return <AccountBalance />;
    default:
      console.error('Payment type icon not found for ', cardType);
      return null;
  }
};
