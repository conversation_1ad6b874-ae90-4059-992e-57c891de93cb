import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { Design } from '../../helpers/Design';

export const HideIcon = (props: SvgIconProps) => (
  <SvgIcon style={{ color: Design.Alias.Color.secondary500 }} {...props}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.70711 3.29314C4.31658 2.90261 3.68342 2.90261 3.29289 3.29314C2.90237 3.68366 2.90237 4.31683 3.29289 4.70735L6.31083 7.72528C5.02058 8.87732 3.93946 10.2481 3.11884 11.7774C2.96521 12.0637 2.96026 12.4069 3.10557 12.6975L4 12.2503C3.10557 12.6975 3.10574 12.6978 3.10591 12.6982L3.10632 12.699L3.10736 12.701L3.11026 12.7068L3.11942 12.7247C3.12701 12.7395 3.13757 12.7598 3.1511 12.7854C3.17813 12.8365 3.21702 12.9084 3.26758 12.9983C3.36864 13.178 3.5168 13.43 3.71074 13.7303C4.09738 14.3289 4.67246 15.1297 5.42671 15.9342C6.91698 17.5238 9.22549 19.2503 12.25 19.2503L12.2663 19.2501C13.7966 19.2251 15.2865 18.7901 16.5836 17.9981L19.7929 21.2074C20.1834 21.5979 20.8166 21.5979 21.2071 21.2074C21.5976 20.8168 21.5976 20.1837 21.2071 19.7931L17.4214 16.0074C17.4147 16.0005 17.4079 15.9938 17.4011 15.9871L8.51137 7.0974C8.50469 7.09052 8.49793 7.08377 8.49109 7.07712L4.70711 3.29314ZM7.72766 9.14211C6.70905 10.0356 5.83774 11.0852 5.1467 12.2523C5.21474 12.3664 5.29621 12.4987 5.39082 12.6452C5.73075 13.1716 6.23379 13.8709 6.88579 14.5663C8.20641 15.975 10.0201 17.2472 12.2419 17.2503C13.2472 17.2326 14.2296 16.9839 15.1154 16.5299L13.7469 15.1613C13.6718 15.2022 13.595 15.2401 13.5166 15.275C13.1179 15.4527 12.6876 15.5482 12.2512 15.5559C11.8148 15.5636 11.3814 15.4833 10.9767 15.3198C10.572 15.1564 10.2044 14.9131 9.89578 14.6045C9.58716 14.2959 9.34387 13.9283 9.18041 13.5236C9.01695 13.1189 8.93668 12.6854 8.94438 12.2491C8.95208 11.8127 9.0476 11.3823 9.22523 10.9837C9.26014 10.9053 9.29808 10.8285 9.33893 10.7534L7.72766 9.14211ZM10.945 12.3595C10.951 12.5017 10.9814 12.6421 11.0349 12.7745C11.0977 12.9302 11.1913 13.0716 11.31 13.1903C11.4287 13.309 11.5701 13.4025 11.7257 13.4654C11.8581 13.5189 11.9985 13.5492 12.1408 13.5553L10.945 12.3595ZM10.9029 7.40394C11.3437 7.30077 11.795 7.2492 12.2477 7.25026L12.25 7.25026C14.4755 7.25026 16.292 8.52382 17.6142 9.9342C18.2662 10.6297 18.7693 11.329 19.1092 11.8553C19.2041 12.0022 19.2857 12.1348 19.3539 12.2492C18.9904 12.8656 18.576 13.4509 18.1148 13.9989C17.7593 14.4215 17.8136 15.0523 18.2362 15.4079C18.6587 15.7635 19.2896 15.7092 19.6452 15.2866C20.3118 14.4944 20.8938 13.6348 21.3819 12.7217C21.5348 12.4356 21.5395 12.0932 21.3944 11.803L20.5 12.2503C21.3944 11.803 21.3943 11.8027 21.3941 11.8024L21.3937 11.8015L21.3926 11.7995L21.3897 11.7938L21.3806 11.7758C21.373 11.7611 21.3624 11.7407 21.3489 11.7152C21.3219 11.6641 21.283 11.5921 21.2324 11.5022C21.1314 11.3225 20.9832 11.0705 20.7893 10.7702C20.4026 10.1716 19.8275 9.37086 19.0733 8.56632C17.5832 6.97691 15.2751 5.25071 12.2512 5.25026L12.25 5.25026V6.25026L12.2523 5.25027L12.2512 5.25026C11.6439 5.24893 11.0384 5.31816 10.4471 5.45658C9.90934 5.58245 9.57545 6.12043 9.70132 6.65818C9.82719 7.19593 10.3652 7.52982 10.9029 7.40394ZM20.5 12.2503L19.6181 11.7789C19.5333 11.9375 19.4452 12.0943 19.3539 12.2492C19.4074 12.3389 19.4525 12.4174 19.4893 12.4827C19.531 12.5569 19.5617 12.6138 19.5812 12.6506C19.5909 12.669 19.5978 12.6823 19.6019 12.6903L19.6058 12.698C19.6057 12.6978 19.6056 12.6975 20.5 12.2503Z"
        fill="currentColor"
      />
    </svg>
  </SvgIcon>
);
