import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { Design } from '../../helpers/Design';

export const HomeIcon = (props: SvgIconProps) => (
  <SvgIcon style={{ color: Design.Alias.Color.neutral800 }} {...props}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="HouseLine">
        <path
          d="M23.0397 20.48H21.5197V13.6401L21.742 13.8624C21.8849 14.005 22.0785 14.085 22.2804 14.0848C22.4822 14.0847 22.6758 14.0043 22.8184 13.8615C22.961 13.7186 23.041 13.5249 23.0408 13.3231C23.0406 13.1212 22.9603 12.9277 22.8174 12.7851L13.4743 3.44484C13.1893 3.16 12.8028 3 12.3999 3C11.9969 3 11.6104 3.16 11.3254 3.44484L1.98229 12.7851C1.83981 12.9277 1.75981 13.1211 1.7599 13.3227C1.75999 13.5243 1.84016 13.7176 1.98276 13.86C2.12537 14.0025 2.31873 14.0825 2.52032 14.0824C2.7219 14.0823 2.91519 14.0022 3.05767 13.8596L3.27997 13.6401V20.48H1.75999C1.55843 20.48 1.36512 20.5601 1.2226 20.7026C1.08007 20.8451 1 21.0384 1 21.24C1 21.4416 1.08007 21.6349 1.2226 21.7774C1.36512 21.9199 1.55843 22 1.75999 22H23.0397C23.2413 22 23.4346 21.9199 23.5771 21.7774C23.7196 21.6349 23.7997 21.4416 23.7997 21.24C23.7997 21.0384 23.7196 20.8451 23.5771 20.7026C23.4346 20.5601 23.2413 20.48 23.0397 20.48ZM14.6798 20.48H10.1199V15.9201C10.1199 15.8193 10.1599 15.7226 10.2312 15.6514C10.3024 15.5801 10.3991 15.5401 10.4999 15.5401H14.2998C14.4006 15.5401 14.4973 15.5801 14.5685 15.6514C14.6398 15.7226 14.6798 15.8193 14.6798 15.9201V20.48Z"
          fill="currentColor"
        />
      </g>
    </svg>
  </SvgIcon>
);
