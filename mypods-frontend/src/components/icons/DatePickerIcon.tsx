import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { Design } from '../../helpers/Design';

export const DatePickerIcon = (props: SvgIconProps) => (
  <SvgIcon style={{ color: Design.Alias.Color.secondary500 }} {...props}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        className={`${props.className}`}
        d="M21.36 2.11055H17.9564V1.20603C17.9564 0.886171 17.8414 0.579413 17.6369 0.353238C17.4323 0.127063 17.1548 0 16.8655 0C16.5761 0 16.2987 0.127063 16.0941 0.353238C15.8895 0.579413 15.7746 0.886171 15.7746 1.20603V2.08643H8.22547V1.20603C8.22547 0.886171 8.11054 0.579413 7.90595 0.353238C7.70137 0.127063 7.42389 0 7.13456 0C6.84524 0 6.56776 0.127063 6.36317 0.353238C6.15859 0.579413 6.04365 0.886171 6.04365 1.20603V2.08643H2.64002C1.93299 2.10225 1.25992 2.42472 0.766027 2.98427C0.272136 3.54382 -0.00298344 4.29559 2.4406e-05 5.07739V21.0211C-0.000120113 21.8008 0.276269 22.5495 0.769856 23.1065C1.26344 23.6634 1.93488 23.9842 2.64002 24H21.36C22.0651 23.9842 22.7366 23.6634 23.2302 23.1065C23.7238 22.5495 24.0001 21.8008 24 21.0211V5.07739C23.9973 4.29976 23.7196 3.55413 23.2264 2.99978C22.7331 2.44542 22.0633 2.12629 21.36 2.11055ZM2.64002 4.52261H6.04365V6.199C6.04365 6.51885 6.15859 6.82561 6.36317 7.05179C6.56776 7.27796 6.84524 7.40503 7.13456 7.40503C7.42389 7.40503 7.70137 7.27796 7.90595 7.05179C8.11054 6.82561 8.22547 6.51885 8.22547 6.199V4.52261H15.7746V6.199C15.7746 6.51885 15.8895 6.82561 16.0941 7.05179C16.2987 7.27796 16.5761 7.40503 16.8655 7.40503C17.1548 7.40503 17.4323 7.27796 17.6369 7.05179C17.8414 6.82561 17.9564 6.51885 17.9564 6.199V4.52261H21.36C21.4922 4.52569 21.6182 4.58513 21.7118 4.68851C21.8053 4.79189 21.859 4.93122 21.8618 5.07739V8.69548C21.7475 8.64331 21.6255 8.6147 21.5018 8.61106H2.30184H2.1382V4.99296C2.15867 4.86195 2.22015 4.74306 2.31188 4.65708C2.40361 4.5711 2.51976 4.5235 2.64002 4.52261ZM21.36 21.5879H2.64002C2.5137 21.573 2.39689 21.5067 2.31224 21.402C2.22759 21.2973 2.18112 21.1616 2.18184 21.0211V11.0111H2.34548H21.5455C21.6691 11.0074 21.7911 10.9788 21.9055 10.9266V20.9849C21.9103 21.0655 21.8995 21.1463 21.8738 21.2218C21.8481 21.2974 21.808 21.3661 21.7564 21.4232C21.7048 21.4802 21.6427 21.5245 21.5743 21.5529C21.5059 21.5813 21.4329 21.5933 21.36 21.5879Z"
        fill={props.fill ?? '#B1B1B1'}
      />
    </svg>
  </SvgIcon>
);
