import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';
import { Design } from '../../helpers/Design';

export const DownloadIcon = (props: SvgIconProps) => (
  <SvgIcon style={{ color: Design.Alias.Color.secondary500 }} {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
      <path
        d="M4 16.5L4 17.5C4 19.1569 5.34315 20.5 7 20.5L17 20.5C18.6569 20.5 20 19.1569 20 17.5L20 16.5M16 12.5L12 16.5M12 16.5L8 12.5M12 16.5L12 4.5"
        stroke={Design.Alias.Color.secondary500}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </SvgIcon>
);
