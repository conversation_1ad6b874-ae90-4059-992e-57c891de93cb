import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';

export const BoxIcon = (props: SvgIconProps) => (
  <SvgIcon {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
      <path
        d="M4 3.5C2.89543 3.5 2 4.39543 2 5.5C2 6.60457 2.89543 7.5 4 7.5H16C17.1046 7.5 18 6.60457 18 5.5C18 4.39543 17.1046 3.5 16 3.5H4Z"
        fill="#083544"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3 8.5H17V15.5C17 16.6046 16.1046 17.5 15 17.5H5C3.89543 17.5 3 16.6046 3 15.5V8.5ZM8 11.5C8 10.9477 8.44772 10.5 9 10.5H11C11.5523 10.5 12 10.9477 12 11.5C12 12.0523 11.5523 12.5 11 12.5H9C8.44772 12.5 8 12.0523 8 11.5Z"
        fill="#083544"
      />
    </svg>
  </SvgIcon>
);
