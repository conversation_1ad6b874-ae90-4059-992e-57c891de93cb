import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';

export const DISMISS_ICON_ID = 'dismiss-icon';

export const DismissXIcon = (props: SvgIconProps) => (
  <SvgIcon data-testid={DISMISS_ICON_ID} {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
      <path
        d="M15.8346 5.84102L14.6596 4.66602L10.0013 9.32435L5.34297 4.66602L4.16797 5.84102L8.8263 10.4993L4.16797 15.1577L5.34297 16.3327L10.0013 11.6743L14.6596 16.3327L15.8346 15.1577L11.1763 10.4993L15.8346 5.84102Z"
        fill="#993600"
      />
    </svg>
    ;
  </SvgIcon>
);
