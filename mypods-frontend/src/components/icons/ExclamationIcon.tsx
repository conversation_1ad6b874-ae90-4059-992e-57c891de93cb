import React from 'react';
import { SvgIcon } from '@mui/material';
import { SvgIconProps } from '@mui/material/SvgIcon/SvgIcon';

export const EXCLAMATION_ICON_ID = 'exclamation-icon';

export const ExclamationIcon = (props: SvgIconProps) => (
  <SvgIcon data-testid={EXCLAMATION_ICON_ID} style={{ color: 'white' }} {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.7999 11.0002C19.7999 15.8603 15.8601 19.8002 11 19.8002C6.13985 19.8002 2.19995 15.8603 2.19995 11.0002C2.19995 6.14009 6.13985 2.2002 11 2.2002C15.8601 2.2002 19.7999 6.14009 19.7999 11.0002ZM12.1 15.4002C12.1 16.0077 11.6075 16.5002 11 16.5002C10.3924 16.5002 9.89995 16.0077 9.89995 15.4002C9.89995 14.7927 10.3924 14.3002 11 14.3002C11.6075 14.3002 12.1 14.7927 12.1 15.4002ZM11 5.5002C10.3924 5.5002 9.89995 5.99268 9.89995 6.60019V11.0002C9.89995 11.6077 10.3924 12.1002 11 12.1002C11.6075 12.1002 12.1 11.6077 12.1 11.0002V6.60019C12.1 5.99268 11.6075 5.5002 11 5.5002Z"
        fill="currentcolor"
      />
    </svg>
  </SvgIcon>
);
