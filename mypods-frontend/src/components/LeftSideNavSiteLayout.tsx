import React, { ReactNode } from 'react';
import { Grid } from '@mui/material';
import { Design } from '../helpers/Design';
import { LegacyLeftSideNavigationBar } from './legacyComponents/NavigationBar/LegacyLeftSideNavigation/LegacyLeftSideNavigationBar';

interface LayoutProps {
  children: ReactNode;
  showHeaderFooter?: boolean;
  globalBanners?: ReactNode;
}

export const LeftSideNavSiteLayout: React.FC<LayoutProps> = ({ children }: LayoutProps) => (
  <Grid {...styles.container}>
    <LegacyLeftSideNavigationBar></LegacyLeftSideNavigationBar>
    <Grid item xs={12} md sx={styles.mainContent}>
      {children}
    </Grid>
  </Grid>
);

// -- styles --
const styles = {
  container: {
    sx: {
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      '@media (min-width: 768px)': {
        flexDirection: 'row'
      }
    }
  },
  mainContent: {
    sx: {
      flex: 1,
      display: 'flex',
      alignItems: 'center',
      flexDirection: 'column',
      paddingTop: {
        xs: Design.Primitives.Spacing.md,
        md: Design.Primitives.Spacing.lgPlus
      }
    }
  }
};
