import React from 'react';
import { ButtonBase, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Design } from '../../helpers/Design';
import { CancelIcon } from '../icons/CancelIcon';
import { TranslationKeys } from '../../locales/TranslationKeys';

export const CancelVisitButton: React.FC<{ onClick: () => void; disabled: boolean }> = ({
  onClick,
  disabled = false
}) => {
  const { t: translate } = useTranslation();
  return (
    <ButtonBase
      data-testid="cancel-visit-button"
      onClick={onClick}
      disabled={disabled}
      sx={{
        padding: 0,
        margin: 0,
        width: '64px',
        height: '20px',
        textTransform: 'none',
        borderRadius: '3px'
      }}>
      <Typography
        style={Design.Alias.Text.BodyUniversal.XsBold}
        sx={{ marginRight: '4px' }}
        color={Design.Alias.Color.secondary500}>
        {translate(TranslationKeys.CommonComponents.CANCEL_BUTTON)}
      </Typography>
      <CancelIcon sx={{ height: '20px', width: '20px' }} />
    </ButtonBase>
  );
};
