import React, { CSSProperties } from 'react';
import { Link, LinkProps } from 'react-router-dom';
import { Design } from '../../helpers/Design';

export const BlueLink: React.FC<LinkProps> = (props: LinkProps) => (
  <Link {...props} style={{ ...styles, ...props.style }}>
    {props.children}
  </Link>
);

const styles: CSSProperties = {
  ...Design.Alias.Text.BodyUniversal.SmBold,
  color: Design.Alias.Color.secondary500,
  textDecorationLine: 'underline',
  textAlign: 'right'
};
