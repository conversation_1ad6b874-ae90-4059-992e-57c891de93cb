import React from 'react';
import { ButtonBase, Typography } from '@mui/material';
import ChatIcon from '@mui/icons-material/Chat';
import { Design } from '../../helpers/Design';

export const ChatButton: React.FC<{
  onClick: () => void;
  children: React.ReactNode;
}> = ({ onClick, children }) => (
  <ButtonBase
    onClick={onClick}
    sx={{
      textTransform: 'none',
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '8px 16px',
      gap: '4px',
      border: '1px solid',
      borderColor: Design.Alias.Color.secondary700,
      borderRadius: '4px',
      cursor: 'pointer',
      background: Design.Alias.Color.secondary500
    }}>
    <ChatIcon sx={{ fontSize: '1rem', color: Design.Alias.Color.neutral100 }} />
    <Typography
      style={{
        ...Design.Alias.Text.BodyUniversal.SmBold,
        color: Design.Alias.Color.neutral100
      }}>
      {children}
    </Typography>
  </ButtonBase>
);
