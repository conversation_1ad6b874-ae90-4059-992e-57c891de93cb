import React, { ReactNode } from 'react';
import { Card, FormControlLabel, Radio } from '@mui/material';
import { Design } from '../../helpers/Design';
import { mergeStyles } from '../../helpers/styleHelpers';

// -- types --
interface Props {
  label: ReactNode;
  value: any;
  isSelected: boolean;
  onClick: (value: any) => void;
  disabled?: boolean;
}

interface TestProps {
  'data-testid'?: string;
}

// -- impls --
export const CardRadioButton = ({
  label,
  value,
  isSelected,
  onClick,
  disabled,
  ...props
}: Props & TestProps) => {
  const styles = style();

  // -- view --
  return (
    <Card
      {...mergeStyles(styles.card, isSelected && styles.selectedCard)}
      onClick={() => onClick(value)}
      data-testid={`${props['data-testid']}${isSelected ? '-selected' : ''}`}>
      <FormControlLabel
        disabled={disabled ?? false}
        value={value}
        control={<Radio {...styles.radioButton} checked={isSelected} />}
        label={label}
      />
    </Card>
  );
};

const style = () => ({
  card: {
    variant: 'outlined',
    sx: {
      borderColor: Design.Alias.Color.neutral300,
      borderRadius: Design.Primitives.Spacing.xxxs,
      borderWidth: '1px',
      paddingY: Design.Primitives.Spacing.xxs,
      paddingX: Design.Primitives.Spacing.sm,
      flexGrow: 1,
      // height: Design.Primitives.Spacing.lg,
      cursor: 'pointer',
      '&:hover': {
        borderColor: Design.Alias.Color.secondary300
      }
    }
  },
  selectedCard: {
    sx: {
      borderColor: Design.Alias.Color.secondary500,
      borderWidth: '2px'
    }
  },
  radioButton: {
    sx: {
      '&.Mui-checked': {
        color: Design.Alias.Color.secondary500
      }
    }
  }
});
