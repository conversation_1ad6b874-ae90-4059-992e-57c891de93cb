import React from 'react';
import { ButtonBase, Typography } from '@mui/material';
import { Design } from '../../helpers/Design';
import { DeviceMobileIcon } from '../icons/DeviceMobileIcon';

export const SMSButton: React.FC<{
  SMSNumber: string;
  SMSBody: string;
  children: React.ReactNode;
}> = ({ SMSNumber, SMSBody, children }) => (
  <ButtonBase
    href={`sms:${SMSNumber}?body=${SMSBody}`}
    sx={{
      textTransform: 'none',
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '8px 16px',
      gap: '4px',
      border: '1px solid',
      borderColor: Design.Alias.Color.secondary500,
      borderRadius: '4px',
      cursor: 'pointer',
      background: Design.Alias.Color.neutral100
    }}>
    <DeviceMobileIcon sx={{ fontSize: '1rem', color: Design.Alias.Color.secondary500 }} />
    <Typography
      style={{
        ...Design.Alias.Text.BodyUniversal.SmBold,
        color: Design.Alias.Color.secondary500
      }}>
      {children}
    </Typography>
  </ButtonBase>
);
