import { AddSignatureButtonCustomer } from '../AddSignature';
import { renderWithPoetProvidersAndState } from '../../../testUtils/RenderHelpers';
import { createCustomer, createRefreshSessionClaims } from '../../../testUtils/MyPodsFactories';
import { TranslationKeys } from '../../../locales/TranslationKeys';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { act, useState } from 'react';
import { formatToLocale } from '../../../helpers/dateHelpers';
import { mockGetCustomer, mockRefreshSession } from '../../../../setupTests';

const refreshSessionClaims = createRefreshSessionClaims();
const customer = createCustomer({ ...refreshSessionClaims });

const TestComponent = () => {
  const [isSigned, setIsSigned] = useState<boolean>(false);
  return (
    <AddSignatureButtonCustomer
      isSigned={isSigned}
      handleSignClicked={() => setIsSigned(true)}
      customer={customer}
    />
  );
};

describe('add signature button', () => {
  beforeEach(() => {
    mockRefreshSession.mockResolvedValue(refreshSessionClaims);
  });

  const renderButton = () => {
    renderWithPoetProvidersAndState(<TestComponent />);
  };

  it('should show a button with the appropriate translation', async () => {
    renderButton();

    expect(
      await screen.findByText(TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT)
    ).toBeInTheDocument();
  });

  it('the button should be clickable and show the label, customer name, and signed date', async () => {
    renderButton();

    const button = await screen.findByText(
      TranslationKeys.CommonComponents.AddSignature.BUTTON_TEXT
    );
    await act(async () => {
      await userEvent.click(button);
    });

    expect(
      screen.getByText(TranslationKeys.CommonComponents.AddSignature.ELECTRONICALLY_SIGNED)
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        `${TranslationKeys.CommonComponents.AddSignature.SIGNED_DATE_LABEL} ${formatToLocale(new Date())}`
      )
    ).toBeInTheDocument();
    expect(screen.getByText(`${customer.firstName} ${customer.lastName}`)).toBeInTheDocument();
  });
});
