import { useTranslation } from 'react-i18next';
import Button from '@mui/material/Button';
import React from 'react';
import { TranslationKeys } from '../../locales/TranslationKeys';

interface CancelButtonProps {
  onClick: () => void;
  disabled?: boolean;
}

export const CancelButton = ({ onClick, disabled = false }: CancelButtonProps) => {
  const { t: translate } = useTranslation();
  const label = translate(TranslationKeys.CommonComponents.CANCEL_BUTTON);

  // Prevents blur effects from validating before canceling
  // MouseDown is used to run before the blur events
  const handleOnClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    event.preventDefault();
    onClick();
  };

  return (
    <Button
      disabled={disabled}
      color="secondary"
      variant="outlined"
      sx={{ textTransform: 'none', height: '40px', width: '112px' }}
      aria-label={label}
      onMouseDown={handleOnClick}>
      {label}
    </Button>
  );
};
