import { useTranslation } from 'react-i18next';
import React from 'react';
import { CircularProgress } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { TranslationKeys } from '../../locales/TranslationKeys';

interface SaveButtonProps {
  onClick: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  label?: string;
  styles?: any;
}

export const SaveButton = ({ onClick, isLoading, disabled, label, styles }: SaveButtonProps) => {
  const { t: translate } = useTranslation();
  return (
    <LoadingButton
      loadingPosition={isLoading ? 'start' : undefined}
      startIcon={isLoading && <CircularProgress size={20} color="inherit" />}
      centerRipple
      disableElevation
      color="secondary"
      variant="contained"
      disabled={isLoading || disabled}
      {...styles}
      onClick={onClick}>
      {translate(label ?? TranslationKeys.CommonComponents.SAVE_BUTTON)}
    </LoadingButton>
  );
};
