import React from 'react';
import { useTranslation } from 'react-i18next';
import { CircularProgress } from '@mui/material';
import { LoadingButton, LoadingButtonProps } from '@mui/lab';
import merge from 'lodash/merge';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { Design } from '../../helpers/Design';

interface StretchableLoadingButtonProps extends LoadingButtonProps {
  isLoading?: boolean;
  isMobile: boolean;
  label?: string;
  customStyles?: any;
  variant?: 'contained' | 'outlined';
}

export const StretchableLoadingButton = ({
  isLoading,
  label,
  customStyles,
  disabled,
  onClick,
  variant = 'contained'
}: StretchableLoadingButtonProps) => {
  const style = styles();
  const { t: translate } = useTranslation();

  return (
    <LoadingButton
      loadingPosition={isLoading ? 'start' : undefined}
      startIcon={
        isLoading && <CircularProgress data-testid="loadingIcon" size={20} color="inherit" />
      }
      centerRipple
      disableElevation
      color="secondary"
      variant={variant}
      disabled={isLoading || disabled}
      onClick={onClick}
      {...merge(style.button, customStyles)}>
      {translate(label ?? TranslationKeys.CommonComponents.SAVE_BUTTON)}
    </LoadingButton>
  );
};

// -- styles --
const styles = () => ({
  button: {
    sx: {
      width: '100%',
      ...Design.Alias.Text.BodyUniversal.MdBold
    }
  }
});
