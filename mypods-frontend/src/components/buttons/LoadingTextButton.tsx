import React from 'react';
import { CircularProgress, Typography } from '@mui/material';
import { LoadingButton, LoadingButtonProps } from '@mui/lab';
import { Design } from '../../helpers/Design';

interface LoadingTextButtonProps extends LoadingButtonProps {
  isLoading?: boolean;
  onClick: () => void;
  label: string;
}

export const LoadingTextButton = ({
  isLoading,
  onClick,
  label,
  ...props
}: LoadingTextButtonProps) => (
  <LoadingButton
    loadingPosition={isLoading ? 'end' : undefined}
    endIcon={isLoading && <CircularProgress size={20} color="inherit" />}
    centerRipple
    disableElevation
    variant="text"
    onClick={onClick}
    disabled={isLoading}
    {...styles}
    {...props}>
    <Typography {...styles.label}>{label}</Typography>
  </LoadingButton>
);

// -- styles --
const styles = {
  sx: {
    width: '100%',
    color: Design.Alias.Color.secondary500,
    ...Design.Alias.Text.BodyUniversal.MdBold,
    '&:disabled': {
      color: Design.Alias.Color.secondary500
    }
  },
  label: {
    sx: {
      textTransform: 'capitalize',
      color: 'inherit',
      ...Design.Alias.Text.BodyUniversal.SmBold
    }
  }
};
