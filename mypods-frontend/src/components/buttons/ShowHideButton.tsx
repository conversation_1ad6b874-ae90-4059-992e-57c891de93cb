import React from 'react';
import { ButtonBase } from '@mui/material';
import { ShowIcon } from '../icons/ShowIcon';
import { HideIcon } from '../icons/HideIcon';
import { Design } from '../../helpers/Design';

export const ShowHideButton = ({
  color = Design.Alias.Color.secondary500,
  ...props
}: {
  onClick: () => void;
  dataVisible: boolean;
  color?: string;
}) => (
  <ButtonBase onClick={props.onClick} aria-label="Show/Hide Button">
    {props.dataVisible ? (
      <HideIcon sx={{ height: '24px', width: '24px' }} style={{ color }} />
    ) : (
      <ShowIcon sx={{ height: '24px', width: '24px' }} style={{ color }} />
    )}
  </ButtonBase>
);
