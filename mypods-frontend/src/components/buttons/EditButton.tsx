import React from 'react';
import { ButtonBase, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { EditIcon } from '../icons/EditIcon';
import { Design } from '../../helpers/Design';
import { TranslationKeys } from '../../locales/TranslationKeys';

export const EditButton: React.FC<{
  onClick: () => void;
  dataTestId: string;
  disabled?: boolean;
}> = ({ onClick, dataTestId, disabled = false }) => {
  const { t: translate } = useTranslation();
  return (
    <ButtonBase
      data-testid={`${dataTestId}-edit-button`}
      onClick={onClick}
      sx={{
        padding: 0,
        margin: 0,
        width: Design.Primitives.Spacing.lgPlus,
        height: '20px',
        textTransform: 'none',
        borderRadius: '3px'
      }}
      disabled={disabled}>
      <Typography
        style={Design.Alias.Text.BodyUniversal.XsBold}
        sx={{ marginRight: '4px' }}
        color={disabled ? Design.Alias.Color.neutral400 : Design.Alias.Color.secondary500}>
        {translate(TranslationKeys.CommonComponents.EDIT_BUTTON)}
      </Typography>
      <EditIcon
        sx={{
          height: '20px',
          width: '20px',
          stroke: disabled ? Design.Alias.Color.neutral400 : Design.Alias.Color.secondary500
        }}
      />
    </ButtonBase>
  );
};
