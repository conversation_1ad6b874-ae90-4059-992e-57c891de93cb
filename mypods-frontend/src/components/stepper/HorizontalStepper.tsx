import React from 'react';
import { Stepper, Step, StepLabel } from '@mui/material';
import { Design } from '../../helpers/Design';

interface HorizontalStepperProps {
  steps: string[];
  activeStep: number;
  className?: string;
  style?: React.CSSProperties;
}

export const HorizontalStepper: React.FC<HorizontalStepperProps> = ({
  steps,
  activeStep,
  className,
  style
}) => (
  <Stepper
    activeStep={activeStep}
    alternativeLabel
    className={className}
    style={style}
    sx={{
      '& .MuiStepIcon-root': {
        color: Design.Alias.Color.accent900,
        '&.Mui-active': {
          color: Design.Alias.Color.secondary500
        },
        '&.Mui-completed': {
          color: Design.Alias.Color.accent900
        }
      },
      '& .MuiStepLabel-label': {
        color: Design.Alias.Color.accent900,
        '&.Mui-active': {
          color: Design.Alias.Color.accent900
        },
        '&.Mui-completed': {
          color: Design.Alias.Color.accent900
        }
      }
    }}>
    {steps.map((label) => (
      <Step key={label}>
        <StepLabel sx={{ color: Design.Alias.Color.accent900 }}>{label}</StepLabel>
      </Step>
    ))}
  </Stepper>
);
