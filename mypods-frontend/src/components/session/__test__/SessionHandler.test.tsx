import React, { act } from 'react';
import { screen } from '@testing-library/react';
import { vi } from 'vitest';
import userEvent from '@testing-library/user-event';
import { addMinutes } from 'date-fns';
import { mockRefreshSession } from '../../../../setupTests';
import { renderWithQueryProvider } from '../../../testUtils/RenderHelpers';
import { SessionHandler } from '../SessionHandler';

const mocks = vi.hoisted(() => ({
  redirectToLoginWithStatusMock: vi.fn()
}));

vi.mock('../../../context/ApigeeContext', () => ({
  redirectToLoginWithStatus: mocks.redirectToLoginWithStatusMock
}));

vi.mock('../../../environment', () => ({
  ENVIRONMENT: 'not-local',
  ENV_VARS: {}
}));

describe('SessionHandler', () => {
  const now = new Date();
  // TODO: Test the maintenance mode redirect
  // const maintenanceMode = createEntryPointResult({
  //   maintenanceModeEnabled: true,
  //   outstandingRentalAgreements: []
  // });

  beforeAll(() => vi.useFakeTimers({ shouldAdvanceTime: true }));

  beforeEach(() => {
    vi.setSystemTime(now);
    localStorage.clear();
    mockRefreshSession.mockResolvedValue({
      firstName: 'firstName',
      lastName: 'lastName'
    });
  });

  afterAll(() => {
    vi.useRealTimers();
  });

  it('should redirect to login if inactivity exceeds limit', async () => {
    localStorage.setItem('session-timeout-minutes', '1');

    renderWithQueryProvider(
      <SessionHandler>
        <p>Hello</p>
      </SessionHandler>
    );

    await act(() => vi.runOnlyPendingTimersAsync());
    await act(() => vi.advanceTimersByTimeAsync(30 * 1000));

    expect(mocks.redirectToLoginWithStatusMock).not.toHaveBeenCalled();

    await act(() => vi.advanceTimersByTimeAsync(60 * 1000));
    expect(mocks.redirectToLoginWithStatusMock).toHaveBeenCalledWith('SESSION_EXPIRED');
  });

  it('should refresh the session on click', async () => {
    const user = userEvent.setup({ delay: null });
    renderWithQueryProvider(
      <SessionHandler>
        <p data-testid="hello">Hello</p>
      </SessionHandler>
    );

    await act(() => vi.runOnlyPendingTimersAsync());

    await user.click(screen.getByText(/hello/i));
    vi.clearAllMocks();
    vi.setSystemTime(addMinutes(new Date(), 6));

    await user.click(screen.getByText(/hello/i));

    expect(mockRefreshSession).toHaveBeenCalled();
  });

  it('should not refresh the session, if the user has recently been active', async () => {
    const user = userEvent.setup({ delay: null });
    renderWithQueryProvider(
      <SessionHandler>
        <p data-testid="hello">Hello</p>
      </SessionHandler>
    );

    await act(() => vi.runOnlyPendingTimersAsync());

    await user.click(screen.getByText(/hello/i));
    vi.clearAllMocks();
    vi.setSystemTime(addMinutes(new Date(), 4));
    await user.click(screen.getByText(/hello/i));

    expect(mockRefreshSession).not.toHaveBeenCalled();
  });
});
