import React from 'react';
import { redirectToLoginWithStatus } from '../../context/ApigeeContext';
import { useSessionHandler } from './useSessionHandler';
import { useStartPodsReadySession } from '../../networkRequests/queries/podsReady/useStartPodsReadySession';

export const PodsReadySessionHandler = ({ children }: { children: React.ReactNode }) => {
  const { refetch } = useStartPodsReadySession();

  useSessionHandler({
    refreshSession: async () => {
      await refetch();
    },
    lastRefreshKey: 'pods-ready-session-last-refreshed',
    lastActivityKey: 'pods-ready-session-last-activity',
    sessionTimeoutMinutesKey: 'pods-ready-session-timeout-minutes',
    onSessionExpire: () => redirectToLoginWithStatus('SESSION_EXPIRED')
  });

  return children;
};
