import React from 'react';
import { useRefreshSession } from '../../networkRequests/queries/useRefreshSession';
import { redirectToLoginWithStatus } from '../../context/ApigeeContext';
import { useSessionHandler } from './useSessionHandler';

export const sessionTimeoutMinutesKey = 'session-timeout-minutes';

export const SessionHandler = ({ children }: { children: React.ReactNode }) => {
  const { refetch } = useRefreshSession();

  useSessionHandler({
    refreshSession: async () => {
      await refetch();
    },
    lastRefreshKey: 'session-last-refreshed',
    lastActivityKey: 'session-last-activity',
    sessionTimeoutMinutesKey,
    onSessionExpire: () => redirectToLoginWithStatus('SESSION_EXPIRED')
  });

  return children;
};
