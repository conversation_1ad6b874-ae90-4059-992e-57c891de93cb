import { useEffect } from 'react';
import { differenceInSeconds, isValid, parseISO } from 'date-fns';

export const useSessionHandler = ({
  refreshSession,
  lastRefreshKey,
  lastActivityKey,
  sessionTimeoutMinutesKey,
  defaultSessionTimeout = 60,
  inactiveTimeout = 5,
  pollInterval = 30 * 1000,
  onSessionExpire
}: {
  refreshSession: () => Promise<void>;
  lastRefreshKey: string;
  lastActivityKey: string;
  sessionTimeoutMinutesKey: string;
  defaultSessionTimeout?: number;
  inactiveTimeout?: number;
  pollInterval?: number;
  onSessionExpire: () => void;
}) => {
  const handleRefreshSession = async () => {
    const lastRefresh = localStorage.getItem(lastRefreshKey);
    localStorage.setItem(lastActivityKey, new Date().toISOString());

    if (lastRefresh != null && isValid(parseISO(lastRefresh))) {
      const lastRefreshDate = parseISO(lastRefresh);
      const diffInSeconds = differenceInSeconds(new Date(), lastRefreshDate);
      if (diffInSeconds < inactiveTimeout * 60) return;
    }

    try {
      await refreshSession();
      localStorage.setItem(lastRefreshKey, new Date().toISOString());
    } catch (error) {
      console.error('Session refresh failed:', error);
    }
  };

  function checkLastUserActivity() {
    const lastActivity = localStorage.getItem(lastActivityKey);
    const sessionTimeoutMinutes = parseInt(
      localStorage.getItem(sessionTimeoutMinutesKey) ?? String(defaultSessionTimeout)
    );

    if (lastActivity != null && isValid(parseISO(lastActivity))) {
      const lastActivityDateTime = parseISO(lastActivity);
      if (differenceInSeconds(new Date(), lastActivityDateTime) > sessionTimeoutMinutes * 60) {
        onSessionExpire();
        return;
      }
    }

    setTimeout(checkLastUserActivity, pollInterval);
  }

  useEffect(() => {
    localStorage.setItem(lastActivityKey, new Date().toISOString());
    checkLastUserActivity();
    document.body.addEventListener('click', handleRefreshSession);
    return () => {
      document.body.removeEventListener('click', handleRefreshSession);
    };
  }, []);
};
