import React, { createContext, FC, ReactNode, useContext, useMemo, useState } from 'react';
import { BottomSnackbar, Notification } from './BottomSnackbar';

interface NotificationContextProps {
  children: ReactNode;
}

export interface INotificationContext {
  notification?: Notification;
  setNotification: (notification: Notification) => void;
}

export const NotificationContext = createContext<INotificationContext>({
  setNotification(_): void {}
});

export default function useNotificationContext() {
  return useContext(NotificationContext);
}

export const NotificationProvider: FC<NotificationContextProps> = ({ children }) => {
  const [notification, setNotification] = useState<Notification | undefined>();

  const value = useMemo(() => ({ notification, setNotification }), []);

  return (
    <NotificationContext.Provider value={value}>
      <BottomSnackbar
        notification={notification}
        onNotificationCleared={() => setNotification(undefined)}
      />
      {children}
    </NotificationContext.Provider>
  );
};
