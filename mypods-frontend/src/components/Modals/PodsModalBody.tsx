import React from 'react';
import { Grid, useMediaQuery } from '@mui/material';
import { Design } from '../../helpers/Design';
import { theme } from '../../PodsTheme';

type Props = {
  children: React.ReactNode;
};

export const PodsModalContent = ({ children }: Props) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const style = styles(isMobile);

  return (
    <Grid item container {...style.content}>
      {children}
    </Grid>
  );
};

const styles = (isMobile: boolean) => ({
  content: {
    sx: {
      paddingY: Design.Primitives.Spacing.md,
      paddingX: isMobile ? Design.Primitives.Spacing.sm : Design.Primitives.Spacing.lgPlus
    }
  }
});
