import React from 'react';
import { Dialog, useMediaQuery } from '@mui/material';
import { theme } from '../../PodsTheme';

type Props = {
  open: boolean;
  children: React.ReactNode;
};

export const PodsModal = ({ open, children }: Props) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  return (
    <Dialog
      open={open}
      fullScreen={isMobile}
      fullWidth
      PaperProps={{ sx: { borderRadius: isMobile ? '' : '24px' } }}>
      {children}
    </Dialog>
  );
};
