import { Grid, Typography, useMediaQuery } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ModalAlertProps, RootModal } from './RootModal';
import { AddSignatureButtonCustomer } from '../buttons/AddSignature';
import { Design } from '../../helpers/Design';
import { theme } from '../../PodsTheme';
import { TranslationKeys } from '../../locales/TranslationKeys';
import { useEntryPointContext } from '../../context/EntryPointContext';
import { PodsAlertIcon, PodsAlertType } from '../alert/PodsAlert';
import { useGtmEvents } from '../../config/google/useGtmEvents';
import { ViewAgreementSteps } from '../../config/google/GoogleEntities';
import { OutstandingFnpsAgreement } from '../../networkRequests/responseEntities/AuthorizationEntities';
import { Customer } from '../../networkRequests/responseEntities/CustomerEntities';

interface Props {
  modalId: string;
  onClickActionButton: (fnpsAgreement: OutstandingFnpsAgreement, onSuccess?: () => void) => void;
  onClose: () => void;
  paginationText?: string;
  open: boolean;
  showAlert: boolean;
  isLoading: boolean;
  customer: Customer;
}

const Tx = TranslationKeys.FnpsModal;

export const FNPSModal = ({
  modalId,
  open,
  onClickActionButton,
  onClose,
  showAlert,
  isLoading,
  customer
}: Props) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const style = styles(isMobile);
  const { entryPointResult } = useEntryPointContext();
  const { t: translate } = useTranslation();

  const [pageNumber, setPageNumber] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [isSigned, setIsSigned] = useState<boolean>(false);
  const firstUnsignedFNPS =
    entryPointResult.outstandingFnpsAgreements.length > 0
      ? entryPointResult.outstandingFnpsAgreements[0]
      : null;
  const isNextButtonEnabled = isSigned;
  const gtmEvents = useGtmEvents();

  useEffect(() => {
    setPageNumber(1);
    setTotalPages(entryPointResult.outstandingFnpsAgreements.length);
    setIsSigned(false);
    if (open && firstUnsignedFNPS) {
      gtmEvents.startAgreement('fragile_non_paved_surface_waiver', firstUnsignedFNPS.orderId);
      gtmEvents.viewAgreementStep(ViewAgreementSteps.fnps.viewAndSign, firstUnsignedFNPS.orderId);
    }
  }, [open]);

  const onActionSuccess = () => () => {
    setIsSigned(false);
    setPageNumber((prev) => prev + 1);
  };

  const handleActionButtonClick = () => {
    if (firstUnsignedFNPS == null) return;

    onClickActionButton(firstUnsignedFNPS, onActionSuccess());
  };

  const alert: ModalAlertProps = {
    alertTitle: translate(Tx.ALERT_TITLE),
    alertDescription: translate(Tx.ALERT_DESCRIPTION),
    alertType: PodsAlertType.ERROR,
    alertIcon: PodsAlertIcon.ERROR
  };

  const getPaginationText = () => {
    if (totalPages === 1) return undefined;
    return translate(TranslationKeys.CommonComponents.PROGRESS_COUNTER, {
      current: pageNumber,
      total: totalPages
    });
  };

  return (
    firstUnsignedFNPS && (
      <RootModal
        open={open}
        modalId={modalId}
        showAlert={showAlert}
        alert={alert}
        title={translate(Tx.TITLE)}
        paginationText={getPaginationText()}
        actionButtonText={
          pageNumber < totalPages
            ? translate(TranslationKeys.CommonComponents.NEXT_BUTTON)
            : translate(Tx.ACTION_BUTTON_LABEL)
        }
        onClickActionButton={handleActionButtonClick}
        actionButtonEnabled={isNextButtonEnabled}
        isLoading={isLoading}
        onClickClose={onClose}>
        <Grid>
          <Typography {...style.body}>{translate(Tx.Body.INTRO)}</Typography>
          <br />
          <Typography {...style.body}>{translate(Tx.Body.CONDITIONS)}</Typography>
          <br />
          <Typography {...style.body}>{translate(Tx.Body.TERMS_CONFIRMATION)}</Typography>
          <Typography {...style.containerLocation}>
            {translate(Tx.Body.CONTAINER_LOCATION)}
          </Typography>
          <Typography {...style.body}>{firstUnsignedFNPS.address.address1}</Typography>
          <Typography {...style.body}>{firstUnsignedFNPS.address.address2}</Typography>
          <Typography {...style.body}>
            {firstUnsignedFNPS.address.city}, {firstUnsignedFNPS.address.state}{' '}
            {firstUnsignedFNPS.address.postalCode}
          </Typography>
        </Grid>
        <Grid {...style.signatureButton}>
          <AddSignatureButtonCustomer
            isSigned={isSigned}
            handleSignClicked={() => setIsSigned(true)}
            customer={customer}
          />
        </Grid>
      </RootModal>
    )
  );
};

const styles = (isMobile: boolean) => ({
  body: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.Md,
      color: Design.Alias.Color.accent900
    }
  },
  containerLocation: {
    sx: {
      ...Design.Alias.Text.BodyUniversal.MdBold,
      color: Design.Alias.Color.accent900,
      marginTop: '10px'
    }
  },
  signatureButton: {
    sx: {
      width: isMobile ? '100%' : '35%',
      marginTop: '25px'
    }
  }
});
