import React, { ReactNode } from 'react';
import { Dialog, Grid, IconButton, Typography, useMediaQuery } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { theme } from '../../PodsTheme';
import { Design } from '../../helpers/Design';
import { PodsAlert, PodsAlertIcon, PodsAlertType } from '../alert/PodsAlert';
import { StretchableLoadingButton } from '../buttons/StretchableLoadingButton';

type Props = {
  modalId: string;
  title: string;
  actionButtonText: string;
  actionButtonEnabled: boolean;
  onClickActionButton: () => void;
  onClickClose: () => void;
  paginationText?: string;
  open: boolean;
  showAlert?: boolean;
  isLoading?: boolean;
  alert?: ModalAlertProps;
  children: ReactNode;
};

export type ModalAlertProps = {
  alertTitle: string;
  alertDescription: string;
  alertType: PodsAlertType;
  alertIcon: PodsAlertIcon;
};

export const RootModal = ({
  modalId,
  title,
  actionButtonText,
  actionButtonEnabled = true,
  onClickActionButton,
  onClickClose = () => {},
  paginationText,
  open,
  showAlert,
  isLoading,
  alert,
  children
}: Props) => {
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { alertTitle, alertDescription, alertIcon, alertType } = alert ?? {};

  const style = styles(isMobile);

  return (
    <Dialog
      open={open}
      onClose={onClickClose}
      data-testid={modalId}
      fullScreen={isMobile}
      maxWidth={isMobile ? false : 'md'}>
      {showAlert && (
        <PodsAlert
          title={alertTitle ?? ''}
          description={alertDescription ?? ''}
          alertType={alertType}
          icon={alertIcon}
        />
      )}
      <Grid>
        <Grid container {...style.closeButton}>
          <IconButton onClick={onClickClose}>
            <CloseIcon />
          </IconButton>
        </Grid>
        <Grid container {...style.body}>
          <Grid item container>
            <Grid item>
              <Typography data-testid="pods-modal-pagination" {...style.pagination}>
                {paginationText}
              </Typography>
            </Grid>
          </Grid>
          <Grid>
            <Typography variant="h3" {...style.title}>
              {title}
            </Typography>
            {children}
          </Grid>
          <Grid {...style.buttonContainer}>
            <StretchableLoadingButton
              isLoading={isLoading}
              label={actionButtonText}
              isMobile={isMobile}
              disabled={!actionButtonEnabled}
              onClick={onClickActionButton}
              customStyles={style.actionButton}
            />
          </Grid>
        </Grid>
      </Grid>
    </Dialog>
  );
};

const styles = (isMobile: boolean) => ({
  closeButton: {
    sx: {
      paddingTop: '32px',
      paddingRight: Design.Primitives.Spacing.md,
      justifyContent: 'flex-end'
    }
  },
  body: {
    sx: {
      paddingLeft: isMobile ? Design.Primitives.Spacing.md : Design.Primitives.Spacing.lgPlus,
      paddingRight: isMobile ? Design.Primitives.Spacing.md : Design.Primitives.Spacing.lgPlus,
      paddingBottom: isMobile ? Design.Primitives.Spacing.sm : Design.Primitives.Spacing.lgPlus,
      justifyContent: 'center'
    }
  },
  pagination: {
    sx: {
      color: Design.Alias.Color.neutral700,
      marginBottom: Design.Primitives.Spacing.xxs,
      ...Design.Alias.Text.BodyUniversal.LgBold
    }
  },
  title: {
    sx: {
      ...(isMobile ? Design.Alias.Text.Heading.Mobile.Lg : Design.Alias.Text.Heading.Desktop.Lg),
      paddingBottom: '5px'
    }
  },
  buttonContainer: {
    sx: {
      marginTop: '24px',
      width: isMobile ? '100%' : '400px'
    }
  },
  actionButton: {
    sx: {
      backgroundColor: '#0069E5',
      height: Design.Primitives.Spacing.lgPlus,
      ':hover': {
        backgroundColor: '#0058C0'
      }
    }
  }
});
