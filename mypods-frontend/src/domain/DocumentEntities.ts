import {
  ANCILLAR<PERSON>_ITEM_DOCUMENTATION,
  BOOKING_CONFIRMATION,
  CPI_COO_ADDENDUM_ELECTRONIC,
  CPO_COO_ADDENDUM,
  DOCK_RECEIPT,
  IF_RENTAL_AGREEMENT,
  IF_RENTAL_AGREEMENT_ELECTRONIC,
  LOCAL_RENTAL_AGREEMENT,
  LOCAL_RENTAL_AGREEMENT_ELECTRONIC,
  NON_PAVED_SURFACE_WAIVER,
  ORDER_CONFIRMATION,
  ORDER_UPDATE_CONFIRMATION,
  PLACEMENT_WAIVER,
  SPONGY_MOTH_CERTIFICATION_FORM,
  WEIGHT_TICKET_EMPTY_CONTAINER,
  WEIGHT_TICKET_EMPTY_CONTAINER_WITH_TRUCK,
  WEIGHT_TICKET_FULL_CONTAINER,
  WEIGHT_TICKET_FULL_CONTAINER_WITH_TRUCK
} from '../helpers/Documents';
import {
  OrderDocumentType,
  PoetDocumentStatus,
  CustomerDocumentType
} from '../networkRequests/responseEntities/DocumentSharedSubtypes';

export type DocumentType = 'MasterOrder' | 'Invoice' | 'Statement';

export type DocumentDescription =
  | typeof ORDER_CONFIRMATION
  | typeof ORDER_UPDATE_CONFIRMATION
  | typeof LOCAL_RENTAL_AGREEMENT
  | typeof LOCAL_RENTAL_AGREEMENT_ELECTRONIC
  | typeof IF_RENTAL_AGREEMENT
  | typeof IF_RENTAL_AGREEMENT_ELECTRONIC
  | typeof NON_PAVED_SURFACE_WAIVER
  | typeof PLACEMENT_WAIVER
  | typeof SPONGY_MOTH_CERTIFICATION_FORM
  | typeof ANCILLARY_ITEM_DOCUMENTATION
  | typeof CPO_COO_ADDENDUM
  | typeof CPI_COO_ADDENDUM_ELECTRONIC
  | typeof WEIGHT_TICKET_EMPTY_CONTAINER
  | typeof WEIGHT_TICKET_EMPTY_CONTAINER_WITH_TRUCK
  | typeof WEIGHT_TICKET_FULL_CONTAINER
  | typeof WEIGHT_TICKET_FULL_CONTAINER_WITH_TRUCK
  | typeof BOOKING_CONFIRMATION
  | typeof DOCK_RECEIPT;

// Note: This had previously been Document, but it's a common FE Type that auto imports
export interface IDocument {
  orderId: string;
  id: string;
  title: DocumentDescription;
  type: DocumentType;
  companyCode: string;
  isPoet: boolean;
}

export interface OrderDocument {
  orderId: string;
  billingCompanyCode: string;
  docName: string;
  docPath?: string;
  isCustomerFacing: boolean;
  id: string;
  docType: OrderDocumentType;
  docStatus?: PoetDocumentStatus;
  isInterFranchise: boolean;
  title: DocumentDescription;
}

export interface CustomerDocument {
  docRef: string;
  docName: string;
  isCustomerFacing: boolean;
  id: string;
  docType: CustomerDocumentType;
  tags?: string;
  docNotes: string;
  invoiceNumber: string;
  title: DocumentDescription;
}
