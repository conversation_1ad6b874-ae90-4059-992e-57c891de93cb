import React, { ReactNode, useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { datadogLogs } from '@datadog/browser-logs';
import { useMediaQuery } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { ENV_VARS, Environment } from '../environment';
import { CustomerType } from '../networkRequests/responseEntities/CustomerEntities';
import { useStartPodsReadySession } from '../networkRequests/queries/podsReady/useStartPodsReadySession';
import { PodsReadyAccessTokenClaims } from '../networkRequests/responseEntities/PodsReadyEntities';
import { theme } from '../PodsTheme';

// @ts-ignore
window.ENV_VARS = ENV_VARS;

// @ts-ignore
export function initEmbeddedMessaging(w) {
  try {
    // @ts-ignore
    embeddedservice_bootstrap.settings.language = 'en_US';

    // @ts-ignore
    embeddedservice_bootstrap.init(
      w.ENV_VARS.SALESFORCE_CHAT_ID,
      w.ENV_VARS.SALESFORCE_CHAT_LABEL,
      w.ENV_VARS.SALESFORCE_CHAT_URL,
      {
        scrt2URL: w.ENV_VARS.SALESFORCE_CHAT_CERT_URL
      }
    );
  } catch (err) {
    console.error('Error loading Embedded Messaging: ', err);
  }
}

const addEventListener = (customer: PodsReadyAccessTokenClaims) => {
  window.addEventListener('onEmbeddedMessagingButtonClicked', () => {
    // @ts-ignore
    embeddedservice_bootstrap.prechatAPI.setVisiblePrechatFields({
      _assistanceType: {
        value: 'Residential Service',
        isEditableByEndUser: false
      },
      _webOrigin: {
        value: 'MyPODS',
        isEditableByEndUser: false
      },
      _firstName: {
        value: customer.firstName,
        isEditableByEndUser: true
      },
      _lastName: {
        value: customer.lastName,
        isEditableByEndUser: true
      },
      _email: {
        value: customer.email,
        isEditableByEndUser: true
      }
    });
  });
};

// @ts-ignore
export function initEmbeddedMessagingCommercial(w) {
  try {
    // @ts-ignore
    embeddedservice_bootstrap.settings.language = 'en_US';

    // @ts-ignore
    embeddedservice_bootstrap.init(
      w.ENV_VARS.SALESFORCE_CHAT_ID_COMMERCIAL,
      w.ENV_VARS.SALESFORCE_CHAT_LABEL_COMMERCIAL,
      w.ENV_VARS.SALESFORCE_CHAT_URL_COMMERCIAL,
      {
        scrt2URL: w.ENV_VARS.SALESFORCE_CHAT_CERT_URL_COMMERCIAL
      }
    );
  } catch (err) {
    console.error('Error loading Commercial Embedded Messaging: ', err);
  }
}

export function launchLiveChat(sessionClaims: PodsReadyAccessTokenClaims) {
  try {
    // @ts-ignore
    if (embeddedservice_bootstrap) {
      // @ts-ignore
      embeddedservice_bootstrap.prechatAPI.setVisiblePrechatFields({
        _assistanceType: {
          value: 'Residential Service',
          isEditableByEndUser: false
        },
        _webOrigin: {
          value: 'MyPODS',
          isEditableByEndUser: false
        },
        _firstName: {
          value: sessionClaims.firstName,
          isEditableByEndUser: true
        },
        _lastName: {
          value: sessionClaims.lastName,
          isEditableByEndUser: true
        },
        _email: {
          value: sessionClaims.email,
          isEditableByEndUser: true
        }
      });

      // @ts-ignore
      embeddedservice_bootstrap.utilAPI.launchChat();
    }
  } catch (error: any) {
    datadogLogs.logger.log(
      'Failed to launch the live chat',
      {
        error: error.message,
        stack: error.stack
      },
      'error'
    );
  }
}

export function showChatButton() {
  try {
    // @ts-ignore
    if (embeddedservice_bootstrap) {
      // @ts-ignore
      embeddedservice_bootstrap.utilAPI.showChatButton();
    }
  } catch (err) {
    console.error('Error calling Embedded Messaging show chat: ', err);
  }
}

export function hideChatButton() {
  try {
    // @ts-ignore
    if (embeddedservice_bootstrap) {
      // @ts-ignore
      embeddedservice_bootstrap.utilAPI.hideChatButton();
    }
  } catch (err) {
    console.error('Error calling Embedded Messaging hide chat: ', err);
  }
}

export const PodsReadyLiveChat: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { podsReadySessionClaims: sessionClaims } = useStartPodsReadySession();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const customerType = sessionClaims?.type ?? CustomerType.RESIDENTIAL;
  const pagesHideChatButton = [
    '/moth-fly-inspection',
    '/moth-fly-inspection-form',
    '/pods-ready/moth-fly-inspection',
    '/pods-ready/rental-agreement',
    '/pods-ready/set-password'
  ];
  const [isButtonShown, setIsButtonShown] = useState(true);

  useEffect(() => {
    addEventListener(sessionClaims);
  }, []);

  useEffect(() => {
    if (isMobile && pagesHideChatButton.includes(location.pathname)) {
      if (isButtonShown) {
        setIsButtonShown(false);
        hideChatButton();
      }
    } else if (!isButtonShown) {
      setIsButtonShown(true);
      showChatButton();
    }
  }, [location]);

  if (ENV_VARS.ENVIRONMENT === Environment.LOCAL) {
    return children;
  }

  return (
    <>
      <style type="text/css">
        {`
          .embeddedMessagingLiveRegion.embeddedMessagingLiveRegion {
            left: -1px;
          }`}
      </style>
      <Helmet async={false}>
        {customerType === CustomerType.RESIDENTIAL ? (
          <script
            src={`${ENV_VARS.SALESFORCE_CHAT_URL}/assets/js/bootstrap.min.js`}
            // @ts-ignore
            onLoad={`(${initEmbeddedMessaging.toString()})(window)`}
          />
        ) : (
          <script
            src={`${ENV_VARS.SALESFORCE_CHAT_URL_COMMERCIAL}/assets/js/bootstrap.min.js`}
            // @ts-ignore
            onLoad={`(${initEmbeddedMessagingCommercial.toString()})(window)`}
          />
        )}
      </Helmet>
      {children}
    </>
  );
};
