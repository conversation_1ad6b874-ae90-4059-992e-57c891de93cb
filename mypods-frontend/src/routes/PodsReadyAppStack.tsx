import React, { Suspense } from 'react';
import { Route, Routes } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { PodsReadySessionHandler } from '../components/session/PodsReadySessionHandler';
import LoadingScreen from '../components/Loading/LoadingScreen';
import { TranslationKeys } from '../locales/TranslationKeys';
import { redirectToLoginWithStatus } from '../context/ApigeeContext';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { TaskPage } from '../pages/PodsReadyPages/Task/TaskPage';
import { PodsReadyRoutes } from '../PodsReadyRoutes';
import { SetPasswordPage } from '../pages/PodsReadyPages/SetPassword/SetPasswordPage';
import { PodsReadyPage } from '../pages/PodsReadyPages/PodsReadyPage';
import { PodsReadySplitContextProvider } from '../context/SplitContext';
import { RentalAgreementOnboardingPage } from '../components/RentalAgreementOnboarding/RentalAgreementOnboardingPage';
import { PodsReadySuccessPage } from '../pages/PodsReadyPages/PodsReadySuccessPage';
import { MothInspectionVariantSelector } from '../pages/MothFlyInspectionFormPage/MothInspectionVariantSelector';

export const PodsReadyAppStack: React.FC = () => {
  const { t: translate } = useTranslation();
  const removeBaseRoute = (fullRoute: string): string =>
    fullRoute.replace(PodsReadyRoutes.BASE_INDEX, '');

  return (
    <ErrorBoundary
      renderError={(_) => (
        <LoadingScreen loadingText={translate(TranslationKeys.LoadingScreens.HOME_PAGE)} />
      )}
      redirectOnError={(status) => {
        if (status === 401 || status === 400) {
          redirectToLoginWithStatus('INVALID_PODS_READY_TOKEN');
        } else {
          redirectToLoginWithStatus('AUTH_ERROR');
        }
      }}>
      <Suspense
        fallback={
          <LoadingScreen loadingText={translate(TranslationKeys.LoadingScreens.HOME_PAGE)} />
        }>
        <PodsReadySessionHandler>
          <PodsReadySplitContextProvider>
            <Routes>
              <Route path="/" element={<PodsReadyPage />} />
              <Route
                path={removeBaseRoute(PodsReadyRoutes.SET_PASSWORD)}
                element={<SetPasswordPage />}
              />
              <Route path={removeBaseRoute(PodsReadyRoutes.TASKS)} element={<TaskPage />} />
              <Route
                path={removeBaseRoute(PodsReadyRoutes.RENTAL_AGREEMENT)}
                element={<RentalAgreementOnboardingPage />}
              />
              <Route
                path={removeBaseRoute(PodsReadyRoutes.SUCCESS)}
                element={<PodsReadySuccessPage />}
              />
              <Route
                path={removeBaseRoute(PodsReadyRoutes.MOTH_FLY_INSPECTION)}
                element={<MothInspectionVariantSelector />}
              />
            </Routes>
          </PodsReadySplitContextProvider>
        </PodsReadySessionHandler>
      </Suspense>
    </ErrorBoundary>
  );
};
