import { useLayoutEffect, ReactNode } from 'react';
import { useLocation } from 'react-router-dom';

// NOTE: In ReactRouter V7, this is an exported component.
// After the upgrade, we can delete ours in favor of the library's version.
export const ScrollRestoration = ({ children }: { children: ReactNode }) => {
  const location = useLocation();

  useLayoutEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  return children;
};
