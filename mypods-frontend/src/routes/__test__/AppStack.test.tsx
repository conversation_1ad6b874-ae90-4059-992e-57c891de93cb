import React from 'react';
import { screen } from '@testing-library/react';
import { AppStack } from '../AppStack';
import { renderWithPoetProvidersAndState } from '../../testUtils/RenderHelpers';
import {
  mockAuthorizationEntrypoint,
  mockedUseFeatureFlags,
  mockGetBillingInformation,
  mockGetCustomer,
  mockGetCustomerDocuments,
  mockGetCustomerOrders,
  mockGetOrderDocuments,
  mockGetPaymentMethods,
  mockRefreshSession
} from '../../../setupTests';
import {
  createBillingInformation,
  createCustomer,
  createEntryPointResult,
  createOrder,
  createUseFeatureFlagResult
} from '../../testUtils/MyPodsFactories';

describe('AppStack', () => {
  let mockBannerRef: React.MutableRefObject<HTMLDivElement>;

  async function renderStack() {
    return renderWithPoetProvidersAndState(
      <AppStack showHeaderFooter globalBannersRef={mockBannerRef} />
    );
  }

  beforeEach(() => {
    mockBannerRef = { current: document.createElement('div') };
    mockGetCustomerDocuments.mockResolvedValue([]);
    mockAuthorizationEntrypoint.mockResolvedValue(createEntryPointResult());
    mockGetCustomerOrders.mockResolvedValue([createOrder()]);
    mockGetPaymentMethods.mockResolvedValue([]);
    mockGetBillingInformation.mockResolvedValue(createBillingInformation());
    mockGetOrderDocuments.mockResolvedValue({ documents: [], token: 'token' });
    mockedUseFeatureFlags.useFeatureFlags.mockImplementation(() =>
      createUseFeatureFlagResult({
        isPoetEnabled: () => true,
        isAcornFinancingEnabled: () => true
      })
    );
  });

  describe('Initial Data', () => {
    it('should refresh the session before other calls, on the initial load', async () => {
      const customer = createCustomer();
      mockRefreshSession.mockResolvedValue({
        firstName: customer.firstName,
        lastName: customer.lastName
      });
      mockGetCustomer.mockResolvedValue(customer);

      await renderStack();

      expect(mockGetCustomer).not.toHaveBeenCalled();
      expect(mockRefreshSession).toHaveBeenCalled();
      expect(
        await screen.findByRole('button', { name: new RegExp(customer.firstName) })
      ).toBeInTheDocument();
      expect(mockGetCustomer).toHaveBeenCalled();
    });
  });
});
