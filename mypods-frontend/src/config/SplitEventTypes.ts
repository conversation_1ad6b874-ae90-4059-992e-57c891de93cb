export enum SplitEventType {
  MOTH_FORM_START = 'pb-moth-form-start',
  MOTH_FORM_SUBMIT = 'pb-moth-form-submit',
  MOTH_FORM_SUCCESS = 'pb-moth-form-success',
  MOTH_FORM_ERROR = 'pb-moth-form-Error',
  RENTAL_AGREEMENT_START = 'rental-agreement-start',
  RENTAL_AGREEMENT_SUBMIT_AGREEMENT = 'rental-agreement-submit-agreement',
  RENTAL_AGREEMENT_SUBMIT_SUCCESS = 'rental-agreement-submit-success',
  RENTAL_AGREEMENT_SUBMIT_FAILURE = 'rental-agreement-submit-failure ',
  PASSWORD_ONBOARDING_START = 'pb_proactive_password_onboarding_start',
  PASSWORD_ONBOARDING_COMPLETE = 'password_onboarding_complete',
  PODS_READY_START = 'pods-ready-start',
  PODS_READY_COMPLETE = 'pods-ready-complete'
}
