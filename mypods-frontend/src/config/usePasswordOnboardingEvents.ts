import { SplitEventType } from './SplitEventTypes';
import { useSplitEvents } from './useSplitEvents';
import { useLegacyGetCustomer } from '../networkRequests/legacy/queries/useLegacyGetCustomer';
import { useGetCustomer } from '../networkRequests/queries/useGetCustomer';

export const useLegacyPasswordOnboardingEvents = () => {
  const { customer } = useLegacyGetCustomer();
  return usePasswordOnboarding(customer.id);
};

export const usePoetPasswordOnboardingEvents = () => {
  const { customer } = useGetCustomer();
  return usePasswordOnboarding(customer.id);
};

export const usePasswordOnboarding = (customerId: string) => {
  const { send } = useSplitEvents(customerId);

  const sendPasswordOnboardingStart = () => {
    send(SplitEventType.PASSWORD_ONBOARDING_START);
  };

  const sendPasswordOnboardingComplete = () => {
    send(SplitEventType.PASSWORD_ONBOARDING_COMPLETE);
  };

  return {
    sendPasswordOnboardingStart,
    sendPasswordOnboardingComplete
  };
};
