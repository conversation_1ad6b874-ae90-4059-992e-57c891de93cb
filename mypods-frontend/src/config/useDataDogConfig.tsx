import { datadogRum } from '@datadog/browser-rum';
import { datadogLogs } from '@datadog/browser-logs';
import { ENV_VARS } from '../environment';
import { RequestHeaders } from './RequestHeaders';

export const useDataDogConfig = () => {
  const configure = async (uniqueVisitorID: string) => {
    const isSyntheticTest = uniqueVisitorID === RequestHeaders.SYNTHETIC_TEST_VISITOR_ID;

    if (ENV_VARS.DATADOG) {
      const sampleRate = isSyntheticTest ? 0 : 100;
      const replayRate = isSyntheticTest ? 0 : 17;
      datadogRum.init({
        applicationId: ENV_VARS.DATADOG.APPLICATION_ID,
        clientToken: ENV_VARS.DATADOG.CLIENT_TOKEN,
        site: 'us3.datadoghq.com',
        service: 'my.pods.com',
        env: ENV_VARS.DATADOG.ENVIRONMENT_NAME,
        allowedTracingUrls: [ENV_VARS.APIGEE_API_URL],
        // @ts-ignore
        version: import.meta.env.VITE_APP_GIT_SHA,
        sessionSampleRate: sampleRate,
        sessionReplaySampleRate: replayRate,
        trackUserInteractions: true,
        trackResources: true,
        trackLongTasks: true,
        defaultPrivacyLevel: 'mask-user-input',
        enableExperimentalFeatures: ['feature_flags']
      });
      datadogRum.setUser({
        id: uniqueVisitorID
      });
      datadogLogs.init({
        clientToken: ENV_VARS.DATADOG.CLIENT_TOKEN,
        site: 'us3.datadoghq.com',
        service: 'my.pods.com',
        env: ENV_VARS.DATADOG.ENVIRONMENT_NAME,
        version: import.meta.env.VITE_APP_GIT_SHA,
        sessionSampleRate: sampleRate
      });
    }
  };

  return {
    configure
  };
};
