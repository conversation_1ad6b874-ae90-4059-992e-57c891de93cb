import { useTrack } from '@splitsoftware/splitio-react';
import { getVisitorId } from './getVisitorId';

export const useSplitEvents = (customerId?: string) => {
  const uniqueVisitorID = getVisitorId();
  const splitTargetId = customerId ?? uniqueVisitorID;

  const track = useTrack(splitTargetId, 'user');
  const NO_VALUE = 0;
  const send = (eventType: string, value: number = NO_VALUE, properties: any = {}) => {
    track(eventType, value, properties);
  };
  return { send };
};
