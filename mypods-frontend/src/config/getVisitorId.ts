import { v4 as uuidv4 } from 'uuid';
import Cookies from 'universal-cookie';
import { RequestHeaders } from './RequestHeaders';
import { addDays } from '../helpers/dateHelpers';

export const getVisitorId = () => {
  const cookies = new Cookies();
  const uniqueVisitorID = cookies.get(RequestHeaders.UNIQUE_VISITOR_ID) ?? uuidv4();
  cookies.set(RequestHeaders.UNIQUE_VISITOR_ID, uniqueVisitorID, {
    // Cookie accessible on all pages
    path: '/',
    expires: addDays(new Date(), 90),
    secure: true,
    httpOnly: false,
    sameSite: 'strict',
    domain: 'localhost'
  });
  return uniqueVisitorID;
};
