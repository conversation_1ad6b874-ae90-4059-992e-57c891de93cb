// -- Top Level Types --
import { RentalAgreementType } from '../../networkRequests/responseEntities/AuthorizationEntities';
import { PaymentCardType } from '../../pages/ManagePaymentMethodPage/Braintree/PaymentMethodPayload';
import { CardIcon } from '../../components/icons/PaymentTypeIcon';
import { PaymentMethod } from '../../networkRequests/responseEntities/PaymentEntities';

export type GtmEventName =
  | 'user_detect'
  | 'start_agreement'
  | 'view_agreement_step'
  | 'decline_agreement'
  | 'submit_agreement'
  | 'success_agreement'
  | 'ssp_file_download'
  | 'start_placement_flow'
  | 'success_placement_flow'
  | 'view_placement_step'
  | 'submit_generate_statement'
  | 'success_generate_statement'
  | 'start_add_payment'
  | 'submit_add_payment'
  | 'success_add_payment'
  | 'submit_set_default_payment_method'
  | 'success_set_default_payment_method'
  | 'start_edit_account_detail'
  | 'submit_account_detail'
  | 'success_account_detail'
  | 'submit_payment'
  | 'success_payment'
  | 'start_schedule'
  | 'submit_schedule'
  | 'success_schedule'
  | 'start_edit_schedule'
  | 'submit_edit_schedule'
  | 'success_edit_schedule'
  | 'ssp_error';

export type GtmAgreementType =
  | 'rental_agreement_local'
  | 'rental_agreement_if'
  | 'fragile_non_paved_surface_waiver'
  | 'moth_fly_inventory';

export type GtmPaymentType = 'paypal' | 'card' | 'line-of-credit';

export type GtmAccountDetailType =
  | 'email'
  | 'phone_primary'
  | 'phone_secondary'
  | 'address_shipping'
  | 'address_billing'
  | 'sms_preference'
  | 'pin'
  | 'security_question'
  | 'password';

// -- View Agreement Steps --

export type ViewAgreementStep = {
  agreementType: GtmAgreementType;
  stepName: string;
};

export const viewAgreementStep = (agreementType: GtmAgreementType, stepName: string) => ({
  agreementType,
  stepName
});

// prettier-ignore
export const ViewAgreementSteps = {
  rentalAgreementLocal: {
    viewAndSign: viewAgreementStep(
      'rental_agreement_local',
      'view_and_sign_rental_agreement'
    )
  },
  rentalAgreementIF: {
    viewAndSign: viewAgreementStep(
      'rental_agreement_if',
      'view_and_sign_rental_agreement'
    )
  },
  fnps: {
    viewAndSign: viewAgreementStep(
      'fragile_non_paved_surface_waiver',
      'view_and_sign_fnps'
    )
  },
  moth: {
    faq: viewAgreementStep(
      'moth_fly_inventory',
      'view_faq'
    ),
    form: viewAgreementStep(
      'moth_fly_inventory',
      'view_and_sign_form'
    )
  }
};

// -- Placement Types --

type CommonPlacementType = {
  orderId: string;
  containerId: string;
  moveLegId: string;
  moveLegType: string;
};

export type StartPlacementType = CommonPlacementType;
export type SuccessPlacementType = CommonPlacementType;
export type ViewPlacementType = CommonPlacementType & { stepName: string };

// -- Getters --

export const gtmAgreementTypeFromRentalAgreement = (agreementType: RentalAgreementType) =>
  agreementType === 'IF' ? 'rental_agreement_if' : 'rental_agreement_local';

export const gtmAgreementTypeFromIsInterfranchise = (isInterFranchise: boolean) =>
  isInterFranchise ? 'rental_agreement_if' : 'rental_agreement_local';

export const gtmCardType = (cardType: string): GtmPaymentType => {
  const paypalCardType: PaymentCardType = 'PAYPAL';
  return cardType === paypalCardType ? 'paypal' : 'card';
};

export const gtmDefaultPaymentCardType = (cardType: string): GtmPaymentType => {
  if (cardType === CardIcon.PAYPAL) return 'paypal';
  if (cardType === CardIcon.LINE_OF_CREDIT) return 'line-of-credit';
  return 'card';
};

export type GtmSubmitPaymentType = {
  accountBalance: number;
  paymentMethod: PaymentMethod | undefined;
  paymentAmount: number;
};

export type GtmStartScheduleType = {
  transactionId: string;
  containerId: string;
  moveLegId: string;
  moveLegType: string;
};

export type GtmScheduleType = {
  transactionId: string;
  containerId: string;
  moveLegId: string;
  moveLegType: string;
  addressChanged: boolean;
  deliverySiteType?: string;
  containerPlacement?: string;
  pavedSurface?: boolean;
  gated: boolean;
  optionalNotes?: boolean;
};

export type GtmErrorMessage = {
  title: string;
  body: string;
};

export type GtmErrorType = 'backend' | 'front_end' | 'form_validation';

export type GtmErrorLocation =
  | 'account'
  | 'schedule'
  | 'edit_schedule'
  | 'agreement'
  | 'placement'
  | 'billing'
  | 'payment'
  | 'document'
  | 'general'
  | 'address_shipping'
  | 'password'
  | 'submit_edit_schedule'
  | 'submit_schedule'
  | 'rental_agreement_if'
  | 'rental_agreement_local';

export type GtmError = {
  errorMessage: GtmErrorMessage;
  errorType: GtmErrorType;
  errorLocation: GtmErrorLocation;
};
