import TagManager from 'react-gtm-module';
import { useRef } from 'react';
import {
  GtmAccountDetailType,
  GtmAgreementType,
  gtmDefaultPaymentCardType,
  GtmError,
  GtmEventName,
  GtmPaymentType,
  GtmScheduleType,
  GtmStartScheduleType,
  GtmSubmitPaymentType,
  StartPlacementType,
  SuccessPlacementType,
  ViewAgreementStep,
  ViewPlacementType
} from './GoogleEntities';
import { getVisitorId } from '../getVisitorId';
import { useLegacyRefreshSession } from '../../networkRequests/queries/useLegacyRefreshSession';
import { useRefreshSession } from '../../networkRequests/queries/useRefreshSession';
import { RefreshSessionClaims } from '../../networkRequests/responseEntities/AuthorizationEntities';
import { PodsReadyAccessTokenClaims } from '../../networkRequests/responseEntities/PodsReadyEntities';
import { Customer, CustomerType } from '../../networkRequests/responseEntities/CustomerEntities';
import { useScheduleMoveLegEvents } from '../useScheduleMoveLegEvents';

export const useLegacyGtmEvents = () => {
  const { sessionClaims } = useLegacyRefreshSession();
  return useGtmEventsWithSessionClaims(sessionClaims);
};

export const useGtmEvents = () => {
  const { sessionClaims } = useRefreshSession();
  return useGtmEventsWithSessionClaims(sessionClaims);
};

export const useGtmEventsWithCustomer = (customer: Customer) =>
  useGtmEventsWithData(
    customer.trackingUuid ?? '',
    customer.id,
    customer.customerType ?? CustomerType.RESIDENTIAL
  );

export const useGtmEventsWithSessionClaims = (
  sessionClaims: RefreshSessionClaims | PodsReadyAccessTokenClaims
) => useGtmEventsWithData(sessionClaims.trackingUuid, sessionClaims.customerId, sessionClaims.type);

export const useGtmEventsWithData = (
  trackingUuid: string,
  customerId: string,
  customerType: CustomerType
) => {
  const pageLoaded = useRef(false);
  const { schedulingSucceeded } = useScheduleMoveLegEvents();

  const pushDataLayer = (event: GtmEventName, eventModel: any) => {
    TagManager.dataLayer({
      dataLayer: {
        event,
        userModel: {
          user_id: trackingUuid,
          customer_id: customerId,
          visitor_id: getVisitorId(),
          customer_type: customerType
          // militaryStatus, // will return with Poet
          // militaryBranch
        },
        eventModel
      }
    });
  };

  const pageLoadEvent = () => {
    if (!pageLoaded.current) {
      pageLoaded.current = true;
      pushDataLayer('user_detect', {});
    }
  };

  const startAgreement = (agreementType: GtmAgreementType, orderId: string) => {
    pushDataLayer('start_agreement', {
      agreement_type: agreementType,
      transaction_id: orderId
    });
  };

  const viewAgreementStep = (step: ViewAgreementStep, orderId: string) => {
    pushDataLayer('view_agreement_step', {
      agreement_type: step.agreementType,
      step_name: step.stepName,
      transaction_id: orderId
    });
  };

  const declineAgreement = (agreementType: GtmAgreementType, orderId: string) => {
    pushDataLayer('decline_agreement', {
      agreement_type: agreementType,
      transaction_id: orderId
    });
  };

  const submitAgreement = (agreementType: GtmAgreementType, orderId: string) => {
    pushDataLayer('submit_agreement', {
      agreement_type: agreementType,
      transaction_id: orderId
    });
  };

  const successAgreement = (agreementType: GtmAgreementType, orderId: string) => {
    pushDataLayer('success_agreement', {
      agreement_type: agreementType,
      transaction_id: orderId
    });
  };

  const fileDownload = (documentType: string) => {
    pushDataLayer('ssp_file_download', {
      file_name: documentType
    });
  };

  const startPlacementFlow = (args: StartPlacementType) => {
    pushDataLayer('start_placement_flow', {
      transaction_id: args.orderId,
      container_id: args.containerId,
      move_leg_id: args.moveLegId,
      move_leg_type: args.moveLegType
    });
  };
  const successPlacementFlow = (args: SuccessPlacementType) => {
    pushDataLayer('success_placement_flow', {
      transaction_id: args.orderId,
      container_id: args.containerId,
      move_leg_id: args.moveLegId,
      move_leg_type: args.moveLegType
    });
  };
  const viewPlacementStep = (args: ViewPlacementType) => {
    pushDataLayer('view_placement_step', {
      step_name: args.stepName,
      transaction_id: args.orderId,
      container_id: args.containerId,
      move_leg_id: args.moveLegId,
      move_leg_type: args.moveLegType
    });
  };

  const submitGenerateStatement = (startDate: string, endDate: string) => {
    pushDataLayer('submit_generate_statement', {
      start_date: startDate,
      end_date: endDate
    });
  };

  const successGenerateStatement = (startDate: string, endDate: string) => {
    pushDataLayer('success_generate_statement', {
      start_date: startDate,
      end_date: endDate
    });
  };

  const startAddPayment = (paymentType: GtmPaymentType) => {
    pushDataLayer('start_add_payment', {
      payment_type: paymentType
    });
  };

  const submitAddPayment = (paymentType: GtmPaymentType) => {
    pushDataLayer('submit_add_payment', {
      payment_type: paymentType
    });
  };

  const successAddPayment = (paymentType: GtmPaymentType) => {
    pushDataLayer('success_add_payment', {
      payment_type: paymentType
    });
  };

  const submitSetDefaultPayment = (paymentType: GtmPaymentType) => {
    pushDataLayer('submit_set_default_payment_method', {
      payment_type: paymentType
    });
  };

  const successSetDefaultPayment = (paymentType: GtmPaymentType) => {
    pushDataLayer('success_set_default_payment_method', {
      payment_type: paymentType
    });
  };

  const startEditAccountDetail = (detailType: GtmAccountDetailType) => {
    pushDataLayer('start_edit_account_detail', {
      detail_type: detailType
    });
  };

  const submitAccountDetail = (detailType: GtmAccountDetailType) => {
    pushDataLayer('submit_account_detail', {
      detail_type: detailType
    });
  };

  const successAccountDetail = (detailType: GtmAccountDetailType) => {
    pushDataLayer('success_account_detail', {
      detail_type: detailType
    });
  };

  const submitPayment = (args: GtmSubmitPaymentType) => {
    pushDataLayer('submit_payment', {
      account_balance: args.accountBalance,
      payment_type: gtmDefaultPaymentCardType(args.paymentMethod?.cardType ?? 'card'),
      payment_amount: args.paymentAmount
    });
  };

  const successPayment = (args: GtmSubmitPaymentType) => {
    pushDataLayer('success_payment', {
      account_balance: args.accountBalance,
      payment_type: gtmDefaultPaymentCardType(args.paymentMethod?.cardType ?? 'card'),
      payment_amount: args.paymentAmount
    });
  };

  const startSchedule = (args: GtmStartScheduleType) => {
    pushScheduleEvent('start_schedule', args);
  };

  const startEditSchedule = (args: GtmStartScheduleType) => {
    pushScheduleEvent('start_edit_schedule', args);
  };

  const pushScheduleEvent = (event: GtmEventName, args: GtmStartScheduleType) => {
    pushDataLayer(event, {
      transaction_id: args.transactionId,
      container_id: args.containerId,
      move_leg_id: args.moveLegId,
      move_leg_type: args.moveLegType
    });
  };

  const pushMoveLegScheduleEvent = (event: GtmEventName, args: GtmScheduleType) => {
    pushDataLayer(event, {
      transaction_id: args.transactionId,
      container_id: args.containerId,
      move_leg_id: args.moveLegId,
      move_leg_type: args.moveLegType,
      address_changed: args.addressChanged,
      delivery_site_type: args.deliverySiteType,
      container_placement: args.containerPlacement,
      paved_surface: args.pavedSurface,
      gated: args.gated,
      optional_notes: args.optionalNotes
    });
    if (event === 'success_edit_schedule' || event === 'success_schedule') {
      schedulingSucceeded();
    }
  };

  const errorEvent = (args: GtmError) => {
    pushDataLayer('ssp_error', {
      errorMessage: args.errorMessage,
      errorType: args.errorType,
      errorLocation: args.errorLocation
    });
  };

  return {
    pageLoadEvent,
    startAgreement,
    viewAgreementStep,
    declineAgreement,
    submitAgreement,
    successAgreement,
    fileDownload,
    startPlacementFlow,
    successPlacementFlow,
    viewPlacementStep,
    submitGenerateStatement,
    successGenerateStatement,
    startAddPayment,
    submitAddPayment,
    successAddPayment,
    submitSetDefaultPayment,
    successSetDefaultPayment,
    startEditAccountDetail,
    submitAccountDetail,
    successAccountDetail,
    submitPayment,
    successPayment,
    startSchedule,
    startEditSchedule,
    pushMoveLegScheduleEvent,
    errorEvent
  };
};
