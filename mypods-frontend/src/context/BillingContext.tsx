import React, { createContext, FC, ReactNode, useContext, useMemo } from 'react';
import { QueryStatus } from '@tanstack/react-query';
import { PaymentMethod } from '../networkRequests/responseEntities/PaymentEntities';
import { BillingInformation } from '../networkRequests/responseEntities/BillingEntities';
import {
  financingEligible,
  findMostExpensiveEligibleOrder,
  OrdersForFinancing
} from '../pages/BillingPage/utility/FinancingEligibility';
import { useEntryPointContext } from './EntryPointContext';
import { useLegacyGetBillingInformation } from '../networkRequests/legacy/queries/useLegacyGetBillingInformation';
import { useLegacyGetPaymentMethods } from '../networkRequests/legacy/queries/useLegacyGetPaymentMethods';
import { useLegacyGetCustomer } from '../networkRequests/legacy/queries/useLegacyGetCustomer';
import useLegacyOrdersContext from './legacy/LegacyOrdersContext';

export type BillingContextProps = {
  paymentMethods: PaymentMethod[];
  isPaymentsPending: boolean;
  isPaymentsFetching: boolean;
  isPaymentsSuccess: boolean;
  isPaymentsError: boolean;
  paymentsError: Error | null;
  paymentStatus: QueryStatus;
  refetchPayments: () => void;
  billingInformation: BillingInformation;
  billingError: Error | null;
  isBillingInfoPending: boolean;
  refetchBillingInfo: () => void;
  isBillingInfoRefetching: boolean;
  isFinancingEligible: boolean;
  mostExpensiveOrder: OrdersForFinancing | undefined;
};

export const initialBillingState = {
  paymentMethods: [],
  isPaymentsPending: false,
  isPaymentsFetching: false,
  isPaymentsSuccess: true,
  isPaymentsError: false,
  paymentsError: null,
  paymentStatus: 'pending' as QueryStatus,
  refetchPayments: () => {},
  billingInformation: {
    invoices: [],
    monthlyStatements: [],
    totalBalance: 0
  },
  billingError: null,
  isBillingInfoPending: false,
  refetchBillingInfo: () => {},
  isBillingInfoRefetching: false,
  isFinancingEligible: false,
  mostExpensiveOrder: undefined
};

interface BillingProviderProps {
  children: ReactNode;
}

export const BillingContext = createContext<BillingContextProps>(initialBillingState);

export const useBillingContext = () => useContext(BillingContext);

export const BillingProvider: FC<BillingProviderProps> = ({ children }) => {
  const { paymentMethods, ...paymentsQueryResult } = useLegacyGetPaymentMethods();
  const { billingInformation, ...billingQueryResult } = useLegacyGetBillingInformation();
  const { customer } = useLegacyGetCustomer();
  const { orders } = useLegacyOrdersContext();
  const { entryPointResult } = useEntryPointContext();

  function getPaymentMethods() {
    const updatedPayments = paymentMethods ?? initialBillingState.paymentMethods;
    return {
      paymentMethods: updatedPayments,
      isPaymentsPending: paymentsQueryResult.isPending,
      isPaymentsFetching: paymentsQueryResult.isFetching,
      isPaymentsSuccess: paymentsQueryResult.isSuccess,
      isPaymentsError: paymentsQueryResult.isError,
      paymentsError: paymentsQueryResult.error,
      paymentStatus: paymentsQueryResult.status,
      refetchPayments: paymentsQueryResult.refetch
    };
  }

  function getBillingInformation() {
    const updatedBillingInfo = billingInformation ?? initialBillingState.billingInformation;
    return {
      billingInformation: updatedBillingInfo,
      billingError: billingQueryResult.error,
      isBillingInfoPending: billingQueryResult.isFetching,
      isBillingInfoRefetching: billingQueryResult.isRefetching,
      refetchBillingInfo: billingQueryResult.refetch
    };
  }

  const mostExpensiveOrder = findMostExpensiveEligibleOrder(orders);

  const isFinancingEligible = financingEligible(
    customer,
    entryPointResult.acornFinancingEnabled,
    paymentMethods,
    orders,
    mostExpensiveOrder
  );

  const getValue = () => ({
    ...getPaymentMethods(),
    ...getBillingInformation(),
    isFinancingEligible,
    mostExpensiveOrder
  });

  const value = useMemo(
    () => getValue(),
    [
      paymentMethods,
      paymentsQueryResult.error,
      paymentsQueryResult.status,
      billingInformation,
      billingQueryResult.error,
      billingQueryResult.status
    ]
  );

  return <BillingContext.Provider value={value}>{children}</BillingContext.Provider>;
};
