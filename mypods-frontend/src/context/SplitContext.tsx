import React, { ReactNode, useEffect, useMemo, useState } from 'react';
import SplitIO from '@splitsoftware/splitio/types/splitio';
import { SplitFactoryProvider } from '@splitsoftware/splitio-react';
import { datadogRum } from '@datadog/browser-rum';
import { ENV_VARS } from '../environment';
import { useRefreshSession } from '../networkRequests/queries/useRefreshSession';
import { getVisitorId } from '../config/getVisitorId';
import { useStartPodsReadySession } from '../networkRequests/queries/podsReady/useStartPodsReadySession';
import { useGtmEvents } from '../config/google/useGtmEvents';

export interface ISplitContext {
  splitIoAuthorization: string;
  setSplitConfig: (newConfig: SplitIO.IBrowserSettings) => void;
}

export const initialSplitContext: ISplitContext = {
  splitIoAuthorization: '',
  setSplitConfig: () => {}
};

export const getSplitConfig = (
  splitTargetId: string,
  splitIoAuthorization: string
): SplitIO.IBrowserSettings => ({
  core: {
    authorizationKey: splitIoAuthorization,
    key: splitTargetId,
    trafficType: 'user'
  },
  impressionListener: {
    logImpression(impressionData: SplitIO.ImpressionData) {
      datadogRum.addFeatureFlagEvaluation(
        impressionData.impression.feature,
        impressionData.impression.treatment
      );
    }
  }
});

export const SplitContext = React.createContext<ISplitContext>(initialSplitContext);

export const VisitorIdSplitContextProvider = ({ children }: { children?: ReactNode }) => {
  const splitIoAuthorization = ENV_VARS.SPLIT_CLIENT_KEY;
  const [splitConfig, setSplitConfig] = useState<SplitIO.IBrowserSettings>();

  useEffect(
    function initialize() {
      if (splitIoAuthorization!!) {
        const splitTargetId = getVisitorId();
        setSplitConfig(getSplitConfig(splitTargetId, splitIoAuthorization));
      }
    },
    [splitIoAuthorization]
  );

  const contextValue: ISplitContext = useMemo(
    () => ({ splitIoAuthorization, setSplitConfig }),
    [splitIoAuthorization]
  );

  // -- provider --
  return (
    <SplitContext.Provider value={contextValue}>
      <SplitFactoryProvider config={splitConfig}>{children}</SplitFactoryProvider>
    </SplitContext.Provider>
  );
};

export const PodsReadySplitContextProvider = ({ children }: { children?: ReactNode }) => {
  const splitIoAuthorization = ENV_VARS.SPLIT_CLIENT_KEY;
  const [splitConfig, setSplitConfig] = useState<SplitIO.IBrowserSettings>();
  const { podsReadySessionClaims: sessionClaims } = useStartPodsReadySession();

  useEffect(
    function initialize() {
      if (splitIoAuthorization!!) {
        setSplitConfig(getSplitConfig(sessionClaims.customerId, splitIoAuthorization));
      }
    },
    [splitIoAuthorization]
  );

  const contextValue: ISplitContext = useMemo(
    () => ({ splitIoAuthorization, setSplitConfig }),
    [splitIoAuthorization]
  );

  // -- provider --
  return (
    <SplitContext.Provider value={contextValue}>
      <SplitFactoryProvider config={splitConfig}>{children}</SplitFactoryProvider>
    </SplitContext.Provider>
  );
};

export const SessionSplitContextProvider = ({ children }: { children?: ReactNode }) => {
  const splitIoAuthorization = ENV_VARS.SPLIT_CLIENT_KEY;
  const [splitConfig, setSplitConfig] = useState<SplitIO.IBrowserSettings>();
  const { sessionClaims } = useRefreshSession();
  const { pageLoadEvent } = useGtmEvents();

  useEffect(
    function initialize() {
      if (splitIoAuthorization!!) {
        setSplitConfig(getSplitConfig(sessionClaims.customerId, splitIoAuthorization));
      }
    },
    [splitIoAuthorization]
  );

  useEffect(() => {
    pageLoadEvent();
  }, []);

  const contextValue: ISplitContext = useMemo(
    () => ({ splitIoAuthorization, setSplitConfig }),
    [splitIoAuthorization]
  );

  // -- provider --
  return (
    <SplitContext.Provider value={contextValue}>
      <SplitFactoryProvider config={splitConfig}>{children}</SplitFactoryProvider>
    </SplitContext.Provider>
  );
};
