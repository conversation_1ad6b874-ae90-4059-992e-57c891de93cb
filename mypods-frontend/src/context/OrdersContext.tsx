import React, { createContext, FC, ReactNode, useContext, useMemo, useState } from 'react';
import { Order } from '../domain/OrderEntities';
import { useGetCustomerOrders } from '../networkRequests/queries/useGetCustomerOrders';

export interface OrdersContextProps {
  topOfPageRef: React.RefObject<HTMLDivElement>;
  children: ReactNode;
}

export interface IOrdersContextState {
  orders: Order[];
  refetch: (
    notifyOrdersUpdated?: () => void,
    ordersBeforeTheUpdate?: Map<string, string>,
    tries?: number
  ) => void;
  refetchOnFailure: () => void;
  showLoader: boolean;
}

export const OrdersContext = createContext<IOrdersContextState>({
  orders: [],
  refetch: (notifyContainersUpdated?: (stopScheduling: boolean) => void) => {
    if (notifyContainersUpdated) {
      notifyContainersUpdated(true);
    }
  },
  refetchOnFailure: () => {},
  showLoader: false
});

export default function useOrdersContext() {
  return useContext(OrdersContext);
}

export const OrdersContextProvider: FC<OrdersContextProps> = ({ topOfPageRef, children }) => {
  const {
    error,
    refetch,
    customerOrders: orderQuery,
    refetchUntilTheOrdersUpdate
  } = useGetCustomerOrders();
  const [showLoader, setShowLoader] = useState<boolean>(false);

  const refetchOnFailure = async () => {
    if (topOfPageRef.current) {
      // NOTE: scrolling is buggy when trying with "smooth" behavior
      topOfPageRef.current.scrollIntoView();
    }
    setShowLoader(true);
    await refetch();
    setShowLoader(false);
  };

  function getOrders() {
    const orders = orderQuery ?? [];
    return {
      orders,
      refetch: refetchUntilTheOrdersUpdate,
      refetchOnFailure,
      showLoader
    };
  }

  const value = useMemo(() => getOrders(), [orderQuery, showLoader]);

  if (error) return <pre>{JSON.stringify(error, null, 2)}</pre>;
  return <OrdersContext.Provider value={value}>{children}</OrdersContext.Provider>;
};
