import React, { createContext, FC, useContext, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLegacyGetCustomerOrders } from '../../networkRequests/legacy/queries/useLegacyGetCustomerOrders';
import { OrdersContextProps } from '../OrdersContext';
import { useLegacyShowPodsReadySingleOrder } from '../../helpers/legacy/useLegacyShowPodsReadySingleOrder';
import { PodsReadyRoutes } from '../../PodsReadyRoutes';
import { Order } from '../../domain/OrderEntities';

export interface ILegacyOrdersContextState {
  orders: Order[];
  refetch: () => void;
  refetchOnFailure: () => void;
  showLoader: boolean;
}

export const LegacyOrdersContext = createContext<ILegacyOrdersContextState>({
  orders: [],
  refetch: () => {},
  refetchOnFailure: () => {},
  showLoader: false
});

export default function useLegacyOrdersContext() {
  return useContext(LegacyOrdersContext);
}

export const LegacyOrdersContextProvider: FC<OrdersContextProps> = ({ topOfPageRef, children }) => {
  const { error, refetch, customerOrders: orderQuery } = useLegacyGetCustomerOrders();
  const navigate = useNavigate();
  const { showPodsReadySingleOrder } = useLegacyShowPodsReadySingleOrder();
  const [showLoader, setShowLoader] = useState<boolean>(false);

  const refetchOnFailure = async () => {
    if (topOfPageRef.current) {
      // NOTE: scrolling is buggy when trying with "smooth" behavior
      topOfPageRef.current.scrollIntoView();
    }
    setShowLoader(true);
    await refetch();
    setShowLoader(false);
  };

  function getOrders() {
    const orders = orderQuery ?? [];
    return {
      orders,
      refetch,
      refetchOnFailure,
      showLoader
    };
  }

  const value = useMemo(() => getOrders(), [orderQuery, showLoader]);

  useEffect(() => {
    if (showPodsReadySingleOrder) {
      navigate(PodsReadyRoutes.TASKS, { replace: true });
    }
  }, [showPodsReadySingleOrder]);

  if (error) return <pre>{JSON.stringify(error, null, 2)}</pre>;

  return <LegacyOrdersContext.Provider value={value}>{children}</LegacyOrdersContext.Provider>;
};
