import React, { createContext, FC, ReactNode, useContext, useMemo } from 'react';
import { Container, Order, OrderType } from '../domain/OrderEntities';

interface ContainerContextProps {
  state: Pick<IContainerContextState, 'order' | 'container'>;
  children: ReactNode;
}

export interface IContainerContextState {
  container: Container;
  order: Order;
}

const initialState: IContainerContextState = {
  container: {
    containerId: '',
    containerSize: '',
    moveLegs: []
  },
  order: {
    orderId: '',
    quoteId: 0,
    orderType: OrderType.LOCAL,
    containers: [],
    orderDate: undefined,
    price: 0,
    initialDeliveryPlacementIsReviewed: true
  }
};

export const ContainerContext = createContext<IContainerContextState>(initialState);

export default function useContainerContext() {
  return useContext(ContainerContext);
}

export const ContainerProvider: FC<ContainerContextProps> = ({ state, children }) => {
  const value = useMemo(
    () => ({
      ...state
    }),
    [state]
  );

  return <ContainerContext.Provider value={value}>{children}</ContainerContext.Provider>;
};
