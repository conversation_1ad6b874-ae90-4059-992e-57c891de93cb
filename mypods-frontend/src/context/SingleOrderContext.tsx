import React, {
  createContext,
  FC,
  ReactNode,
  useCallback,
  useContext,
  useMemo,
  useState
} from 'react';
import { MoveLeg, Order, OrderType } from '../domain/OrderEntities';
import { useScheduleMoveLegEvents } from '../config/useScheduleMoveLegEvents';

interface SingleOrderContextProps {
  state: Pick<ISingleOrderContextState, 'order'>;
  children: ReactNode;
}

export type MoveLegScheduling = {
  currentlySelectedMoveLeg: MoveLeg | null;
  editMoveLegScheduling: (_: MoveLeg) => void;
  stopMoveLegScheduling: () => void;
  isSaving: boolean;
  setIsSaving: (_: boolean) => void;
  isCancelling: boolean;
  setIsCancelling: (_: boolean) => void;
};

export interface ISingleOrderContextState {
  order: Order;
  moveLegScheduling: MoveLegScheduling;
}

const initialState: ISingleOrderContextState = {
  order: {
    orderId: '',
    quoteId: 0,
    orderType: OrderType.LOCAL,
    containers: [],
    orderDate: undefined,
    price: 0,
    initialDeliveryPlacementIsReviewed: true
  },
  moveLegScheduling: {
    currentlySelectedMoveLeg: null,
    editMoveLegScheduling: (_: MoveLeg) => {},
    stopMoveLegScheduling: () => {},
    isSaving: false,
    setIsSaving: (_: boolean) => {},
    isCancelling: false,
    setIsCancelling: (_: boolean) => {}
  }
};

export const SingleOrderContext = createContext<ISingleOrderContextState>(initialState);

export default function useSingleOrderContext() {
  return useContext(SingleOrderContext);
}

export const SingleOrderProvider: FC<SingleOrderContextProps> = ({ state, children }) => {
  const [currentlySelectedMoveLeg, setCurrentlySelectedMoveLeg] = useState<MoveLeg | null>(null);
  const { schedulingStart } = useScheduleMoveLegEvents();
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isCancelling, setIsCancelling] = useState<boolean>(false);

  const handleSchedulingMoveLeg = useCallback(
    (moveLeg: MoveLeg) => {
      setCurrentlySelectedMoveLeg(moveLeg);
      setIsCancelling(false);
      schedulingStart();
    },
    [setCurrentlySelectedMoveLeg, setIsCancelling]
  );

  const handleStopScheduling = useCallback(() => {
    setCurrentlySelectedMoveLeg(null);
    setIsCancelling(false);
  }, [setCurrentlySelectedMoveLeg, setIsCancelling]);

  const value = useMemo(
    () => ({
      ...state,
      moveLegScheduling: {
        currentlySelectedMoveLeg,
        editMoveLegScheduling: handleSchedulingMoveLeg,
        stopMoveLegScheduling: handleStopScheduling,
        isSaving,
        setIsSaving,
        isCancelling,
        setIsCancelling
      }
    }),
    [
      state,
      currentlySelectedMoveLeg,
      isSaving,
      setIsSaving,
      isCancelling,
      setIsCancelling,
      handleStopScheduling,
      handleSchedulingMoveLeg
    ]
  );

  return <SingleOrderContext.Provider value={value}>{children}</SingleOrderContext.Provider>;
};
