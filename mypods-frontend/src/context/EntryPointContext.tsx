import React, {
  createContext,
  FC,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState
} from 'react';
import uniq from 'lodash/uniq';
import { useTranslation } from 'react-i18next';
import { EntryPointResult } from '../networkRequests/responseEntities/AuthorizationEntities';
import { useLegacyAuthorizationEntrypoint } from '../networkRequests/legacy/queries/useLegacyAuthorizationEntrypoint';
import { redirectToLoginWithStatus, redirectToLogout } from './ApigeeContext';
import LoadingScreen from '../components/Loading/LoadingScreen';
import { TranslationKeys } from '../locales/TranslationKeys';
import { useLegacyMyPodsService } from '../networkRequests/legacy/LegacyMyPodsService';
import { taskStatus } from '../helpers/entrypointHelpers';

const MAX_MYPODS_RETRY_COUNT = 3;
type EntrypointContextProps = {
  children: ReactNode;
};

export type EntryPointContextState = {
  entryPointResult: EntryPointResult;
  setEntryPointResult: (entryPointResult: EntryPointResult) => void;
  pdfUrls: OnboardingPdfUrl[]; // RA
  removeOutstandingFnpsAgreement: (orderId: string, moveId: string) => void;
  podsReadyTasksStatus: PodsReadyTaskStatus;
};

export const initialEntryPointState: EntryPointContextState = {
  entryPointResult: {
    outstandingRentalAgreements: [],
    outstandingMothAgreements: [],
    outstandingFnpsAgreements: [],
    hasWeightTickets: false,
    maintenanceModeEnabled: false,
    acornFinancingEnabled: false,
    isFetching: true
  },
  setEntryPointResult: () => {},
  pdfUrls: [], // RA
  removeOutstandingFnpsAgreement: () => {},
  podsReadyTasksStatus: {
    totalTasks: 2,
    completedTasks: 0
  }
};

type OnboardingPdfUrl = {
  companyCode: string;
  url: string;
};

export type PodsReadyTaskStatus = {
  totalTasks: number;
  completedTasks: number;
};

export const useEntryPointContext = () => useContext(EntryPointContext);

const EntryPointContext = createContext<EntryPointContextState>(initialEntryPointState);

export const EntryPointProviderWrapper = (props: {
  entryPointResult: EntryPointResult;
  setEntryPointResult: (entryPointResult: EntryPointResult) => void;
  pdfUrls: OnboardingPdfUrl[];
  podsReadyTasksStatus: PodsReadyTaskStatus;
  children: ReactNode;
}) => {
  const { entryPointResult, setEntryPointResult, pdfUrls, podsReadyTasksStatus, children } = props;

  const removeOutstandingFnpsAgreement = useCallback(
    (orderId: string, moveId: string) => {
      setEntryPointResult({
        ...entryPointResult,
        outstandingFnpsAgreements: entryPointResult.outstandingFnpsAgreements.filter(
          (agreement) => !(agreement.orderId === orderId && agreement.moveId === moveId)
        )
      });
    },
    [entryPointResult, setEntryPointResult]
  );

  const getState = () => ({
    entryPointResult,
    setEntryPointResult,
    pdfUrls,
    removeOutstandingFnpsAgreement,
    podsReadyTasksStatus,
    isFetching: false
  });

  const value = useMemo(
    () => getState(),
    [
      entryPointResult,
      pdfUrls,
      setEntryPointResult,
      removeOutstandingFnpsAgreement,
      podsReadyTasksStatus
    ]
  );
  return <EntryPointContext.Provider value={value}>{children}</EntryPointContext.Provider>;
};

export const EntryPointProvider: FC<EntrypointContextProps> = ({ children }) => {
  const { t: translate } = useTranslation();
  const authorizationEntrypoint = useLegacyAuthorizationEntrypoint();
  const { getRentalAgreement } = useLegacyMyPodsService();
  const [entryPointResult, setEntryPointResult] = useState<EntryPointResult | null>(null);
  const [pdfUrls, setPdfUrls] = useState<OnboardingPdfUrl[] | null>(null); // RA
  const [podsReadyTasksStatus, setPodsReadyTasksStatus] = useState<PodsReadyTaskStatus>({
    totalTasks: 0,
    completedTasks: 0
  });

  useEffect(() => {
    if (authorizationEntrypoint.isSuccess) {
      if (authorizationEntrypoint.data?.maintenanceModeEnabled) {
        redirectToLogout();
      }
      const entryPointData = authorizationEntrypoint.data;
      setupPdfUrls(entryPointData, () => {
        entryPointData.isFetching = false;
        setEntryPointResult(entryPointData);
      });

      setPodsReadyTasksStatus(taskStatus(entryPointData));
    }
    if (authorizationEntrypoint.failureCount >= MAX_MYPODS_RETRY_COUNT) {
      redirectToLoginWithStatus('AUTH_ERROR');
    }
  }, [authorizationEntrypoint.isLoading, authorizationEntrypoint.failureCount]);

  const setupPdfUrls = (data: EntryPointResult, onSuccess: () => void) => {
    const companyCodes: string[] = uniq(
      data.outstandingRentalAgreements.map((ra) => ra.companyCode)
    );
    const promises: Promise<OnboardingPdfUrl>[] = companyCodes.map((companyCode) =>
      getRentalAgreement(companyCode).then((url) => ({ companyCode, url }))
    );
    Promise.all(promises).then((urls) => {
      setPdfUrls(urls);
      onSuccess();
    });
  };

  if (entryPointResult == null || pdfUrls == null) {
    return <LoadingScreen loadingText={translate(TranslationKeys.LoadingScreens.INITIAL)} />;
  }
  return (
    <EntryPointProviderWrapper
      {...{ entryPointResult, pdfUrls, setEntryPointResult, podsReadyTasksStatus }}>
      {children}
    </EntryPointProviderWrapper>
  );
};
