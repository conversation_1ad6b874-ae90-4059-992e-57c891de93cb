import { vi } from 'vitest';
import { isOAuthTokenExpired, OAuthToken } from '../ApigeeContext';
import { subDays } from 'date-fns';

const mockCreate = vi.hoisted(() => vi.fn());

vi.mock('axios', () => ({
  default: {
    create: mockCreate
  }
}));

describe('ApigeeContext', () => {
  describe('IsOauthTokenExpired', () => {
    const accessToken = 'tokenHere';
    const validOAuthToken: OAuthToken = {
      access_token: accessToken,
      expires_in: 600,
      issued_at: new Date().getTime().toString()
    };
    const invalidOAuthToken: OAuthToken = {
      ...validOAuthToken,
      expires_in: 0,
      issued_at: subDays(new Date(), 1).getTime().toString()
    };

    it('should not be expired if in the future or now', () => {
      expect(isOAuthTokenExpired(validOAuthToken)).toEqual(false);
    });

    it('should be expired if in the past', () => {
      expect(isOAuthTokenExpired(invalidOAuthToken)).toEqual(true);
    });

    it('should be expired if null', () => {
      expect(isOAuthTokenExpired(null)).toEqual(true);
    });
  });
});
