import React, { createContext, ReactNode, useMemo } from 'react';
import axios, { AxiosInstance, InternalAxiosRequestConfig } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { addSeconds } from 'date-fns';
import { ENV_VARS } from '../environment';
import { getVisitorId } from '../config/getVisitorId';
import {
  AuthorizationErrorStatus,
  CustomerAccountsLoginStatus
} from '../networkRequests/responseEntities/ErrorEntities';

export const AUTH_TOKEN_KEY = 'authToken';
const CORRELATION_ID = 'Correlation-ID';

export type OAuthToken = {
  issued_at: string;
  access_token: string;
  expires_in: number;
};

export const isOAuthTokenExpired = (token: OAuthToken | null): boolean => {
  if (!token) return true;
  const millis = parseInt(token.issued_at);
  return addSeconds(new Date(millis), token.expires_in) < new Date();
};

export const getToken = (): OAuthToken | null => {
  const storedToken = localStorage.getItem(AUTH_TOKEN_KEY);
  return storedToken ? JSON.parse(storedToken) : null;
};

export const setToken = (token: OAuthToken) =>
  localStorage.setItem(AUTH_TOKEN_KEY, JSON.stringify(token));

export const removeToken = () => localStorage.removeItem(AUTH_TOKEN_KEY);

const getOAuthToken = async (): Promise<OAuthToken> => {
  try {
    const res = await axios.post<OAuthToken>(
      ENV_VARS.APIGEE_OAUTH_URL,
      {
        client_id: ENV_VARS.APIGEE_CLIENT_ID,
        client_secret: ENV_VARS.APIGEE_CLIENT_SECRET
      },
      {
        headers: {
          'content-type': 'application/x-www-form-urlencoded',
          Authorization: undefined
        },
        withCredentials: false
      }
    );

    return res.data;
  } catch (e) {
    removeToken();
    throw e;
  }
};

export const redirectToLoginWithEmail = (email: string) => {
  const encodedEmail = encodeURIComponent(email);
  window.location.replace(`${ENV_VARS.MYPODS_LOGIN}?username=${encodedEmail}`);
};

export const redirectToLoginWithStatus = (status: CustomerAccountsLoginStatus) => {
  const redirectUrl = window.location.href;
  window.location.replace(`${ENV_VARS.MYPODS_LOGIN}?status=${status}&redirect=${redirectUrl}`);
};

export const redirectToLogout = () => {
  window.location.replace(ENV_VARS.LOGOUT);
};

export const ApigeeContext = createContext<AxiosInstance>(axios.create());
export const ApigeeProvider = ({ children }: { children: ReactNode }) => {
  async function configureApigeeOathHeader(config: InternalAxiosRequestConfig<any>) {
    let token = getToken();
    if (isOAuthTokenExpired(token)) {
      try {
        token = await getOAuthToken();
        setToken(token);
      } catch (error) {
        return Promise.reject(error);
      }
    }
    // eslint-disable-next-line no-param-reassign
    config.headers.Authorization = `Bearer ${token?.access_token}`;
    return config;
  }

  const axiosInstance = useMemo(() => {
    const instance = axios.create({
      baseURL: ENV_VARS.APIGEE_API_URL,
      headers: {
        'Visitor-ID': getVisitorId()
      },
      withCredentials: true
    });
    instance.interceptors?.request.use(
      async (config) => {
        // eslint-disable-next-line no-param-reassign
        config.headers[CORRELATION_ID] = uuidv4();
        return configureApigeeOathHeader(config);
      },
      (error) => Promise.reject(error)
    );
    instance.interceptors?.response.use(
      async (response) => response,
      async (error) => {
        const errorStatus = error.response?.data?.status as AuthorizationErrorStatus;
        if (errorStatus === 'UNAUTHORIZED') {
          redirectToLoginWithStatus('SESSION_EXPIRED');
          return;
        }
        if (errorStatus === 'FORBIDDEN') {
          redirectToLoginWithStatus('AUTH_ERROR');
          return;
        }
        if (errorStatus === 'MULTI_CUID') {
          redirectToLoginWithStatus('SESSION_EXPIRED');
          return;
        }
        if (errorStatus === 'UNEXPECTED_AUTH_FAILURE') {
          redirectToLoginWithStatus('AUTH_ERROR');
          return;
        }
        if (errorStatus === 'MAINTENANCE') {
          redirectToLoginWithStatus('MAINTENANCE');
          return;
        }

        return Promise.reject(error);
      }
    );
    return instance;
  }, []);

  return <ApigeeContext.Provider value={axiosInstance}>{children}</ApigeeContext.Provider>;
};
