import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import viteTsconfigPaths from 'vite-tsconfig-paths';
import vitePluginSvgr from 'vite-plugin-svgr';

export default defineConfig({
  define: process.env.VITEST ? {} : { global: 'window' },
  plugins: [react(), viteTsconfigPaths(), vitePluginSvgr()],
  server: {
    host: '0.0.0.0',
    port: 3001,
    strictPort: true,
    open: true
  },
  build: {
    target: 'es6',
    outDir: './build',
    sourcemap: true
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./setupTests.ts', './src/testUtils/mock-react-i18next.jsx'],
    css: false,
    mockReset: true,
    env: {
      NODE_ENV: 'test'
    }
  }
});
