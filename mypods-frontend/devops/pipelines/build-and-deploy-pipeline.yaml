pool: Azure Pipelines
trigger:
  paths:
    include:
      - mypods-frontend/*
  branches:
    include:
      - main

parameters:
  - name: skipTests
    type: boolean
    default: false
    displayName: 'Skip Tests'

variables:
  isMain: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  serviceConnectionNP: SC-PODS-NP
  serviceConnectionPD: SC-PODS-PD
  subscriptionNP: 805be479-ee7f-4c01-aed8-8267c4ff0664
  subscriptionPD: 2a71f9dc-3c1b-48dd-bb6b-c3b9e9456fc1
  projectFolderPath: mypods-frontend
  devopsFolderPath: /mypods-frontend/devops
  serviceName: mypods-frontend
  chartName: mypods-frontend
  skipTests: ${{ parameters.skipTests }}

stages:
  - stage: Build_Test_Push_Docker_Image
    displayName: 'Build and Test React Project, Push Docker Image'
    jobs:
      - job: ReactTests
        condition: and(succeeded(), eq(variables.skipTests, false))
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '18.12.1'
          - script: |
              rm -rf node_modules
            workingDirectory: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/'
            displayName: 'Remove node_modules'
          - task: NpmAuthenticate@0
            displayName: 'Authenticate with Azure Artifacts'
            inputs:
              workingFile: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/.npmrc'
          - script: |
              npm install
            workingDirectory: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/'
            displayName: 'Run npm install'
          - script: |
              npm run test
            workingDirectory: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/'
            displayName: 'Run npm test'
          - task: AzureKeyVault@2
            inputs:
              azureSubscription: $(serviceConnectionNP)
              KeyVaultName: KV-EUS-SERVICES-NP-01
              SecretsFilter: 'datadog-js-src-map-upload-api-key'
              RunAsPreJob: true
          - script: |
              VITE_APP_GIT_SHA=$(Build.SourceVersion) npm run build
            workingDirectory: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/'
            displayName: 'Run npm run build'
          - script: |
              DATADOG_API_KEY=$(datadog-js-src-map-upload-api-key) bash $(System.DefaultWorkingDirectory)/$(projectFolderPath)/devops/scripts/datadog_push.sh
      - job: Docker_Image
        displayName: 'Build and Push Docker Image'
        dependsOn:
          - ReactTests
        condition: or(succeeded(), eq(variables.skipTests, true))
        steps:
          - checkout: self
          - script:
              echo 'Building image $(Build.SourceVersion)'
          - task: NpmAuthenticate@0
            displayName: 'Authenticate with Azure Artifacts'
            inputs:
              workingFile: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/.npmrc'
          - task: Docker@2
            inputs:
              buildContext: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/'
              command: 'build'
              Dockerfile: '$(System.DefaultWorkingDirectory)/$(devopsFolderPath)/Dockerfile'
              containerRegistry: podscreusdv
              repository: $(serviceName)
              tags: |
                $(Build.SourceVersion)
              arguments: '--build-arg VITE_APP_GIT_SHA=$(Build.SourceVersion)'
          - task: Docker@2
            inputs:
              command: 'push'
              containerRegistry: podscreusdv
              repository: $(serviceName)
              tags: |
                $(Build.SourceVersion)

  - stage: Approval
    displayName: 'Manual Approval'
    dependsOn: Build_Test_Push_Docker_Image
    jobs:
      - job: Approval
        displayName: 'Approval Job'
        pool: server
        steps:
          - task: ManualValidation@0
            condition: and(succeeded(), eq(variables.isMain, 'false'))
            timeoutInMinutes: 1440 # task times out in 1 day
            inputs:
              instructions: 'Do you really want to deploy $(Build.SourceBranch) to Test?'

  - stage: BAUApproval
    displayName: 'Manual BAUApproval'
    dependsOn: Build_Test_Push_Docker_Image
    jobs:
      - job: Approval
        displayName: 'Approval Job'
        pool: server
        steps:
          - task: ManualValidation@0
            condition: and(succeeded(), eq(variables.isMain, 'false'))
            timeoutInMinutes: 1440 # task times out in 1 day
            inputs:
              instructions: 'Do you really want to deploy $(Build.SourceBranch) to BAUTest?'

  - stage: Int_Approval
    displayName: 'Manual Approval for Int'
    dependsOn: Build_Test_Push_Docker_Image
    jobs:
      - job: Approval
        displayName: 'Approval To Int'
        pool: server
        steps:
          - task: ManualValidation@0
            timeoutInMinutes: 1440 # 1-day timeout
            inputs:
              instructions: 'Do you want to deploy to Int(Dev)?'

  - stage: Deploy_Dev
    displayName: 'Deploy to Int'
    dependsOn: Int_Approval
    jobs:
      - template: ../../../devops/pipelines/deploy-templates/deploy-dev.yaml
        parameters:
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

  - stage: Deploy_Test
    displayName: 'Deploy to Test'
    dependsOn: Approval
    jobs:
      - template: ../../../devops/pipelines/deploy-templates/create-new-image.yaml
        parameters:
          serviceName: $(serviceName)
          serviceConnection: $(serviceConnectionNP)
          targetAzureSubscriptionId: $(subscriptionNP)
          previousAzureSubscriptionId: $(subscriptionNP)
          previousImageResourceGroup: RG-INFRA-DV
          previousContainerRegistry: podscreusdv
          targetContainerRegistry: podscreuste

      - template: ../../../devops/pipelines/deploy-templates/deploy-test.yaml
        parameters:
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

  - stage: Deploy_BAUTest
    displayName: 'Deploy to BAU Test'
    dependsOn: BAUApproval
    jobs:
      - template: ../../../devops/pipelines/deploy-templates/create-new-image.yaml
        parameters:
          serviceName: $(serviceName)
          serviceConnection: $(serviceConnectionNP)
          targetAzureSubscriptionId: $(subscriptionNP)
          previousAzureSubscriptionId: $(subscriptionNP)
          previousImageResourceGroup: RG-INFRA-DV
          previousContainerRegistry: podscreusdv
          targetContainerRegistry: podscreuste

      - template: ../../../devops/pipelines/deploy-templates/deploy-bau-test.yaml
        parameters:
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

  - stage: Deploy_Stage
    displayName: 'Deploy to Stage EUS and WUS'
    dependsOn: Deploy_Test
    jobs:
      - template: ../../../devops/pipelines/deploy-templates/create-new-image.yaml
        parameters:
          serviceName: $(serviceName)
          serviceConnection: $(serviceConnectionNP)
          targetAzureSubscriptionId: $(subscriptionNP)
          previousAzureSubscriptionId: $(subscriptionNP)
          previousImageResourceGroup: RG-INFRA-TE
          previousContainerRegistry: podscreuste
          targetContainerRegistry: podscreusst

      - template: ../../../devops/pipelines/deploy-templates/deploy-stage-eus.yaml
        parameters:
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

      - template: ../../../devops/pipelines/deploy-templates/deploy-stage-wus.yaml
        parameters:
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

  - stage: Deploy_Production_EUS
    displayName: 'Deploy to Production EUS'
    dependsOn:
      - Deploy_Stage
      - Deploy_BAUTest
    condition: or(succeeded('Deploy_Stage'), succeeded('Deploy_BAUTest'))
    jobs:
      - template: ../../../devops/pipelines/deploy-templates/create-new-image.yaml
        parameters:
          serviceName: $(serviceName)
          serviceConnection: SC-PODS-PD
          targetAzureSubscriptionId: 2a71f9dc-3c1b-48dd-bb6b-c3b9e9456fc1
          previousAzureSubscriptionId: 805be479-ee7f-4c01-aed8-8267c4ff0664
          previousImageResourceGroup: RG-INFRA-ST
          previousContainerRegistry: podscreusst
          targetContainerRegistry: podscreuspd
      - template: ../../../devops/pipelines/deploy-templates/deploy-prod-eus.yaml
        parameters:
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)

  - stage: Deploy_Production_WUS
    displayName: 'Deploy to Production WUS'
    dependsOn: Deploy_Production_EUS
    jobs:
      - template: ../../../devops/pipelines/deploy-templates/deploy-prod-wus.yaml
        parameters:
          chartName: $(chartName)
          devopsFolderPath: $(devopsFolderPath)
