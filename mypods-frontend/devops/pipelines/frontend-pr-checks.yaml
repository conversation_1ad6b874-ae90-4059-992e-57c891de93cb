pr:
  branches:
    include:
      - main
  paths:
    include:
      - mypods-frontend/*
pool: Azure Pipelines

variables:
  projectFolderPath: mypods-frontend
  devopsFolderPath: mypods-frontend/devops

stages:
  - stage: Build_And_Test
    displayName: 'Build and Test React Project'
    jobs:
      - job: ReactTests
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '18.12.1'
          - task: NpmAuthenticate@0
            displayName: 'Authenticate with Azure Artifacts'
            inputs:
              workingFile: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/.npmrc'
          - script: |
              npm install
            workingDirectory: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/'
            displayName: 'Run npm install'
          - script: |
              npm run test
            workingDirectory: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/'
            displayName: 'Run npm test'
          - script: |
              VITE_APP_GIT_SHA=$(Build.SourceVersion) npm run build
            workingDirectory: '$(System.DefaultWorkingDirectory)/$(projectFolderPath)/'
            displayName: 'Run npm run build'