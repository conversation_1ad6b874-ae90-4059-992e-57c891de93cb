FROM node:18-buster-slim as build

WORKDIR /app

COPY --chown=node package.json ./
COPY --chown=node package-lock.json ./
# Copy .npmrc for npm authentication
COPY --chown=node .npmrc ./

RUN npm install

COPY --chown=node ./ ./

ARG VITE_APP_GIT_SHA=''
ENV VITE_APP_GIT_SHA ${VITE_APP_GIT_SHA}
RUN npm run build

RUN find ./build/assets -name "*.map" -type f -delete

# Use Nginx as the production server
FROM nginx:alpine

COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /app/build /usr/share/nginx/html

RUN touch /var/run/nginx.pid
RUN chown -R nginx:nginx /var/run/nginx.pid /usr/share/nginx/html /var/cache/nginx /var/log/nginx /etc/nginx/conf.d
USER nginx

EXPOSE 3001

CMD ["nginx", "-g", "daemon off;"]
