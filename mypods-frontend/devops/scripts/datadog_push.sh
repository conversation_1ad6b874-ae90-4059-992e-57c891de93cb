echo build version "$BUILD_SOURCEVERSION"
echo source map path "$SYSTEM_DEFAULTWORKINGDIRECTORY"/"$PROJECTFOLDERPATH"/build/assets

export DATADOG_SITE=us3.datadoghq.com
export VITE_APP_GIT_SHA="$BUILD_SOURCEVERSION"

npx @datadog/datadog-ci sourcemaps upload \
    "$SYSTEM_DEFAULTWORKINGDIRECTORY"/"$PROJECTFOLDERPATH"/build/assets \
    --service="my.pods.com" \
    --release-version="$BUILD_SOURCEVERSION" \
    --minified-path-prefix=/assets
