# Default values for mypods-frontend.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: podscreusdv.azurecr.io/mypods-frontend
  pullPolicy: Always

ingress:
  agic:
    frontDoorAppendedRoute: /mypods/*

imagePullSecrets: [ ]
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext:
  runAsNonRoot: true
  runAsUser: 101
# fsGroup: 2000

securityContext:
  runAsNonRoot: true
  runAsUser: 101
# capabilities:
#   drop:
#   - ALL
# readOnlyRootFilesystem: true

service:
  type: ClusterIP
  port: 80

resources: { }
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as <PERSON>ku<PERSON>. If you do want to specify resources, uncomment the following
# lines, adjust them as necessary, and remove the curly braces after 'resources:'.
# limits:
#   cpu: 100m
#   memory: 128Mi
# requests:
#   cpu: 100m
#   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector:
  "kubernetes.io/os": linux

initialDelaySeconds: 30

tolerations: [ ]

affinity: { }
