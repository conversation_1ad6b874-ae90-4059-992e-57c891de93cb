apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mypods-ingress-frontend
  annotations:
    {{- with .Values.ingress.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.ingress.waf }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    appgw.ingress.kubernetes.io/backend-path-prefix: "/"
spec:
  # "ingressClassName" must match the "ingressClassResource.name" name provided when deploying the AGIC
  ingressClassName: azure-application-gateway-webfe-infra
  rules:
    - http:
        paths:
          - path: {{ .Values.ingress.agic.frontDoorAppendedRoute }}
            pathType: Prefix
            backend:
              service:
                name: mypods-frontend
                port:
                  number: 3001