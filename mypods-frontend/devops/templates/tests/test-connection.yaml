apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "mypods-frontend.fullname" . }}-test-connection"
  labels:
    {{- include "mypods-frontend.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "mypods-frontend.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
