import type { Preview } from '@storybook/react';
import i18n from './i18next';
import '../src/App.css';

import { ThemeProvider, CssBaseline } from '@mui/material';
import { withThemeFromJSXProvider } from '@storybook/addon-themes';

/* TODO: update import for your custom Material UI themes */
import { theme } from '../src/PodsTheme';

const preview: Preview = {
  initialGlobals: {
    locale: 'en_us',
    locales: {
      en_us: 'English (US)',
      fr_ca: 'Français (CA)'
    }
  },
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i
      }
    },
    i18n
  },
  decorators: [
    withThemeFromJSXProvider({
      GlobalStyles: CssBaseline,
      Provider: ThemeProvider,
      themes: {
        base: theme
      },
      defaultTheme: 'base'
    })
  ]
};

export default preview;
