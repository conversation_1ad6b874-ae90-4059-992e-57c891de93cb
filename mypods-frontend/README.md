# MyPODS 2.0 FrontEnd.

- [Getting Started](#getting-started)
- [Pods Component Library Setup](#pods-component-library-setup)
- [Linting & Formatting](#linting--formatting)
- [Organization](#organization)

* [Testing](#testing)
* [Libraries to Know](#libraries-to-know)

- [Translation Scripts](#translation-scripts)
- [Generating Design Constants](#generating-design-constants)
- [Adding Images](#adding-images)

## Getting Started

- If you do not have nvm installed on your system, follow the instructions [here](https://github.com/nvm-sh/nvm) for your operating system.
- Once installed, our project has an `.nvmrc` file in this directory, so to install and use the correct version of npm, simply run:
  ```bash
  nvm install
  nvm use
  ```
- To install the project's dependencies, run `npm install`
- To start the server, run `npm run start`

## Pods Component Library Setup

- run `npm install`
- if you see a `E401` error, try running `better-vsts-npm-auth`
- if better-vsts is not installed, run `npm i -g better-vsts-npm-auth`
- if it is installed, run `better-vsts-npm-auth` once
- if the console gives you a command or link click, open it and follow the instructions
- run `better-vsts-npm-auth` again
- run `npm install` again

## Linting & Formatting

- The project has configuration files for both eslint & prettier for linting and formatting, respectively. Please ensure that your editor is configured to use the `.eslintrc` and `.prettierrc` configurations by default. If you're unsure how to configure them, ask a teammate. Alternatively, you can run the `npm run validate` command to run all formatters and tests before committing.

## Organization

- The visual elements of the site are mainly located in two directories:
  - Components (for shared/reuseable elements)
  - Pages, which should each correspond to an individual page, along with any sub-components that are specific to it
- NetworkRequests contains data requests to the mypods-api
- Context currently contains state relating to the customer's information

## Testing

- Tests are located in the same directory as the component under test, within a folder titled `__tests__`
- We use vitest & react-testing-library for testing, along with some custom rendering helpers that are stored within the `/testUtils` directory
- The types of testing is currently a point of discussion, so feel free to engage!

## Styling

- For now, this project uses the [Mui Library](https://mui.com/material-ui/getting-started/), though a team is working to make a custom design library.
- Our pattern is to style as many common components using themes (see the PodsTheme.tsx file), and using the named variables from our Design Tokens file (see Design.ts)
- Custom components avoid inline-styles, with the exception of Mui Props; our approach is to extract `style` & `sx` props into an object with an `sx` key, that can be spread into the element's JSX
- Our designs live in [Figma](<https://www.figma.com/file/AOa7K3O0Qc6RovU2zXh6G0/Vision-Post-Booking-(mypods2.0)?type=design&node-id=1477-72827&mode=design&t=zp7KjhdVOy62NXqQ-0>)
- If you toggle the Figma view to show layout grids, you can see that the site is organized by a 12 column grid, and our designs should use Mui's Grid components to keep our components proportional, as represented by the designs.

## Libraries to Know:

- [React Router](https://reactrouter.com/en/main/start/concepts): Handles the application's routing, and provides useful components for navigation
- [React Query / TanStack](https://tanstack.com/query/latest): Library that can manage request caching & more
- [Datadog's Real User Monitoring (RUM)](https://docs.datadoghq.com/real_user_monitoring/) Allows for frontend logs
- [SplitIO](https://docs.split.io/docs/split-documentation-links): Used for A/B Testing experiments

## Translation Scripts

### Creating the Translation Keys within the project

When adding new strings to the app, you can just add them to the `src/locales/en_us.json` file,
then run `npm run i18n:build` within the `mypods-frontend` directory. This and it will generate a new TranslationKeys.ts file for you.

Things to note:

- The overall pattern is that keys that have further nesting are PascalCase; if a key contains the string value, it will be CONSTANT_CASE. There was some cleanup in the frontend to use the correctly cased variables.
- Using i18n library's
  [contexts](https://www.i18next.com/translation-function/context) and [counts](https://www.i18next.com/translation-function/plurals) features (rather than functions) keep the strings parsable by script.
- When adding new context-based strings to the project, there's a constant at the top of the i18n/build.cjs script that those values should be added to in order for them to be parsed correctly.

### Generating a Report of Terms needing French Translation

To generate a .csv of the English language terms needing French translation, run `npm run i18n:report` - the path to the file will be part of the scripts command line output.

### Updating the French Translations

Given a csv file with a format like the one generated by the `report` command, we can somewhat-automatically update our French translations, by calling `npm run i18n:update path/to/translations.csv`.

The catch is that if the translators translate any of the words in {{brackets}}, we have to change them back to the canonical English variable that is actually used in the code.

## Generating Design Constants

Occasionally, the design team will update the Design Tokens that the system is modeled on, and will share a JSON file with the updated values & structure. In order to keep things consistent across PODS Digital projects, we're consuming these tokens from the [shared-components-library](https://pods-llc.visualstudio.com/Digital%20Strategy%20and%20Innovation/_git/shared-react-components). The script that generates our Design variables is located in the `/design` directory in the root of the project. It uses the [style-dictionary](https://www.npmjs.com/package/style-dictionary) package to generate a standard output.

### Usage

** CAUTION: THE NEXT STEP IS DESTRUCTIVE, IT WILL _OVERWRITE_ OUR EXISTING TOKENS **

Running `npm run design:build` in the frontend directory will generate a list of new tokens. If you'd like a preview, just edit the `design/config.cjs` file to output to a different location.

### Overrides

There are some values where our project wants to create a variable for a frequently used property that isn't accounted for by the design system. In the `./design/inputs` directory, there is an `overwrites.json` file that can amend or replace design system values in our Design.ts variables. Any additions to this file must match the format and structure of the original `tokens.json` file ( the path is referenced in `config.cjs`). Ideally, we share this list of overrides with our designers to ensure that reused values are adopted by the system -- and override minimally.

## Adding Images

For assets larger than an icon, we've been adding them to the prod & non-prod blob storage accounts.
To access the storage account, log into azure, and navigate to / search for `Storage Accounts` and filter the list by `mypods`. There should be two accounts, one for each environment. Select the account for the environment you want to add the image to, and then click on `Data Storage` -> `Containers` -> `mypods`. In most cases, we'll want parity between the two accounts.

We've been working over time to get the images directory organized, though you'll notice a few loose files for the time being, trying to keep images for different feature-related components together.

There's an environment variable for each of the storage accounts paths, so you should be able to use the `ENV_VARS.ASSETS_BASE_URL` variable to access the image in the code, like so:

```jsx
<img
  src={`${ENV_VARS.ASSETS_BASE_URL}/nice-image.jpg`}
  alt="a really nice image"
  {...styles.image}
/>
```
