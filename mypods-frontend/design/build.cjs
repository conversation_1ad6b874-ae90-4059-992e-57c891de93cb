const StyleDictionary = require("style-dictionary");

// -- main --
function Main() {
  // extremely unclear from the docs, but loading the config before registering parsers will blow away any parsers defined here
  StyleDictionary.extend("design/config.cjs").buildAllPlatforms();
}

// -- parsers --
// parse w3c design token json (replace $value & $description)
StyleDictionary.registerParser({
  pattern: /\.json$/,
  parse: ({ contents }) => {
    let parsed = contents ?? "{}";
    parsed = parsed.replace(/"\$value"\s*:/g, '"value":');
    parsed = parsed.replace(/"\$description"\s*:/g, '"comment":');
    return JSON.parse(parsed);
  },
});

// -- formats --
// output a nested typescript module
StyleDictionary.registerFormat({
  name: "typescript/custom",
  formatter: ({ dictionary, _platform, _options, _file }) => {
    function format(node) {
      if ("value" in node) {
        return node.value;
      }

      const result = {};
      for (const key in node) {
        const child = node[key];

        // for now, discard any keys for now prefixed w/ '$'
        if (key[0] === "$") {
          continue;
        }

        // format any child objects
        let value = child;
        if (value instanceof Object) {
          value = format(child);
        }

        // pascalize keys used for structural nesting
        let name = key;
        if (value instanceof Object) {
          name = key.slice(0, 1).toUpperCase() + key.slice(1);
        }

        result[name] = value;
      }

      return result;
    }

    const serialized = `
      // generated by \`npm run design:build\`; do not edit directly
      // add desired design changes to design/inputs/overrides.json
      export const Design = \n${JSON.stringify(format(dictionary.tokens), null, 2)};
    `;

    return serialized;
  },
});

// -- bootstrap --
Main();
