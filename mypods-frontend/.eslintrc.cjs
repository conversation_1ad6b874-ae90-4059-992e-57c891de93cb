module.exports = {
    env: {
        browser: true,
        es6: true,
        jest: true,
    },
    extends: [
        "plugin:react/recommended",
        "airbnb",
        "airbnb-typescript",
        "airbnb/hooks",
        "plugin:prettier/recommended",
    ],
    parserOptions: {
        ecmaFeatures: {
            jsx: true,
        },
        ecmaVersion: "latest",
        sourceType: "module",
        project: "./tsconfig.json",
        tsconfigRootDir: __dirname,
    },
    plugins: ["@typescript-eslint", "react", "react-hooks", "react-refresh", "jsx-a11y", "jest", "unused-imports", "lodash"],
    settings: {
        react: {
            version: "detect",
        },
    },
    ignorePatterns: ["node_modules/", "*.js", "*__tests__*"],
    overrides: [
        {
            files: ["vite.config.ts", "vite-env.d.ts", "panda.config.ts"],
            parserOptions: {
                project: "./tsconfig.node.json"
            }
        }
    ],
    rules: {
        "arrow-body-style": [2, "as-needed"],
        "class-methods-use-this": 0,
        "react/function-component-definition": [
            2,
            {
                namedComponents: "arrow-function",
                unnamedComponents: "arrow-function",
            },
        ],
        "import/extensions": [
            2,
            "ignorePackages",
            {
                js: "never",
                jsx: "never",
                ts: "never",
                tsx: "never",
            },
        ],
        "unused-imports/no-unused-imports": "warn",
        "import/imports-first": 0,
        "import/newline-after-import": 0,
        "import/no-dynamic-require": 0,
        "import/no-extraneous-dependencies": [
            "error",
            { devDependencies: true, optionalDependencies: false, peerDependencies: false },
        ],
        "import/no-named-as-default": 0,
        "import/no-unresolved": "off",
        "import/no-webpack-loader-syntax": 0,
        "import/prefer-default-export": 0,
        "jsx-a11y/anchor-is-valid": 0,
        "max-len": 0,
        "newline-per-chained-call": 0,
        "no-confusing-arrow": 0,
        "no-console": "off",
        "no-continue": "off",
        "no-plusplus": [2, { allowForLoopAfterthoughts: true }],
        "no-use-before-define": 0,
        "no-warning-comments": [2, { terms: ["AAA"] }],
        "prefer-template": 2,
        "radix": [2, "as-needed"],
        "react/destructuring-assignment": 0,
        "react/require-default-props": 0,
        "react/require-extension": "off",
        "react/self-closing-comp": 0,
        "react/sort-comp": 0,
        "react/forbid-prop-types": 0,
        "react/jsx-closing-tag-location": 0,
        "react/jsx-first-prop-new-line": [2, "multiline"],
        "react/jsx-filename-extension": 0,
        "react/jsx-props-no-spreading": 0,
        "react/jsx-no-target-blank": 0,
        "react/jsx-uses-vars": 2,
        "react/jsx-no-constructed-context-values": "warn",
        "react/no-array-index-key": 0,
        "react/no-unstable-nested-components": [2, { allowAsProps: true }],
        "react/jsx-no-duplicate-props": [1, { ignoreCase: false }],
        "jsx-a11y/aria-props": 2,
        "jsx-a11y/heading-has-content": 0,
        "jsx-a11y/label-has-associated-control": [
            2,
            {
                // NOTE: If this error triggers, either disable it or add
                // your custom components, labels and attributes via these options
                // See https://github.com/evcohen/eslint-plugin-jsx-a11y/blob/master/docs/rules/label-has-associated-control.md
                controlComponents: ["Input"],
            },
        ],
        "jsx-a11y/label-has-for": 0,
        "jsx-a11y/mouse-events-have-key-events": 2,
        "jsx-a11y/role-has-required-aria-props": 2,
        "jsx-a11y/role-supports-aria-props": 2,
        "react-hooks/rules-of-hooks": 2,
        "react-hooks/exhaustive-deps": 0,
        "@typescript-eslint/no-redeclare": 0,
        "default-case": 0,
        "@typescript-eslint/switch-exhaustiveness-check": "error",
        "consistent-return": 0,
        "@typescript-eslint/no-use-before-define": 0,
        "no-unused-vars": 0,
        "unused-imports/no-unused-vars": 0,
        "@typescript-eslint/no-unused-vars": ["error", { argsIgnorePattern: "^_" }],
        "lodash/import-scope": [2, "method"],
    },
};
