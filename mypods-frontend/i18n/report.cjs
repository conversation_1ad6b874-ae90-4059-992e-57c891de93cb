const enUSJson = require("../src/locales/en_us.json");
const frCANJson = require("../src/locales/fr_ca.json");
const fs = require("fs");
const _ = require("lodash");
const { createArrayCsvWriter } = require("csv-writer");

function findAllKeys(json, parentKey = "") {
  const keys = [];
  for (const key in json) {
    const currentKey = parentKey ? `${parentKey}.${key}` : key;
    if (typeof json[key] === "object") {
      const nestedKeys = findAllKeys(json[key], currentKey);
      keys.push(...nestedKeys);
    } else {
      keys.push(currentKey);
    }
  }
  return keys;
}

function formatValueToCsv(value) {
  return value === undefined ? "" : `"${value.replaceAll("\"", "\"\"")}"`;
}

// Find all keys in both JSON files
const allEnUSKeys = findAllKeys(enUSJson);
const allFrCANKeys = findAllKeys(frCANJson);

// Combine all keys and remove duplicates
const allKeys = Array.from(new Set([...allEnUSKeys, ...allFrCANKeys]));

// Sort keys alphabetically
allKeys.sort();

// Convert keys to CSV format
let missingCsvWriter = createArrayCsvWriter({
  path: "missing_translations.csv",
  header: ["key", "english", "french"]
});
const missingKeys = allKeys.map((key) => {
  const enValue = _.get(enUSJson, key);
  const frValue = _.get(frCANJson, key);
  if (enValue === undefined || frValue === undefined) {
    if (enValue === undefined) {
      console.warn(`Missing English value for key ${key}`);
    }
    return [key, enValue, frValue];
  }
}).filter((row) => row !== undefined);

// Output CSV
missingCsvWriter.writeRecords(missingKeys).then(() => {
  console.log("CSV file with all keys has been generated in missing_translations.csv.");
});
