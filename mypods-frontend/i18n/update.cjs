const fs = require("fs");
const path = require("path");
const { parse } = require("csv-parse/sync");
const frenchTranslations = require("../src/locales/fr_ca.json");
const _ = require("lodash");

const KEY_COLUMN = "key";
const FRENCH_COLUMN = "french";

function main(translationsFile) {
  const csv = fs.readFileSync(translationsFile);
  const parsedCSV = parse(csv, { columns: true });

  const updatedFrenchTranslations = frenchTranslations;
  parsedCSV.forEach((record) => {
    _.set(updatedFrenchTranslations, record[KEY_COLUMN], record[FRENCH_COLUMN]);
  });

  const prettyPrinted = JSON.stringify(updatedFrenchTranslations, null, 2);
  const frenchTranslationsPath = path.join(__dirname, "..", "src", "locales", "fr_ca.json");
  fs.writeFileSync(frenchTranslationsPath, prettyPrinted);
}

// -- bootstrap --
main(process.argv[2]);
