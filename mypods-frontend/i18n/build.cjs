const { pascalCase, snakeCase } = require('change-case');
const fs = require('fs-extra');
const translations = require('../src/locales/en_us.json');

// For use with i18n's context feature
// https://www.i18next.com/translation-function/context

const CONTEXT_KEYS = [
  // i18n keys
  'zero',
  'one',
  'two',
  'other',
  // scheduled status
  'past',
  'future',
  'unscheduled',
  // -- Service countdown context --
  'default',
  'delivery',
  'pickup',
  'return',
  'dropoff',
  'move',
  'selfdelivery',
  'selfpickup',
  // -- Schedule button context --
  'pickup',
  'dropOff',
  'visitContainer',
  'finalPickup',
  'selfFinalPickup',
  'redelivery',
  'selfInitialDelivery',
  // -- Move Leg Scheduling Price Diff --
  'increase',
  'decrease',
  // -- statement button state, also payment methods --
  'loading',
  'default',
  // -- rental agreement context --
  'local',
  'interFranchise',
  // -- banner context --
  'reviewDocuments',
  'mothAgreements',
  'fnpsAgreements',
  // -- moth inspection card types --
  'fiveDays',
  'hawaii',
  'dontRiskIt',
  // -- moth faqs --
  'incomplete',
  'notInfested',
  'afterSubmit',
  'howToInspect'
];

function Main() {
  convertToTranslationKeys(translations);
}

// -- type helpers --
function isAnObject(value) {
  return typeof value === 'object' && !Array.isArray(value) && value !== null;
}

function remapTheObject(parentObject, parentKey) {
  let memo = {};

  Object.entries(parentObject).forEach(([key, value]) => {
    if (key.includes('array')) {
      const map = Object.entries(value).map(([item_key, item_value]) => {
        return remapTheObject(item_value, `${parentKey}.${key}.${item_key}`);
      });

      memo = {
        ...memo,
        ...{
          [pascalCase(key)]: map
        }
      };
    } else if (isAnObject(value)) {
      memo = {
        ...memo,
        ...{
          [pascalCase(key)]: remapTheObject(value, `${parentKey}.${key}`)
        }
      };
    } else {
      // This allows to use the count functionality of react-i18n
      // see: https://www.i18next.com/translation-function/plurals
      CONTEXT_KEYS.forEach((contextKey) => {
        if (key.includes(`_${contextKey}`)) {
          key = key.replace(`_${contextKey}`, '');
        }
      });
      memo = { ...memo, [snakeCase(key).toUpperCase()]: `${parentKey}.${key}` };
    }
  });

  return memo;
}

// map the json from en_us.json
function convertToTranslationKeys(translations) {
  let updatedTranslationKeys = {};

  Object.entries(translations).forEach(([key, value]) => {
    if (isAnObject(value)) {
      updatedTranslationKeys = {
        ...updatedTranslationKeys,
        ...{ [pascalCase(key)]: remapTheObject(value, key) }
      };
    }
  });
  // export to a file.

  const path = 'src/locales/TranslationKeys.ts';
  const json = JSON.stringify(updatedTranslationKeys, null, 2);
  // remove the quotes from the json.
  const translationKeys = json.replace(/"(.*)"(:)/g, '$1$2');

  const serialized = `
  // generated by \`npm run i18n:build\`; do not edit directly
  // add desired design changes to en_us.json
  export const TranslationKeys = \n${translationKeys};
  `;

  fs.outputFileSync(path, serialized);
}

// -- bootstrap --
Main();

// export the json into translation keys file
// compare output
// update en_us to generate expected translation where it's inconsistent.
