{"compilerOptions": {"target": "ES6", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["node", "vite/client", "jest", "@testing-library/jest-dom"], "sourceMap": true}, "include": ["src", "global.d.ts", "setupTests.ts", "node_modules/vitest/globals.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}